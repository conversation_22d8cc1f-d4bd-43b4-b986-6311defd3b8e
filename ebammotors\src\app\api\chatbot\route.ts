import { NextRequest, NextResponse } from 'next/server';
import { aiService } from '../../../services/AIService';

export async function POST(request: NextRequest) {
  try {
    const { message, context, locale } = await request.json();

    if (!message || typeof message !== 'string') {
      return NextResponse.json(
        { error: 'Message is required and must be a string' },
        { status: 400 }
      );
    }

    // Check if AI service is available
    const isAIAvailable = await aiService.isAvailable();
    
    if (!isAIAvailable) {
      return NextResponse.json({
        success: false,
        error: 'AI service not available',
        useAI: false
      });
    }

    // Generate AI response
    const aiResponse = await aiService.generateResponse(
      message,
      context || '',
      locale || 'en'
    );

    if (aiResponse.error) {
      return NextResponse.json({
        success: false,
        error: aiResponse.error,
        useAI: false
      });
    }

    return NextResponse.json({
      success: true,
      response: aiResponse.text,
      useAI: true,
      provider: aiService.getProviderInfo()
    });

  } catch (error) {
    console.error('Chatbot API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        useAI: false
      },
      { status: 500 }
    );
  }
}

// Health check endpoint
export async function GET() {
  try {
    const isAvailable = await aiService.isAvailable();
    const providerInfo = aiService.getProviderInfo();
    
    return NextResponse.json({
      status: 'ok',
      aiAvailable: isAvailable,
      provider: providerInfo
    });
  } catch (err) {
    console.error('Error in chatbot API:', err);
    return NextResponse.json({
      status: 'error',
      aiAvailable: false,
      error: 'Service check failed'
    });
  }
}
