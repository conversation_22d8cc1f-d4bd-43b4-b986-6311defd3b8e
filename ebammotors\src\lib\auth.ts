import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';

// Security configuration
const SALT_ROUNDS = 12;
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production';
const JWT_EXPIRES_IN = '24h';
const SESSION_TIMEOUT = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

export interface AdminSession {
  id: string;
  isAdmin: boolean;
  createdAt: number;
  expiresAt: number;
  lastActivity: number;
}

// In-memory session store (replace with Redis in production)
const activeSessions = new Map<string, AdminSession>();

/**
 * Hash a password using bcrypt
 */
export async function hashPassword(password: string): Promise<string> {
  try {
    return await bcrypt.hash(password, SALT_ROUNDS);
  } catch (error) {
    console.error('Error hashing password:', error);
    throw new Error('Failed to hash password');
  }
}

/**
 * Verify a password against its hash
 */
export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  try {
    return await bcrypt.compare(password, hash);
  } catch (error) {
    console.error('Error verifying password:', error);
    return false;
  }
}

/**
 * Generate a JWT token for admin authentication
 */
export function generateAdminToken(adminId: string = 'admin'): string {
  try {
    const payload = {
      id: adminId,
      isAdmin: true,
      iat: Math.floor(Date.now() / 1000),
    };
    
    return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
  } catch (error) {
    console.error('Error generating token:', error);
    throw new Error('Failed to generate authentication token');
  }
}

/**
 * Verify and decode a JWT token
 */
export function verifyAdminToken(token: string): { id: string; isAdmin: boolean } | null {
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as any;
    
    if (decoded.isAdmin) {
      return {
        id: decoded.id,
        isAdmin: decoded.isAdmin,
      };
    }
    
    return null;
  } catch (error) {
    // Token is invalid or expired
    return null;
  }
}

/**
 * Create a new admin session
 */
export function createAdminSession(adminId: string = 'admin'): string {
  const sessionId = generateSessionId();
  const now = Date.now();
  
  const session: AdminSession = {
    id: adminId,
    isAdmin: true,
    createdAt: now,
    expiresAt: now + SESSION_TIMEOUT,
    lastActivity: now,
  };
  
  activeSessions.set(sessionId, session);
  
  // Clean up expired sessions
  cleanupExpiredSessions();
  
  return sessionId;
}

/**
 * Validate an admin session
 */
export function validateAdminSession(sessionId: string): AdminSession | null {
  const session = activeSessions.get(sessionId);
  
  if (!session) {
    return null;
  }
  
  const now = Date.now();
  
  // Check if session has expired
  if (now > session.expiresAt) {
    activeSessions.delete(sessionId);
    return null;
  }
  
  // Update last activity
  session.lastActivity = now;
  activeSessions.set(sessionId, session);
  
  return session;
}

/**
 * Destroy an admin session
 */
export function destroyAdminSession(sessionId: string): boolean {
  return activeSessions.delete(sessionId);
}

/**
 * Generate a secure session ID
 */
function generateSessionId(): string {
  return `admin_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
}

/**
 * Clean up expired sessions
 */
function cleanupExpiredSessions(): void {
  const now = Date.now();
  
  for (const [sessionId, session] of activeSessions.entries()) {
    if (now > session.expiresAt) {
      activeSessions.delete(sessionId);
    }
  }
}

/**
 * Get admin password hash from environment
 * In production, this should be stored in a secure database
 */
export function getAdminPasswordHash(): string {
  // For backward compatibility, check if password is already hashed
  const adminPassword = process.env.ADMIN_PASSWORD || 'admin123';
  
  // If it starts with $2a$, $2b$, or $2y$, it's already a bcrypt hash
  if (adminPassword.startsWith('$2a$') || adminPassword.startsWith('$2b$') || adminPassword.startsWith('$2y$')) {
    return adminPassword;
  }
  
  // For development/migration: return the plain password (will be handled in auth route)
  return adminPassword;
}

/**
 * Secure admin authentication
 */
export async function authenticateAdmin(password: string): Promise<{ success: boolean; token?: string; sessionId?: string; message: string }> {
  try {
    const adminPasswordHash = getAdminPasswordHash();
    
    let isValid = false;
    
    // Check if stored password is hashed or plain text
    if (adminPasswordHash.startsWith('$2a$') || adminPasswordHash.startsWith('$2b$') || adminPasswordHash.startsWith('$2y$')) {
      // Password is hashed, use bcrypt comparison
      isValid = await verifyPassword(password, adminPasswordHash);
    } else {
      // Password is plain text (development/migration), use direct comparison
      isValid = password === adminPasswordHash;
    }
    
    if (isValid) {
      const token = generateAdminToken();
      const sessionId = createAdminSession();
      
      return {
        success: true,
        token,
        sessionId,
        message: 'Authentication successful',
      };
    } else {
      return {
        success: false,
        message: 'Invalid credentials',
      };
    }
  } catch (error) {
    console.error('Authentication error:', error);
    return {
      success: false,
      message: 'Authentication failed',
    };
  }
}

/**
 * Middleware to verify admin authentication
 */
export function verifyAdminAuth(authHeader: string | null, sessionId?: string): { isValid: boolean; adminId?: string; message: string } {
  // Check JWT token
  if (authHeader && authHeader.startsWith('Bearer ')) {
    const token = authHeader.substring(7);
    const decoded = verifyAdminToken(token);
    
    if (decoded) {
      return {
        isValid: true,
        adminId: decoded.id,
        message: 'Token authentication successful',
      };
    }
  }
  
  // Check session ID
  if (sessionId) {
    const session = validateAdminSession(sessionId);
    
    if (session) {
      return {
        isValid: true,
        adminId: session.id,
        message: 'Session authentication successful',
      };
    }
  }
  
  return {
    isValid: false,
    message: 'Authentication required',
  };
}

/**
 * Rate limiting for authentication attempts
 */
const authAttempts = new Map<string, { count: number; lastAttempt: number }>();
const MAX_AUTH_ATTEMPTS = 5;
const AUTH_LOCKOUT_TIME = 15 * 60 * 1000; // 15 minutes

export function checkAuthRateLimit(ip: string): { allowed: boolean; remainingAttempts: number; lockoutTime?: number } {
  const now = Date.now();
  const attempts = authAttempts.get(ip);
  
  if (!attempts) {
    authAttempts.set(ip, { count: 1, lastAttempt: now });
    return { allowed: true, remainingAttempts: MAX_AUTH_ATTEMPTS - 1 };
  }
  
  // Reset if lockout time has passed
  if (now - attempts.lastAttempt > AUTH_LOCKOUT_TIME) {
    authAttempts.set(ip, { count: 1, lastAttempt: now });
    return { allowed: true, remainingAttempts: MAX_AUTH_ATTEMPTS - 1 };
  }
  
  // Check if max attempts exceeded
  if (attempts.count >= MAX_AUTH_ATTEMPTS) {
    const lockoutTime = AUTH_LOCKOUT_TIME - (now - attempts.lastAttempt);
    return { allowed: false, remainingAttempts: 0, lockoutTime };
  }
  
  // Increment attempt count
  attempts.count++;
  attempts.lastAttempt = now;
  authAttempts.set(ip, attempts);
  
  return { allowed: true, remainingAttempts: MAX_AUTH_ATTEMPTS - attempts.count };
}

export function resetAuthRateLimit(ip: string): void {
  authAttempts.delete(ip);
}
