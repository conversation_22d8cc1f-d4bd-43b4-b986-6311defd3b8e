import { NextResponse } from 'next/server';
import { generateStockFromFileSystem } from '@/app/[locale]/stock/utils/stockGenerator';

export async function GET() {
  try {
    const stockItems = await generateStockFromFileSystem();
    return NextResponse.json(stockItems);
  } catch (error) {
    console.error('Error fetching stock data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch stock data' },
      { status: 500 }
    );
  }
}
