// API endpoints for car inventory management
import { NextRequest, NextResponse } from 'next/server';
import {
  getAllCars,
  getCarById,
  getCarByCarId,
  createCar,
  updateCar,
  deleteCar,
  bulkDeleteCars
} from '@/lib/database';
import { CarSearchFilters, CreateCarInput, UpdateCarInput } from '@/types/car';
import { getAdminAuth } from '@/lib/adminMiddleware';

// GET - Fetch cars with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Admin authentication for full access (supports both new and legacy methods)
    const adminAuth = getAdminAuth(request);
    const isAdmin = adminAuth.isValid;

    // Get specific car by ID or car_id
    const id = searchParams.get('id');
    const carId = searchParams.get('car_id');

    if (id) {
      const car = await getCarById(id);
      if (!car) {
        return NextResponse.json(
          { success: false, message: 'Car not found' },
          { status: 404 }
        );
      }
      return NextResponse.json({ success: true, car });
    }

    if (carId) {
      const car = await getCarByCarId(carId);
      if (!car) {
        return NextResponse.json(
          { success: false, message: 'Car not found' },
          { status: 404 }
        );
      }
      return NextResponse.json({ success: true, car });
    }

    // Build filters from query parameters
    const filters: CarSearchFilters = {};
    
    if (searchParams.get('make')) filters.make = searchParams.get('make')!;
    if (searchParams.get('model')) filters.model = searchParams.get('model')!;
    if (searchParams.get('year_min')) filters.year_min = parseInt(searchParams.get('year_min')!);
    if (searchParams.get('year_max')) filters.year_max = parseInt(searchParams.get('year_max')!);
    if (searchParams.get('price_min')) filters.price_min = parseInt(searchParams.get('price_min')!);
    if (searchParams.get('price_max')) filters.price_max = parseInt(searchParams.get('price_max')!);
    if (searchParams.get('mileage_max')) filters.mileage_max = parseInt(searchParams.get('mileage_max')!);
    if (searchParams.get('fuel_type')) filters.fuel_type = searchParams.get('fuel_type')!;
    if (searchParams.get('transmission')) filters.transmission = searchParams.get('transmission')!;
    if (searchParams.get('body_condition')) filters.body_condition = searchParams.get('body_condition')!;
    if (searchParams.get('status')) filters.status = searchParams.get('status')!;
    if (searchParams.get('is_featured')) filters.is_featured = searchParams.get('is_featured') === 'true';
    if (searchParams.get('is_recently_added')) filters.is_recently_added = searchParams.get('is_recently_added') === 'true';
    if (searchParams.get('search')) filters.search_query = searchParams.get('search')!;

    // For non-admin users, only show available cars
    if (!isAdmin) {
      filters.status = 'Available';
    }

    // Pagination
    const page = parseInt(searchParams.get('page') || '1');
    const perPage = parseInt(searchParams.get('per_page') || '50');

    const result = await getAllCars(filters, page, perPage);
    
    return NextResponse.json({
      success: true,
      ...result
    });

  } catch (error) {
    console.error('Error fetching cars:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch cars' },
      { status: 500 }
    );
  }
}

// POST - Create new car
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { car } = body;

    // Admin authentication (supports both new and legacy methods)
    const adminAuth = getAdminAuth(request, body);
    if (!adminAuth.isValid) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (!car) {
      return NextResponse.json(
        { success: false, message: 'Car data is required' },
        { status: 400 }
      );
    }

    // Validate required fields
    const requiredFields = ['car_id', 'make', 'model', 'year', 'price'];
    const missingFields = requiredFields.filter(field => !car[field]);
    
    if (missingFields.length > 0) {
      return NextResponse.json(
        { success: false, message: `Missing required fields: ${missingFields.join(', ')}` },
        { status: 400 }
      );
    }

    const carInput: CreateCarInput = {
      ...car,
      import_source: 'Manual',
    };

    const createdCar = await createCar(carInput);

    return NextResponse.json({
      success: true,
      message: 'Car created successfully',
      car: createdCar
    });

  } catch (error) {
    console.error('Error creating car:', error);
    
    if (error instanceof Error && error.message.includes('already exists')) {
      return NextResponse.json(
        { success: false, message: error.message },
        { status: 409 }
      );
    }

    return NextResponse.json(
      { success: false, message: 'Failed to create car' },
      { status: 500 }
    );
  }
}

// PUT - Update existing car
export async function PUT(request: NextRequest) {
  try {
    const { adminKey, id, updates } = await request.json();

    // Admin authentication
    const validAdminKey = process.env.ADMIN_PASSWORD || 'admin123';
    if (adminKey !== validAdminKey) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (!id || !updates) {
      return NextResponse.json(
        { success: false, message: 'Car ID and updates are required' },
        { status: 400 }
      );
    }

    const updatedCar = await updateCar(id, updates as UpdateCarInput);

    if (!updatedCar) {
      return NextResponse.json(
        { success: false, message: 'Car not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Car updated successfully',
      car: updatedCar
    });

  } catch (error) {
    console.error('Error updating car:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to update car' },
      { status: 500 }
    );
  }
}

// DELETE - Delete car(s)
export async function DELETE(request: NextRequest) {
  try {
    const { adminKey, id, ids } = await request.json();

    // Admin authentication
    const validAdminKey = process.env.ADMIN_PASSWORD || 'admin123';
    if (adminKey !== validAdminKey) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (ids && Array.isArray(ids)) {
      // Bulk delete
      const deletedCount = await bulkDeleteCars(ids);
      
      return NextResponse.json({
        success: true,
        message: `Successfully deleted ${deletedCount} cars`,
        deleted_count: deletedCount
      });
    } else if (id) {
      // Single delete
      const deleted = await deleteCar(id);
      
      if (!deleted) {
        return NextResponse.json(
          { success: false, message: 'Car not found' },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        message: 'Car deleted successfully'
      });
    } else {
      return NextResponse.json(
        { success: false, message: 'Car ID or IDs are required' },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Error deleting car(s):', error);
    return NextResponse.json(
      { success: false, message: 'Failed to delete car(s)' },
      { status: 500 }
    );
  }
}
