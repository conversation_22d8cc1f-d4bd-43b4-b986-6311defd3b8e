#!/usr/bin/env tsx
/**
 * Migration script to transfer existing car data from file system to database
 * 
 * This script:
 * 1. Reads existing car data from the file system structure
 * 2. Converts it to the new database format
 * 3. Imports all cars into the PostgreSQL database
 * 4. Provides detailed migration report
 * 
 * Usage:
 * npm run migrate-cars
 * or
 * npx tsx scripts/migrate-to-database.ts
 */

import { promises as fs } from 'fs';
import path from 'path';
import { createCar } from '../src/lib/database';
import { CreateCarInput } from '../src/types/car';
import { CAR_MODEL_CONFIGS } from '../src/lib/pricingConfig';

// Migration configuration
const MIGRATION_CONFIG = {
  batchId: `migration_${Date.now()}`,
  importSource: 'File System Migration',
  dryRun: process.argv.includes('--dry-run'),
  verbose: process.argv.includes('--verbose'),
};

interface MigrationStats {
  totalFolders: number;
  totalCars: number;
  successfulMigrations: number;
  failedMigrations: number;
  errors: string[];
  migratedCars: Array<{
    car_id: string;
    title: string;
    make: string;
    model: string;
    year: number;
    price: number;
  }>;
}

/**
 * Check if a file is an image
 */
function isImageFile(filename: string): boolean {
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
  return imageExtensions.some(ext => filename.toLowerCase().endsWith(ext));
}

/**
 * Extract year from folder name
 */
function extractYearFromFolderName(folderName: string): number {
  const yearMatch = folderName.match(/(\d{4})/);
  return yearMatch ? parseInt(yearMatch[1]) : 2010; // Default to 2010 if no year found
}

/**
 * Generate deterministic mileage based on folder name
 */
function generateDeterministicMileage(folderName: string): number {
  let hash = 0;
  for (let i = 0; i < folderName.length; i++) {
    const char = folderName.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  
  // Generate mileage between 30,000 and 150,000 km
  const minMileage = 30000;
  const maxMileage = 150000;
  return minMileage + (Math.abs(hash) % (maxMileage - minMileage));
}

/**
 * Generate body condition based on folder name
 */
function generateBodyCondition(folderName: string): string {
  const conditions = ['Excellent', 'Good', 'Fair', 'Needs Work'];
  const hash = folderName.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
  return conditions[hash % conditions.length];
}

/**
 * Get adjusted car price based on model and year
 */
function getAdjustedCarPrice(modelFolder: string, year: number): number {
  const config = CAR_MODEL_CONFIGS[modelFolder];
  if (!config) return 300000; // Default price

  let adjustedPrice = config.basePrice;
  
  // Adjust price based on year
  if (year <= 2010) adjustedPrice -= 5000;
  if (year >= 2012) adjustedPrice += 5000;
  if (year >= 2015) adjustedPrice += 10000;
  if (year >= 2018) adjustedPrice += 20000;
  
  return adjustedPrice;
}

/**
 * Convert file system car data to database format
 */
function convertToCarInput(
  modelFolder: string,
  carFolder: string,
  imageFiles: string[],
  config: any
): CreateCarInput {
  const year = extractYearFromFolderName(carFolder);
  const mileage = generateDeterministicMileage(carFolder);
  const bodyCondition = generateBodyCondition(carFolder);
  const adjustedPrice = getAdjustedCarPrice(modelFolder, year);

  // Generate all image paths
  const allImages = imageFiles.map(file =>
    `/car-models/${modelFolder}/${carFolder}/${file}`
  );

  // Create car input
  const carInput: CreateCarInput = {
    car_id: carFolder,
    make: config.name.split(' ')[0], // Extract make (e.g., "Toyota" from "Toyota Voxy")
    model: config.name.split(' ').slice(1).join(' '), // Extract model (e.g., "Voxy" from "Toyota Voxy")
    year,
    title: `${config.name} ${year}`,
    price: adjustedPrice,
    currency: 'JPY',
    mileage,
    fuel_type: config.fuelType,
    transmission: config.transmission,
    seats: parseInt(config.seats.replace('-seater', '')),
    doors: 4, // Default to 4 doors
    body_type: 'Van', // Most cars in the system are vans
    body_condition: bodyCondition,
    interior_condition: bodyCondition,
    main_image: allImages[0], // First image as main image
    images: allImages,
    image_folder: `/car-models/${modelFolder}/${carFolder}`,
    specs: [
      config.seats,
      config.transmission,
      `${Math.floor(mileage / 1000)}k km`,
      config.fuelType
    ],
    status: carFolder.length % 10 === 0 ? 'Reserved' : 'Available', // Deterministic status
    stock_quantity: 1,
    location: 'Japan',
    description: `Well-maintained ${config.name} with ${Math.floor(mileage / 1000)}k km. ${bodyCondition} condition.`,
    is_featured: carFolder.includes('A'), // Cars with 'A' in folder name are featured
    import_batch_id: MIGRATION_CONFIG.batchId,
    import_source: MIGRATION_CONFIG.importSource,
    import_notes: `Migrated from file system: ${modelFolder}/${carFolder}`,
  };

  return carInput;
}

/**
 * Migrate cars from file system to database
 */
async function migrateCarsToDatabase(): Promise<MigrationStats> {
  const stats: MigrationStats = {
    totalFolders: 0,
    totalCars: 0,
    successfulMigrations: 0,
    failedMigrations: 0,
    errors: [],
    migratedCars: [],
  };

  const carModelsPath = path.join(process.cwd(), 'public', 'car-models');

  try {
    console.log('🚀 Starting car migration from file system to database...');
    console.log(`📁 Scanning directory: ${carModelsPath}`);
    
    if (MIGRATION_CONFIG.dryRun) {
      console.log('🔍 DRY RUN MODE - No data will be written to database');
    }

    // Check if car models directory exists
    try {
      await fs.access(carModelsPath);
    } catch (error) {
      throw new Error(`Car models directory not found: ${carModelsPath}`);
    }

    // Read model folders
    const modelFolders = await fs.readdir(carModelsPath);
    console.log(`📂 Found ${modelFolders.length} model folders`);

    for (const modelFolder of modelFolders) {
      const modelPath = path.join(carModelsPath, modelFolder);
      
      // Check if it's a directory
      const modelStat = await fs.stat(modelPath);
      if (!modelStat.isDirectory()) continue;

      // Get model configuration
      const config = CAR_MODEL_CONFIGS[modelFolder];
      if (!config) {
        console.log(`⚠️  No configuration found for model: ${modelFolder}`);
        continue;
      }

      console.log(`\n🚗 Processing model: ${config.name}`);
      stats.totalFolders++;

      // Get all car folders within this model
      const carFolders = await fs.readdir(modelPath);
      
      for (const carFolder of carFolders) {
        const carPath = path.join(modelPath, carFolder);
        
        // Check if it's a directory
        const carStat = await fs.stat(carPath);
        if (!carStat.isDirectory()) continue;

        try {
          // Get all images in this car folder
          const allFiles = await fs.readdir(carPath);
          const imageFiles = allFiles.filter(isImageFile).sort();

          if (imageFiles.length === 0) {
            console.log(`  ⚠️  No images found in ${carFolder}, skipping`);
            continue;
          }

          stats.totalCars++;

          if (MIGRATION_CONFIG.verbose) {
            console.log(`  📸 Processing ${carFolder} (${imageFiles.length} images)`);
          }

          // Convert to car input format
          const carInput = convertToCarInput(modelFolder, carFolder, imageFiles, config);

          if (!MIGRATION_CONFIG.dryRun) {
            // Create car in database
            const createdCar = await createCar(carInput);
            
            stats.migratedCars.push({
              car_id: createdCar.car_id,
              title: createdCar.title,
              make: createdCar.make,
              model: createdCar.model,
              year: createdCar.year,
              price: createdCar.price,
            });

            if (MIGRATION_CONFIG.verbose) {
              console.log(`  ✅ Migrated: ${createdCar.title} (¥${createdCar.price.toLocaleString()})`);
            }
          } else {
            // Dry run - just log what would be migrated
            console.log(`  🔍 Would migrate: ${carInput.title} (¥${carInput.price.toLocaleString()})`);
          }

          stats.successfulMigrations++;

        } catch (error) {
          const errorMessage = `Failed to migrate ${modelFolder}/${carFolder}: ${
            error instanceof Error ? error.message : 'Unknown error'
          }`;
          
          stats.errors.push(errorMessage);
          stats.failedMigrations++;
          
          console.log(`  ❌ ${errorMessage}`);
        }
      }
    }

    return stats;

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    stats.errors.push(`Migration failed: ${errorMessage}`);
    throw error;
  }
}

/**
 * Print migration report
 */
function printMigrationReport(stats: MigrationStats) {
  console.log('\n' + '='.repeat(60));
  console.log('📊 MIGRATION REPORT');
  console.log('='.repeat(60));

  console.log(`📁 Model folders processed: ${stats.totalFolders}`);
  console.log(`🚗 Total cars found: ${stats.totalCars}`);
  console.log(`✅ Successful migrations: ${stats.successfulMigrations}`);
  console.log(`❌ Failed migrations: ${stats.failedMigrations}`);

  if (stats.successfulMigrations > 0) {
    const successRate = ((stats.successfulMigrations / stats.totalCars) * 100).toFixed(1);
    console.log(`📈 Success rate: ${successRate}%`);
  }

  if (stats.migratedCars.length > 0) {
    console.log('\n🚗 Successfully migrated cars:');
    const makeGroups = stats.migratedCars.reduce((acc, car) => {
      if (!acc[car.make]) acc[car.make] = [];
      acc[car.make].push(car);
      return acc;
    }, {} as Record<string, typeof stats.migratedCars>);

    Object.entries(makeGroups).forEach(([make, cars]) => {
      console.log(`  ${make}: ${cars.length} cars`);
      const totalValue = cars.reduce((sum, car) => sum + car.price, 0);
      console.log(`    Total value: ¥${totalValue.toLocaleString()}`);
    });
  }

  if (stats.errors.length > 0) {
    console.log('\n❌ Errors encountered:');
    stats.errors.forEach((error, index) => {
      console.log(`  ${index + 1}. ${error}`);
    });
  }

  console.log('\n' + '='.repeat(60));

  if (MIGRATION_CONFIG.dryRun) {
    console.log('🔍 This was a DRY RUN - no data was written to the database');
    console.log('💡 Run without --dry-run flag to perform actual migration');
  } else {
    console.log('🎉 Migration completed!');
    console.log('💡 You can now use the admin panel to manage your car inventory');
  }
}

/**
 * Main migration function
 */
async function main() {
  try {
    const stats = await migrateCarsToDatabase();
    printMigrationReport(stats);

    process.exit(stats.failedMigrations > 0 ? 1 : 0);
  } catch (error) {
    console.error('\n💥 Migration failed:', error);
    process.exit(1);
  }
}

// Run migration if this script is executed directly
if (require.main === module) {
  main();
}
