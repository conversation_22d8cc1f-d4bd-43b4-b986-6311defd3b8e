import { NextRequest, NextResponse } from 'next/server';
import { emailService, ContactFormData } from '@/lib/resendService';
import {
  checkRateLimit,
  getClientIP,
  sanitizeInput,
  isValidEmail,
  isValidPhone,
  containsSuspiciousContent,
  getSecurityHeaders,
  logSecurityEvent
} from '@/lib/security';
import { startPerformanceMonitoring, logError } from '@/lib/monitoring';

export async function POST(request: NextRequest) {
  const monitor = startPerformanceMonitoring(request);

  try {
    const clientIP = getClientIP(request);

    // Rate limiting
    const rateLimit = checkRateLimit(clientIP, 'contact');
    if (!rateLimit.allowed) {
      monitor.finish(429, 'Rate limit exceeded');
      return NextResponse.json(
        {
          success: false,
          message: `Too many contact form submissions. Please try again in ${Math.ceil((rateLimit.resetTime - Date.now()) / 60000)} minutes.`,
          resetTime: rateLimit.resetTime
        },
        { status: 429 }
      );
    }

    const formData = await request.formData();

    // Extract and validate form fields
    const rawName = formData.get('name') as string;
    const rawEmail = formData.get('email') as string;
    const rawSubject = formData.get('subject') as string;
    const rawMessage = formData.get('message') as string;
    const rawPhone = formData.get('phone') as string;
    const rawCompany = formData.get('company') as string;

    // Basic validation
    if (!rawName || !rawEmail || !rawSubject || !rawMessage) {
      monitor.finish(400, 'Missing required fields');
      return NextResponse.json(
        { success: false, message: 'Name, email, subject, and message are required' },
        { status: 400 }
      );
    }

    // Sanitize inputs
    const name = sanitizeInput(rawName);
    const email = sanitizeInput(rawEmail);
    const subject = sanitizeInput(rawSubject);
    const message = sanitizeInput(rawMessage);
    const phone = rawPhone ? sanitizeInput(rawPhone) : '';
    const company = rawCompany ? sanitizeInput(rawCompany) : '';

    // Enhanced validation
    if (!isValidEmail(email)) {
      monitor.finish(400, 'Invalid email format');
      return NextResponse.json(
        { success: false, message: 'Please provide a valid email address' },
        { status: 400 }
      );
    }

    if (phone && !isValidPhone(phone)) {
      monitor.finish(400, 'Invalid phone format');
      return NextResponse.json(
        { success: false, message: 'Please provide a valid phone number' },
        { status: 400 }
      );
    }

    // Check for suspicious content
    const allContent = `${name} ${email} ${subject} ${message} ${company}`;
    if (containsSuspiciousContent(allContent)) {
      logSecurityEvent('Suspicious content in contact form', { name, email, subject }, request);
      monitor.finish(400, 'Suspicious content detected');
      return NextResponse.json(
        { success: false, message: 'Your submission contains invalid content. Please review and try again.' },
        { status: 400 }
      );
    }

    // Prepare contact form data
    const contactData: ContactFormData = {
      name: name.trim(),
      email: email.trim().toLowerCase(),
      subject: subject.trim(),
      message: message.trim(),
      submissionDate: new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        timeZone: 'UTC'
      })
    };

    // Add optional fields to message if provided
    let enhancedMessage = contactData.message;
    if (phone) {
      enhancedMessage += `\n\nPhone: ${phone.trim()}`;
    }
    if (company) {
      enhancedMessage += `\nCompany: ${company.trim()}`;
    }
    contactData.message = enhancedMessage;

    // Send email notifications
    const emailResult = await emailService.sendContactFormNotification(contactData);

    if (!emailResult.success) {
      logError('Failed to send contact form emails', {
        endpoint: '/api/contact',
        method: 'POST',
        request,
        additionalContext: { emailError: emailResult.error, contactData: { name, email, subject } }
      });
      return NextResponse.json(
        { success: false, message: 'Failed to send notification emails' },
        { status: 500 }
      );
    }

    // Log successful submission
    console.log('Contact form submitted successfully:', {
      name: contactData.name,
      email: contactData.email,
      subject: contactData.subject,
      timestamp: contactData.submissionDate
    });

    monitor.finish(200);

    const response = NextResponse.json({
      success: true,
      message: 'Thank you for your message! We will respond within 24 hours.',
      data: {
        submissionId: `contact_${Date.now()}`,
        submittedAt: contactData.submissionDate
      }
    });

    // Add security headers
    const securityHeaders = getSecurityHeaders();
    Object.entries(securityHeaders).forEach(([key, value]) => {
      response.headers.set(key, value);
    });

    return response;

  } catch (error) {
    logError(error instanceof Error ? error : 'Unknown error', {
      endpoint: '/api/contact',
      method: 'POST',
      request,
      additionalContext: { action: 'contact_form_submission' }
    });

    monitor.finish(500, error instanceof Error ? error.message : 'Unknown error');

    const response = NextResponse.json(
      { success: false, message: 'Failed to process contact form submission' },
      { status: 500 }
    );

    // Add security headers even for errors
    const securityHeaders = getSecurityHeaders();
    Object.entries(securityHeaders).forEach(([key, value]) => {
      response.headers.set(key, value);
    });

    return response;
  }
}

// Handle GET requests (for form validation or status checks)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    if (action === 'validate-email') {
      const email = searchParams.get('email');
      if (!email) {
        return NextResponse.json(
          { success: false, message: 'Email parameter is required' },
          { status: 400 }
        );
      }

      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      const isValid = emailRegex.test(email);

      return NextResponse.json({
        success: true,
        data: {
          email,
          isValid,
          message: isValid ? 'Email is valid' : 'Please provide a valid email address'
        }
      });
    }

    // Default response for GET requests
    return NextResponse.json({
      success: true,
      message: 'Contact form API is ready',
      endpoints: {
        submit: 'POST /',
        validateEmail: 'GET /?action=validate-email&email=<EMAIL>'
      }
    });

  } catch (error) {
    console.error('Error handling contact form GET request:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to process request' },
      { status: 500 }
    );
  }
}
