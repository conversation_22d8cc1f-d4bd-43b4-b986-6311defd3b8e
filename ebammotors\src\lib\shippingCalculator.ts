import { ShippingCalculation, ShippingMethod } from '@/types/payment';
import { SHIPPING_METHODS, VEHICLE_WEIGHTS, calculateShippingCost, getEstimatedDeliveryDate } from './paymentConfig';

export interface VehicleInfo {
  category: string;
  year: number;
  weight?: number;
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
}

export interface ShippingDestination {
  country: string;
  city: string;
  port?: string;
}

/**
 * Calculate shipping options for a vehicle
 */
export function calculateShippingOptions(
  vehicle: VehicleInfo,
  destination: ShippingDestination
): ShippingCalculation[] {
  const calculations: ShippingCalculation[] = [];

  for (const method of SHIPPING_METHODS) {
    const weight = vehicle.weight || VEHICLE_WEIGHTS[vehicle.category.toLowerCase()] || VEHICLE_WEIGHTS.default;
    
    // Check if vehicle is within weight limits
    if (method.maxWeight && weight > method.maxWeight) {
      continue;
    }

    const cost = calculateShippingCost(method.id, vehicle.category, destination.country);
    const estimatedDelivery = getEstimatedDeliveryDate(method.id);

    calculations.push({
      method,
      cost,
      estimatedDelivery,
      weight,
      dimensions: vehicle.dimensions,
    });
  }

  // Sort by cost (cheapest first)
  return calculations.sort((a, b) => a.cost - b.cost);
}

/**
 * Get recommended shipping method based on customer preferences
 */
export function getRecommendedShipping(
  calculations: ShippingCalculation[],
  preference: 'cost' | 'speed' | 'balanced' = 'balanced'
): ShippingCalculation | null {
  if (calculations.length === 0) return null;

  switch (preference) {
    case 'cost':
      return calculations[0]; // Already sorted by cost

    case 'speed':
      return calculations.reduce((fastest, current) => 
        current.method.estimatedDays < fastest.method.estimatedDays ? current : fastest
      );

    case 'balanced':
      // Score based on cost and speed (lower is better)
      const scored = calculations.map(calc => ({
        calculation: calc,
        score: (calc.cost / 100000) + (calc.method.estimatedDays / 10), // Normalize and combine
      }));
      
      scored.sort((a, b) => a.score - b.score);
      return scored[0].calculation;

    default:
      return calculations[0];
  }
}

/**
 * Calculate total shipping cost including insurance and handling
 */
export function calculateTotalShippingCost(
  baseShippingCost: number,
  vehicleValue: number,
  includeInsurance: boolean = true,
  includeHandling: boolean = true
): {
  baseShipping: number;
  insurance: number;
  handling: number;
  total: number;
} {
  const insurance = includeInsurance ? Math.max(vehicleValue * 0.02, 10000) : 0; // 2% of vehicle value, min ¥10,000
  const handling = includeHandling ? 25000 : 0; // ¥25,000 handling fee

  return {
    baseShipping: baseShippingCost,
    insurance: Math.round(insurance),
    handling,
    total: Math.round(baseShippingCost + insurance + handling),
  };
}

/**
 * Get shipping timeline with milestones
 */
export function getShippingTimeline(shippingMethod: ShippingMethod): Array<{
  stage: string;
  description: string;
  estimatedDays: number;
}> {
  const baseTimeline = [
    {
      stage: 'Order Processing',
      description: 'Vehicle inspection and export documentation',
      estimatedDays: 3,
    },
    {
      stage: 'Port Preparation',
      description: 'Vehicle preparation and loading at port',
      estimatedDays: 5,
    },
  ];

  if (shippingMethod.id.includes('air')) {
    return [
      ...baseTimeline,
      {
        stage: 'Air Transport',
        description: 'Flight to destination airport',
        estimatedDays: 2,
      },
      {
        stage: 'Customs Clearance',
        description: 'Import customs and documentation',
        estimatedDays: 3,
      },
      {
        stage: 'Final Delivery',
        description: 'Transport to final destination',
        estimatedDays: 2,
      },
    ];
  } else {
    return [
      ...baseTimeline,
      {
        stage: 'Sea Transport',
        description: 'Shipping to destination port',
        estimatedDays: shippingMethod.estimatedDays - 15,
      },
      {
        stage: 'Port Arrival',
        description: 'Arrival at destination port',
        estimatedDays: shippingMethod.estimatedDays - 10,
      },
      {
        stage: 'Customs Clearance',
        description: 'Import customs and documentation',
        estimatedDays: shippingMethod.estimatedDays - 5,
      },
      {
        stage: 'Final Delivery',
        description: 'Transport to final destination',
        estimatedDays: shippingMethod.estimatedDays,
      },
    ];
  }
}

/**
 * Validate shipping destination
 */
export function validateShippingDestination(destination: ShippingDestination): {
  valid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (!destination.country || destination.country.trim() === '') {
    errors.push('Country is required');
  }

  if (!destination.city || destination.city.trim() === '') {
    errors.push('City is required');
  }

  // Check if we ship to this country
  const supportedCountries = ['ghana', 'nigeria', 'kenya', 'south africa', 'ivory coast'];
  if (!supportedCountries.includes(destination.country.toLowerCase())) {
    errors.push(`Shipping to ${destination.country} is not currently available`);
  }

  return {
    valid: errors.length === 0,
    errors,
  };
}

/**
 * Get shipping restrictions for a destination
 */
export function getShippingRestrictions(destination: ShippingDestination): string[] {
  const restrictions: string[] = [];

  switch (destination.country.toLowerCase()) {
    case 'ghana':
      restrictions.push('Vehicles must be less than 10 years old');
      restrictions.push('Right-hand drive vehicles only');
      break;
    
    case 'nigeria':
      restrictions.push('Vehicles must be less than 15 years old');
      restrictions.push('Additional import duties may apply');
      break;
    
    case 'kenya':
      restrictions.push('Vehicles must be less than 8 years old');
      restrictions.push('Pre-shipment inspection required');
      break;
    
    default:
      restrictions.push('Please contact us for specific import requirements');
  }

  return restrictions;
}

/**
 * Calculate estimated delivery date range
 */
export function getDeliveryDateRange(shippingMethod: ShippingMethod): {
  earliest: string;
  latest: string;
} {
  const today = new Date();
  const earliest = new Date(today);
  const latest = new Date(today);

  // Add buffer days (±3 days for sea freight, ±1 day for air freight)
  const buffer = shippingMethod.id.includes('air') ? 1 : 3;

  earliest.setDate(earliest.getDate() + shippingMethod.estimatedDays - buffer);
  latest.setDate(latest.getDate() + shippingMethod.estimatedDays + buffer);

  return {
    earliest: earliest.toISOString().split('T')[0],
    latest: latest.toISOString().split('T')[0],
  };
}
