// CSV Import functionality for bulk car uploads
import { sql } from '@vercel/postgres';
import { Car, CreateCarInput, CSVImportRow, CSVImport, CSVImportResult } from '@/types/car';
import { createCar, generateCarSlug } from './database';
import { v4 as uuidv4 } from 'uuid';

/**
 * Parse CSV content and validate rows
 */
export function parseCSVContent(csvContent: string): CSVImportRow[] {
  const lines = csvContent.trim().split('\n');
  if (lines.length < 2) {
    throw new Error('CSV file must contain at least a header row and one data row');
  }

  const headers = lines[0].split(',').map(h => h.trim().toLowerCase());
  const rows: CSVImportRow[] = [];

  // Required headers
  const requiredHeaders = ['car_id', 'make', 'model', 'year', 'price'];
  const missingHeaders = requiredHeaders.filter(h => !headers.includes(h));
  
  if (missingHeaders.length > 0) {
    throw new Error(`Missing required headers: ${missingHeaders.join(', ')}`);
  }

  for (let i = 1; i < lines.length; i++) {
    const values = lines[i].split(',').map(v => v.trim());
    
    if (values.length !== headers.length) {
      throw new Error(`Row ${i + 1}: Column count mismatch. Expected ${headers.length}, got ${values.length}`);
    }

    const row: any = {};
    headers.forEach((header, index) => {
      row[header] = values[index];
    });

    rows.push(row as CSVImportRow);
  }

  return rows;
}

/**
 * Validate a single CSV row
 */
export function validateCSVRow(row: CSVImportRow, rowIndex: number): string[] {
  const errors: string[] = [];

  // Required fields validation
  if (!row.car_id || row.car_id.trim() === '') {
    errors.push(`Row ${rowIndex}: car_id is required`);
  }

  if (!row.make || row.make.trim() === '') {
    errors.push(`Row ${rowIndex}: make is required`);
  }

  if (!row.model || row.model.trim() === '') {
    errors.push(`Row ${rowIndex}: model is required`);
  }

  // Year validation
  const year = typeof row.year === 'string' ? parseInt(row.year) : row.year;
  if (!year || isNaN(year) || year < 1990 || year > 2030) {
    errors.push(`Row ${rowIndex}: year must be a valid number between 1990 and 2030`);
  }

  // Price validation
  const price = typeof row.price === 'string' ? parseFloat(row.price.replace(/[,¥$]/g, '')) : row.price;
  if (!price || isNaN(price) || price <= 0) {
    errors.push(`Row ${rowIndex}: price must be a positive number`);
  }

  // Optional numeric field validations
  if (row.mileage) {
    const mileage = typeof row.mileage === 'string' ? parseInt(row.mileage) : row.mileage;
    if (isNaN(mileage) || mileage < 0) {
      errors.push(`Row ${rowIndex}: mileage must be a non-negative number`);
    }
  }

  if (row.seats) {
    const seats = typeof row.seats === 'string' ? parseInt(row.seats) : row.seats;
    if (isNaN(seats) || seats < 1 || seats > 50) {
      errors.push(`Row ${rowIndex}: seats must be between 1 and 50`);
    }
  }

  if (row.doors) {
    const doors = typeof row.doors === 'string' ? parseInt(row.doors) : row.doors;
    if (isNaN(doors) || doors < 1 || doors > 10) {
      errors.push(`Row ${rowIndex}: doors must be between 1 and 10`);
    }
  }

  if (row.stock_quantity) {
    const stockQuantity = typeof row.stock_quantity === 'string' ? parseInt(row.stock_quantity) : row.stock_quantity;
    if (isNaN(stockQuantity) || stockQuantity < 0) {
      errors.push(`Row ${rowIndex}: stock_quantity must be a non-negative number`);
    }
  }

  // Boolean field validation
  if (row.is_featured && typeof row.is_featured === 'string') {
    const validBooleans = ['true', 'false', '1', '0', 'yes', 'no'];
    if (!validBooleans.includes(row.is_featured.toLowerCase())) {
      errors.push(`Row ${rowIndex}: is_featured must be true/false, 1/0, or yes/no`);
    }
  }

  return errors;
}

/**
 * Convert CSV row to CreateCarInput
 */
export function csvRowToCarInput(row: CSVImportRow, batchId: string): CreateCarInput {
  // Parse numeric fields
  const year = typeof row.year === 'string' ? parseInt(row.year) : row.year;
  const price = typeof row.price === 'string' ? parseFloat(row.price.replace(/[,¥$]/g, '')) : row.price;
  const originalPrice = row.original_price ? 
    (typeof row.original_price === 'string' ? parseFloat(row.original_price.replace(/[,¥$]/g, '')) : row.original_price) : 
    undefined;
  const mileage = row.mileage ? 
    (typeof row.mileage === 'string' ? parseInt(row.mileage) : row.mileage) : 
    undefined;
  const seats = row.seats ? 
    (typeof row.seats === 'string' ? parseInt(row.seats) : row.seats) : 
    undefined;
  const doors = row.doors ? 
    (typeof row.doors === 'string' ? parseInt(row.doors) : row.doors) : 
    undefined;
  const stockQuantity = row.stock_quantity ? 
    (typeof row.stock_quantity === 'string' ? parseInt(row.stock_quantity) : row.stock_quantity) : 
    1;

  // Parse boolean fields
  const isFeatured = row.is_featured ? 
    ['true', '1', 'yes'].includes(String(row.is_featured).toLowerCase()) : 
    false;

  // Parse array fields (comma-separated strings)
  const images = row.images ? row.images.split(',').map(img => img.trim()).filter(img => img) : [];
  const specs = row.specs ? row.specs.split(',').map(spec => spec.trim()).filter(spec => spec) : [];
  const features = row.features ? row.features.split(',').map(feature => feature.trim()).filter(feature => feature) : [];

  // Generate title if not provided
  const title = `${row.make} ${row.model} ${year}`;

  return {
    car_id: row.car_id.trim(),
    make: row.make.trim(),
    model: row.model.trim(),
    year,
    title,
    price,
    original_price: originalPrice,
    currency: 'JPY',
    mileage,
    fuel_type: row.fuel_type?.trim(),
    transmission: row.transmission?.trim(),
    engine_size: row.engine_size?.trim(),
    drive_type: row.drive_type?.trim(),
    seats,
    doors,
    body_type: row.body_type?.trim(),
    body_condition: row.body_condition?.trim() || 'Good',
    interior_condition: row.interior_condition?.trim() || 'Good',
    exterior_color: row.exterior_color?.trim(),
    interior_color: row.interior_color?.trim(),
    main_image: row.main_image?.trim(),
    images,
    image_folder: row.image_folder?.trim(),
    specs,
    features,
    status: row.status?.trim() || 'Available',
    stock_quantity: stockQuantity,
    location: row.location?.trim() || 'Japan',
    description: row.description?.trim(),
    is_featured: isFeatured,
    import_batch_id: batchId,
    import_source: 'CSV',
  };
}

/**
 * Create CSV import record
 */
export async function createCSVImport(
  batchId: string,
  filename: string,
  totalRows: number,
  importedBy: string
): Promise<CSVImport> {
  try {
    const { rows } = await sql`
      INSERT INTO csv_imports (
        batch_id, filename, total_rows, imported_by, status
      ) VALUES (
        ${batchId}, ${filename}, ${totalRows}, ${importedBy}, 'processing'
      )
      RETURNING *
    `;

    return {
      id: rows[0].id,
      batch_id: rows[0].batch_id,
      filename: rows[0].filename,
      total_rows: rows[0].total_rows,
      successful_imports: rows[0].successful_imports,
      failed_imports: rows[0].failed_imports,
      status: rows[0].status,
      error_log: rows[0].error_log || [],
      import_summary: rows[0].import_summary || {},
      imported_by: rows[0].imported_by,
      started_at: rows[0].started_at,
      completed_at: rows[0].completed_at,
      created_at: rows[0].created_at,
    };
  } catch (error) {
    console.error('Error creating CSV import record:', error);
    throw new Error('Failed to create CSV import record');
  }
}

/**
 * Update CSV import record
 */
export async function updateCSVImport(
  batchId: string,
  updates: {
    successful_imports?: number;
    failed_imports?: number;
    status?: 'processing' | 'completed' | 'failed';
    error_log?: string[];
    import_summary?: Record<string, any>;
  }
): Promise<void> {
  try {
    const updateFields: string[] = [];
    const values: any[] = [];
    let paramIndex = 1;

    if (updates.successful_imports !== undefined) {
      updateFields.push(`successful_imports = $${paramIndex}`);
      values.push(updates.successful_imports);
      paramIndex++;
    }

    if (updates.failed_imports !== undefined) {
      updateFields.push(`failed_imports = $${paramIndex}`);
      values.push(updates.failed_imports);
      paramIndex++;
    }

    if (updates.status) {
      updateFields.push(`status = $${paramIndex}`);
      values.push(updates.status);
      paramIndex++;
    }

    if (updates.error_log) {
      updateFields.push(`error_log = $${paramIndex}`);
      values.push(JSON.stringify(updates.error_log));
      paramIndex++;
    }

    if (updates.import_summary) {
      updateFields.push(`import_summary = $${paramIndex}`);
      values.push(JSON.stringify(updates.import_summary));
      paramIndex++;
    }

    if (updates.status === 'completed' || updates.status === 'failed') {
      updateFields.push(`completed_at = NOW()`);
    }

    values.push(batchId);

    const query = `
      UPDATE csv_imports
      SET ${updateFields.join(', ')}
      WHERE batch_id = $${paramIndex}
    `;

    await sql.query(query, values);
  } catch (error) {
    console.error('Error updating CSV import record:', error);
    throw new Error('Failed to update CSV import record');
  }
}

/**
 * Process bulk CSV import
 */
export async function processBulkCSVImport(
  csvContent: string,
  filename: string,
  importedBy: string
): Promise<CSVImportResult> {
  const batchId = uuidv4();
  const errors: string[] = [];
  const importedCars: Car[] = [];
  let successfulImports = 0;
  let failedImports = 0;

  try {
    // Parse CSV content
    const csvRows = parseCSVContent(csvContent);

    // Create import record
    await createCSVImport(batchId, filename, csvRows.length, importedBy);

    // Process each row
    for (let i = 0; i < csvRows.length; i++) {
      const row = csvRows[i];
      const rowIndex = i + 2; // +2 because CSV has header row and we're 1-indexed

      try {
        // Validate row
        const validationErrors = validateCSVRow(row, rowIndex);
        if (validationErrors.length > 0) {
          errors.push(...validationErrors);
          failedImports++;
          continue;
        }

        // Convert to car input
        const carInput = csvRowToCarInput(row, batchId);

        // Create car in database
        const createdCar = await createCar(carInput);
        importedCars.push(createdCar);
        successfulImports++;

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        errors.push(`Row ${rowIndex}: ${errorMessage}`);
        failedImports++;
      }
    }

    // Update import record with results
    const status = failedImports === 0 ? 'completed' : (successfulImports === 0 ? 'failed' : 'completed');
    await updateCSVImport(batchId, {
      successful_imports: successfulImports,
      failed_imports: failedImports,
      status,
      error_log: errors,
      import_summary: {
        total_processed: csvRows.length,
        successful_imports: successfulImports,
        failed_imports: failedImports,
        success_rate: csvRows.length > 0 ? (successfulImports / csvRows.length * 100).toFixed(2) + '%' : '0%',
        makes_imported: [...new Set(importedCars.map(car => car.make))],
        models_imported: [...new Set(importedCars.map(car => car.model))],
        price_range: importedCars.length > 0 ? {
          min: Math.min(...importedCars.map(car => car.price)),
          max: Math.max(...importedCars.map(car => car.price)),
        } : null,
      },
    });

    return {
      success: successfulImports > 0,
      batch_id: batchId,
      total_rows: csvRows.length,
      successful_imports: successfulImports,
      failed_imports: failedImports,
      errors,
      imported_cars: importedCars,
    };

  } catch (error) {
    // Update import record as failed
    await updateCSVImport(batchId, {
      status: 'failed',
      error_log: [error instanceof Error ? error.message : 'Unknown error'],
    });

    throw error;
  }
}

/**
 * Get CSV import by batch ID
 */
export async function getCSVImportByBatchId(batchId: string): Promise<CSVImport | null> {
  try {
    const { rows } = await sql`
      SELECT * FROM csv_imports WHERE batch_id = ${batchId}
    `;

    if (rows.length === 0) {
      return null;
    }

    const row = rows[0];
    return {
      id: row.id,
      batch_id: row.batch_id,
      filename: row.filename,
      total_rows: row.total_rows,
      successful_imports: row.successful_imports,
      failed_imports: row.failed_imports,
      status: row.status,
      error_log: row.error_log || [],
      import_summary: row.import_summary || {},
      imported_by: row.imported_by,
      started_at: row.started_at,
      completed_at: row.completed_at,
      created_at: row.created_at,
    };
  } catch (error) {
    console.error('Error fetching CSV import:', error);
    throw new Error('Failed to fetch CSV import record');
  }
}

/**
 * Get all CSV imports with pagination
 */
export async function getAllCSVImports(page: number = 1, perPage: number = 20): Promise<{
  imports: CSVImport[];
  total_count: number;
  page: number;
  per_page: number;
  total_pages: number;
}> {
  try {
    const offset = (page - 1) * perPage;

    // Get total count
    const countResult = await sql`SELECT COUNT(*) as total FROM csv_imports`;
    const totalCount = parseInt(countResult.rows[0].total);

    // Get paginated results
    const { rows } = await sql`
      SELECT * FROM csv_imports
      ORDER BY started_at DESC
      LIMIT ${perPage} OFFSET ${offset}
    `;

    const imports = rows.map(row => ({
      id: row.id,
      batch_id: row.batch_id,
      filename: row.filename,
      total_rows: row.total_rows,
      successful_imports: row.successful_imports,
      failed_imports: row.failed_imports,
      status: row.status,
      error_log: row.error_log || [],
      import_summary: row.import_summary || {},
      imported_by: row.imported_by,
      started_at: row.started_at,
      completed_at: row.completed_at,
      created_at: row.created_at,
    }));

    return {
      imports,
      total_count: totalCount,
      page,
      per_page: perPage,
      total_pages: Math.ceil(totalCount / perPage),
    };
  } catch (error) {
    console.error('Error fetching CSV imports:', error);
    throw new Error('Failed to fetch CSV imports');
  }
}
