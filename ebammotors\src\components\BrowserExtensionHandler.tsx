'use client';

import { useEffect } from 'react';

/**
 * Component that handles browser extension interference by cleaning up
 * attributes that are commonly added by browser extensions after page load.
 * This helps prevent hydration mismatches.
 */
export default function BrowserExtensionHandler() {
  useEffect(() => {
    // List of known browser extension attributes that can cause hydration issues
    const extensionAttributes = [
      'data-new-gr-c-s-check-loaded',
      'data-gr-ext-installed',
      'data-gramm',
      'data-gramm_editor',
      'data-gramm_id',
      'spellcheck',
      'data-ms-editor',
      'data-adblock',
      'data-darkreader-mode',
      'data-darkreader-scheme'
    ];

    const extensionClasses = [
      'translated-ltr',
      'translated-rtl',
      'notranslate',
      'gr_',
      'adblock',
      'darkreader'
    ];

    // Clean up extension attributes from html and body elements
    const cleanupExtensionAttributes = () => {
      const htmlElement = document.documentElement;
      const bodyElement = document.body;

      // Remove known extension attributes
      extensionAttributes.forEach(attr => {
        htmlElement?.removeAttribute(attr);
        bodyElement?.removeAttribute(attr);
      });

      // Remove known extension classes
      extensionClasses.forEach(className => {
        htmlElement?.classList.remove(className);
        bodyElement?.classList.remove(className);
        
        // Also remove classes that start with these prefixes
        const htmlClasses = Array.from(htmlElement?.classList || []);
        const bodyClasses = Array.from(bodyElement?.classList || []);
        
        htmlClasses.forEach(cls => {
          if (cls.startsWith(className)) {
            htmlElement?.classList.remove(cls);
          }
        });
        
        bodyClasses.forEach(cls => {
          if (cls.startsWith(className)) {
            bodyElement?.classList.remove(cls);
          }
        });
      });
    };

    // Clean up immediately
    cleanupExtensionAttributes();

    // Set up a mutation observer to clean up any future extension modifications
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes') {
          const target = mutation.target as Element;
          if (target === document.documentElement || target === document.body) {
            // Small delay to avoid infinite loops
            setTimeout(cleanupExtensionAttributes, 0);
          }
        }
      });
    });

    // Observe changes to html and body elements
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: extensionAttributes
    });

    observer.observe(document.body, {
      attributes: true,
      attributeFilter: extensionAttributes
    });

    // Cleanup on unmount
    return () => {
      observer.disconnect();
    };
  }, []);

  // This component doesn't render anything
  return null;
}
