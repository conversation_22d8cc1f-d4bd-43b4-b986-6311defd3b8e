import {
  createFollowUp,
  getPendingFollowUps,
  updateFollowUp,
  getCustomerById,
  getAllCustomers,
  getActivitiesByCustomerId
} from './crmStorage';
import {
  sendAbandonedCartEmail,
  sendOrderDeliveredEmail,
  sendReengagementEmail,
  sendLeadFollowUpEmail
} from './emailService';
import { emailService, FollowUpData } from './resendService';
import { FollowUp } from '@/types/payment';

/**
 * Process pending follow-ups
 * This should be called periodically (e.g., every hour via cron job)
 */
export async function processPendingFollowUps(): Promise<void> {
  try {
    console.log('🔄 Processing pending follow-ups...');
    
    const pendingFollowUps = await getPendingFollowUps();
    console.log(`Found ${pendingFollowUps.length} pending follow-ups`);

    for (const followup of pendingFollowUps) {
      await processFollowUp(followup);
    }

    console.log('✅ Finished processing follow-ups');
  } catch (error) {
    console.error('❌ Error processing follow-ups:', error);
  }
}

/**
 * Process individual follow-up
 */
async function processFollowUp(followup: FollowUp): Promise<void> {
  try {
    console.log(`Processing follow-up: ${followup.title} (${followup.type})`);

    let success = false;
    let resultMessage = '';

    switch (followup.type) {
      case 'email':
        success = await processEmailFollowUp(followup);
        resultMessage = success ? 'Email sent successfully' : 'Failed to send email';
        break;

      case 'task':
        // For tasks, just mark as completed if it's past due
        success = true;
        resultMessage = 'Task reminder processed';
        break;

      case 'phone':
        // For phone follow-ups, create a reminder for admin
        success = true;
        resultMessage = 'Phone follow-up reminder created';
        break;

      case 'whatsapp':
        // For WhatsApp follow-ups, create a reminder for admin
        success = true;
        resultMessage = 'WhatsApp follow-up reminder created';
        break;

      default:
        console.log(`Unknown follow-up type: ${followup.type}`);
        return;
    }

    // Update follow-up status
    await updateFollowUp(followup.id, {
      status: success ? 'completed' : 'failed',
      completedDate: success ? new Date().toISOString() : undefined,
      result: {
        success,
        message: resultMessage,
        responseReceived: false,
        nextAction: success ? undefined : 'retry_later'
      }
    });

    console.log(`✅ Follow-up processed: ${followup.title} - ${resultMessage}`);

  } catch (error) {
    console.error(`❌ Error processing follow-up ${followup.id}:`, error);
    
    // Mark as failed
    await updateFollowUp(followup.id, {
      status: 'failed',
      result: {
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error',
        responseReceived: false,
        nextAction: 'manual_review'
      }
    });
  }
}

/**
 * Process email follow-up
 */
async function processEmailFollowUp(followup: FollowUp): Promise<boolean> {
  if (!followup.customerId && !followup.leadId) {
    console.error('Follow-up missing customer or lead ID');
    return false;
  }

  // Get customer information
  let customerEmail = '';
  let customerName = '';

  if (followup.customerId) {
    const customer = await getCustomerById(followup.customerId);
    if (!customer) {
      console.error(`Customer not found: ${followup.customerId}`);
      return false;
    }
    customerEmail = customer.personalInfo.email;
    customerName = customer.personalInfo.name;
  }

  if (!customerEmail) {
    console.error('Customer email not found');
    return false;
  }

  // Send email based on automation rule
  if (followup.automationRule) {
    switch (followup.automationRule.trigger) {
      case 'abandoned_cart':
        return await sendAbandonedCartEmail(
          customerEmail,
          customerName,
          followup.automationRule.conditions?.vehicleTitle || 'Selected Vehicle',
          followup.automationRule.conditions?.cartValue || 0,
          followup.automationRule.conditions?.checkoutUrl || 'https://ebammotors.com/checkout'
        );

      case 'order_delivered':
        return await sendOrderDeliveredEmail(
          customerEmail,
          customerName,
          followup.automationRule.conditions?.vehicleTitle || 'Your Vehicle',
          followup.automationRule.conditions?.orderNumber || 'N/A',
          new Date().toLocaleDateString(),
          'https://ebammotors.com/leave-review'
        );

      case 'no_activity':
        return await sendReengagementEmail(
          customerEmail,
          customerName,
          'https://ebammotors.com/stock'
        );

      default:
        // Generic follow-up email
        return await sendLeadFollowUpEmail(
          customerEmail,
          customerName,
          followup.title,
          followup.description,
          new Date(followup.createdAt).toLocaleDateString(),
          'https://ebammotors.com/contact'
        );
    }
  }

  // If no automation rule, send generic follow-up
  return await sendLeadFollowUpEmail(
    customerEmail,
    customerName,
    followup.title,
    followup.description,
    new Date(followup.createdAt).toLocaleDateString(),
    'https://ebammotors.com/contact'
  );
}

/**
 * Create automated follow-ups for abandoned carts
 * Call this when a customer adds items to cart but doesn't complete purchase
 */
export async function scheduleAbandonedCartFollowUp(
  customerId: string,
  cartData: {
    items: Array<{ title: string; price: number }>;
    totalValue: number;
    checkoutUrl: string;
  }
): Promise<void> {
  try {
    const customer = await getCustomerById(customerId);
    if (!customer) return;

    // Schedule follow-up for 24 hours later
    await createFollowUp({
      type: 'email',
      status: 'pending',
      priority: 'medium',
      customerId,
      title: 'Abandoned Cart Follow-up',
      description: `Customer abandoned cart with ${cartData.items.length} items worth ¥${cartData.totalValue.toLocaleString()}`,
      scheduledDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      automationRule: {
        trigger: 'abandoned_cart',
        delay: 24,
        conditions: {
          vehicleTitle: cartData.items[0]?.title,
          cartValue: cartData.totalValue,
          checkoutUrl: cartData.checkoutUrl
        }
      },
      createdBy: 'system'
    });

    console.log(`📅 Scheduled abandoned cart follow-up for customer ${customerId}`);
  } catch (error) {
    console.error('Error scheduling abandoned cart follow-up:', error);
  }
}

/**
 * Create automated follow-ups for delivered orders
 * Call this when an order is marked as delivered
 */
export async function scheduleOrderDeliveryFollowUp(
  customerId: string,
  orderId: string,
  orderData: {
    vehicleTitle: string;
    orderNumber: string;
  }
): Promise<void> {
  try {
    // Schedule follow-up for 7 days after delivery
    await createFollowUp({
      type: 'email',
      status: 'pending',
      priority: 'low',
      customerId,
      orderId,
      title: 'Order Delivery Follow-up',
      description: `Follow up on delivered order ${orderData.orderNumber}`,
      scheduledDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      automationRule: {
        trigger: 'order_delivered',
        delay: 168, // 7 days in hours
        conditions: {
          vehicleTitle: orderData.vehicleTitle,
          orderNumber: orderData.orderNumber
        }
      },
      createdBy: 'system'
    });

    console.log(`📅 Scheduled order delivery follow-up for order ${orderId}`);
  } catch (error) {
    console.error('Error scheduling order delivery follow-up:', error);
  }
}

/**
 * Create re-engagement follow-ups for inactive customers
 * Call this periodically to identify and re-engage inactive customers
 */
export async function scheduleInactiveCustomerFollowUps(): Promise<void> {
  try {
    const customers = await getAllCustomers();
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

    for (const customer of customers) {
      // Check if customer has been inactive for 30+ days
      const lastActivity = await getActivitiesByCustomerId(customer.id);
      const recentActivity = lastActivity.find(activity => 
        new Date(activity.timestamp) > thirtyDaysAgo
      );

      if (!recentActivity && customer.status === 'active') {
        // Check if we haven't already sent a re-engagement email recently
        const existingFollowUps = await getPendingFollowUps();
        const hasRecentReengagement = existingFollowUps.some(f => 
          f.customerId === customer.id && 
          f.automationRule?.trigger === 'no_activity' &&
          new Date(f.createdAt) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
        );

        if (!hasRecentReengagement) {
          await createFollowUp({
            type: 'email',
            status: 'pending',
            priority: 'low',
            customerId: customer.id,
            title: 'Customer Re-engagement',
            description: `Re-engage inactive customer (30+ days since last activity)`,
            scheduledDate: new Date().toISOString(), // Send immediately
            automationRule: {
              trigger: 'no_activity',
              delay: 0,
              conditions: {
                daysSinceLastActivity: 30
              }
            },
            createdBy: 'system'
          });

          console.log(`📅 Scheduled re-engagement follow-up for customer ${customer.id}`);
        }
      }
    }
  } catch (error) {
    console.error('Error scheduling inactive customer follow-ups:', error);
  }
}

/**
 * Initialize follow-up scheduler
 * This should be called when the application starts
 */
export function initializeFollowUpScheduler(): void {
  console.log('🚀 Initializing follow-up scheduler...');

  // Process pending follow-ups every hour
  setInterval(processPendingFollowUps, 60 * 60 * 1000);

  // Check for inactive customers daily
  setInterval(scheduleInactiveCustomerFollowUps, 24 * 60 * 60 * 1000);

  console.log('✅ Follow-up scheduler initialized');
}

/**
 * Send abandoned cart follow-up using Resend
 */
export async function sendAbandonedCartFollowUpResend(customerData: {
  customerName: string;
  customerEmail: string;
  cartItems: Array<{
    id: string;
    title: string;
    price: string;
    quantity: number;
  }>;
}) {
  const followUpData: FollowUpData = {
    customerName: customerData.customerName,
    customerEmail: customerData.customerEmail,
    type: 'abandoned_cart',
    data: {
      items: customerData.cartItems,
      totalItems: customerData.cartItems.length
    }
  };

  try {
    await emailService.sendFollowUpEmail(followUpData);
    console.log('Abandoned cart follow-up email sent via Resend');
    return { success: true };
  } catch (error) {
    console.error('Failed to send abandoned cart follow-up via Resend:', error);
    return { success: false, error };
  }
}

/**
 * Send delivery update follow-up using Resend
 */
export async function sendDeliveryUpdateFollowUpResend(orderData: {
  customerName: string;
  customerEmail: string;
  orderId: string;
  status: string;
  location: string;
  estimatedArrival?: string;
}) {
  const followUpData: FollowUpData = {
    customerName: orderData.customerName,
    customerEmail: orderData.customerEmail,
    type: 'delivery_update',
    data: {
      orderId: orderData.orderId,
      status: orderData.status,
      location: orderData.location,
      estimatedArrival: orderData.estimatedArrival
    }
  };

  try {
    await emailService.sendFollowUpEmail(followUpData);
    console.log('Delivery update follow-up email sent via Resend');
    return { success: true };
  } catch (error) {
    console.error('Failed to send delivery update follow-up via Resend:', error);
    return { success: false, error };
  }
}

/**
 * Send feedback request follow-up using Resend
 */
export async function sendFeedbackRequestFollowUpResend(customerData: {
  customerName: string;
  customerEmail: string;
  orderId: string;
  vehicleTitle: string;
}) {
  const followUpData: FollowUpData = {
    customerName: customerData.customerName,
    customerEmail: customerData.customerEmail,
    type: 'feedback_request',
    data: {
      orderId: customerData.orderId,
      vehicleTitle: customerData.vehicleTitle
    }
  };

  try {
    await emailService.sendFollowUpEmail(followUpData);
    console.log('Feedback request follow-up email sent via Resend');
    return { success: true };
  } catch (error) {
    console.error('Failed to send feedback request follow-up via Resend:', error);
    return { success: false, error };
  }
}
