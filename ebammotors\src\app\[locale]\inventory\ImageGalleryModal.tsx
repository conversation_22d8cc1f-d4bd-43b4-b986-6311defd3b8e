'use client';

import Image from 'next/image';
import { useState, useEffect } from 'react';

interface InventoryItem {
  id: number;
  category: string;
  title: string;
  price: string;
  location: string;
  status: string;
  image: string;
  images: string[];
  specs: string[];
  carId: string;
}

interface ImageGalleryModalProps {
  item: InventoryItem;
  locale: string;
  onClose: () => void;
}

export default function ImageGalleryModal({ item, locale, onClose }: ImageGalleryModalProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  // Auto-play slideshow
  useEffect(() => {
    if (!isAutoPlaying || !item.images || item.images.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentImageIndex(prev => (prev + 1) % item.images.length);
    }, 2000); // Change image every 2 seconds

    return () => clearInterval(interval);
  }, [isAutoPlaying, item.images]);

  const nextImage = () => {
    if (!item.images || item.images.length <= 1) return;
    setCurrentImageIndex(prev => (prev + 1) % item.images.length);
  };

  const prevImage = () => {
    if (!item.images || item.images.length <= 1) return;
    setCurrentImageIndex(prev => (prev - 1 + item.images.length) % item.images.length);
  };

  const goToImage = (index: number) => {
    setCurrentImageIndex(index);
  };

  const toggleAutoPlay = () => {
    setIsAutoPlaying(!isAutoPlaying);
  };

  // Function to get car color based on image filename
  const getCarColor = (item: InventoryItem) => {
    const colorMap: Record<string, { en: string; ja: string }> = {
      'toyota_voxy_2010_001.jpg': { en: 'Pearl White', ja: 'パールホワイト' },
      'toyota_voxy_2010_002.jpg': { en: 'Silver Metallic', ja: 'シルバーメタリック' },
      'toyota_voxy_2010_003.jpg': { en: 'Black Mica', ja: 'ブラックマイカ' },
      'toyota_voxy_2010_004.jpg': { en: 'Dark Blue Mica', ja: 'ダークブルーマイカ' },
      'toyota_voxy_2010_005.jpg': { en: 'Bordeaux Red Pearl', ja: 'ボルドーレッドパール' },
      'toyota_voxy_2010_006.jpg': { en: 'Gray Metallic', ja: 'グレーメタリック' },
      'toyota_voxy_2010_007.jpg': { en: 'White Pearl Crystal Shine', ja: 'ホワイトパールクリスタルシャイン' },
      'toyota_voxy_2012_001.jpg': { en: 'Pearl White', ja: 'パールホワイト' },
      'toyota_voxy_2012_002.jpg': { en: 'Silver Metallic', ja: 'シルバーメタリック' },
      'toyota_voxy_2012_003.jpg': { en: 'Black', ja: 'ブラック' }
    };

    const imagePath = item.image;
    const filename = imagePath.split('/').pop() || '';
    const colorInfo = colorMap[filename];

    if (colorInfo) {
      return locale === 'en' ? colorInfo.en : colorInfo.ja;
    }

    return locale === 'en' ? 'Silver Metallic' : 'シルバーメタリック';
  };

  if (!item.images || item.images.length === 0) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50 p-2 sm:p-4 animate-fadeIn">
      <div className="bg-white rounded-xl max-w-6xl w-full max-h-[95vh] overflow-y-auto transform transition-all duration-500 animate-slideInUp">
        <div className="p-4 sm:p-6">
          {/* Header */}
          <div className="flex justify-between items-start mb-4 animate-slideInDown">
            <div>
              <h2 className="text-xl sm:text-2xl font-bold text-neutral-800 mb-2">
                {item.title}
              </h2>
              <p className="text-lg sm:text-xl font-bold text-primary-600">
                {item.price}
              </p>
            </div>
            <button
              onClick={onClose}
              className="text-neutral-500 hover:text-neutral-700 text-2xl sm:text-3xl transition-all duration-300 hover:scale-110 hover:rotate-90 p-1"
            >
              ×
            </button>
          </div>

          {/* Main Image Display */}
          <div className="relative mb-4">
            <div className="relative h-64 sm:h-96 rounded-lg overflow-hidden bg-neutral-100">
              <Image
                src={item.images[currentImageIndex]}
                alt={`${item.title} - Image ${currentImageIndex + 1}`}
                fill
                className="object-cover transition-all duration-500"
                sizes="(max-width: 768px) 100vw, 80vw"
              />
              
              {/* Navigation Arrows */}
              {item.images.length > 1 && (
                <>
                  <button
                    onClick={prevImage}
                    className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-all duration-300 hover:scale-110"
                  >
                    ←
                  </button>
                  <button
                    onClick={nextImage}
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-all duration-300 hover:scale-110"
                  >
                    →
                  </button>
                </>
              )}

              {/* Image Counter */}
              <div className="absolute top-2 right-2 bg-black/70 text-white px-3 py-1 rounded-full text-sm">
                {currentImageIndex + 1} / {item.images.length}
              </div>

              {/* Auto-play Toggle */}
              {item.images.length > 1 && (
                <button
                  onClick={toggleAutoPlay}
                  className="absolute top-2 left-2 bg-black/70 hover:bg-black/90 text-white px-3 py-1 rounded-full text-sm transition-all duration-300"
                >
                  {isAutoPlaying ? '⏸️' : '▶️'} {locale === 'en' ? 'Auto' : '自動'}
                </button>
              )}
            </div>
          </div>

          {/* Thumbnail Gallery */}
          {item.images.length > 1 && (
            <div className="mb-6">
              <h4 className="text-sm font-semibold text-neutral-700 mb-2">
                {locale === 'en' ? 'All Images' : '全ての画像'}
              </h4>
              <div className="flex gap-2 overflow-x-auto pb-2">
                {item.images.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => goToImage(index)}
                    className={`relative flex-shrink-0 w-16 h-16 sm:w-20 sm:h-20 rounded-lg overflow-hidden border-2 transition-all duration-300 ${
                      index === currentImageIndex
                        ? 'border-primary-500 scale-105'
                        : 'border-neutral-300 hover:border-primary-300'
                    }`}
                  >
                    <Image
                      src={image}
                      alt={`${item.title} - Thumbnail ${index + 1}`}
                      fill
                      className="object-cover"
                      sizes="80px"
                    />
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Item Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Specifications */}
            <div>
              <h4 className="text-base sm:text-lg font-semibold text-neutral-800 mb-3">
                {locale === 'en' ? 'Specifications' : '仕様'}
              </h4>
              <div className="space-y-2">
                {item.specs.map((spec, index) => (
                  <span
                    key={index}
                    className="inline-block bg-neutral-100 text-neutral-700 px-3 py-1 rounded-full text-sm mr-2 mb-2"
                  >
                    {spec}
                  </span>
                ))}
                {item.category === 'cars' && (
                  <span className="inline-block bg-primary-100 text-primary-700 px-3 py-1 rounded-full text-sm mr-2 mb-2">
                    🎨 {getCarColor(item)}
                  </span>
                )}
              </div>
            </div>

            {/* Status and Location */}
            <div>
              <h4 className="text-base sm:text-lg font-semibold text-neutral-800 mb-3">
                {locale === 'en' ? 'Availability' : '在庫状況'}
              </h4>
              <div className="space-y-2">
                <p className="text-neutral-600">
                  📍 {item.location}
                </p>
                <span className={`inline-block px-3 py-1 rounded-full text-sm font-semibold ${
                  item.status === 'Available' ? 'bg-green-100 text-green-800' :
                  item.status === 'Reserved' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-red-100 text-red-800'
                }`}>
                  {item.status}
                </span>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 mt-6 pt-4 border-t border-neutral-200">
            <button
              className={`flex-1 px-6 py-3 rounded-lg font-semibold transition-all duration-300 ${
                item.status === 'Available'
                  ? 'bg-primary-600 text-white hover:bg-primary-700'
                  : 'bg-neutral-300 text-neutral-500 cursor-not-allowed'
              }`}
              disabled={item.status !== 'Available'}
            >
              {locale === 'en' ? 'Inquire Now' : 'お問い合わせ'}
            </button>
            <button
              onClick={onClose}
              className="flex-1 px-6 py-3 border border-neutral-300 text-neutral-700 rounded-lg font-semibold hover:bg-neutral-50 transition-all duration-300"
            >
              {locale === 'en' ? 'Close' : '閉じる'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
