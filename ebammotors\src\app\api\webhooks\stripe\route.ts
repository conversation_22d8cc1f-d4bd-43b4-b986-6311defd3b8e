import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { updatePaymentStatus, addShippingUpdate } from '@/lib/orderStorage';
import { markInvoiceAsPaid, getInvoiceByOrderId } from '@/lib/invoiceGenerator';

const stripe = process.env.STRIPE_SECRET_KEY ? new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2024-12-18.acacia',
}) : null;

const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

export async function POST(request: NextRequest) {
  if (!stripe || !webhookSecret) {
    return NextResponse.json(
      { error: 'Stripe not configured' },
      { status: 500 }
    );
  }

  try {
    const body = await request.text();
    const signature = request.headers.get('stripe-signature');

    if (!signature) {
      return NextResponse.json(
        { error: 'Missing stripe signature' },
        { status: 400 }
      );
    }

    // Verify webhook signature
    let event: Stripe.Event;
    try {
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
    } catch (err) {
      console.error('Webhook signature verification failed:', err);
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 400 }
      );
    }

    // Handle the event
    switch (event.type) {
      case 'payment_intent.succeeded':
        await handlePaymentSucceeded(event.data.object as Stripe.PaymentIntent);
        break;
      
      case 'payment_intent.payment_failed':
        await handlePaymentFailed(event.data.object as Stripe.PaymentIntent);
        break;
      
      case 'payment_intent.processing':
        await handlePaymentProcessing(event.data.object as Stripe.PaymentIntent);
        break;
      
      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return NextResponse.json({ received: true });

  } catch (error) {
    console.error('Webhook error:', error);
    return NextResponse.json(
      { error: 'Webhook handler failed' },
      { status: 500 }
    );
  }
}

async function handlePaymentSucceeded(paymentIntent: Stripe.PaymentIntent) {
  const orderId = paymentIntent.metadata.orderId;
  
  if (!orderId) {
    console.error('No orderId in payment intent metadata');
    return;
  }

  try {
    // Update payment status
    await updatePaymentStatus(orderId, {
      status: 'completed',
      transactionId: paymentIntent.id,
      paidAt: new Date().toISOString(),
    });

    // Mark invoice as paid
    const invoice = await getInvoiceByOrderId(orderId);
    if (invoice) {
      await markInvoiceAsPaid(invoice.id);
    }

    // Add shipping update
    await addShippingUpdate(orderId, {
      timestamp: new Date().toISOString(),
      status: 'Payment Confirmed',
      location: 'EBAM Motors Office',
      description: 'Payment received and confirmed via Stripe. Vehicle preparation will begin shortly.',
    });

    console.log(`Payment succeeded for order ${orderId}`);

  } catch (error) {
    console.error(`Error handling payment success for order ${orderId}:`, error);
  }
}

async function handlePaymentFailed(paymentIntent: Stripe.PaymentIntent) {
  const orderId = paymentIntent.metadata.orderId;
  
  if (!orderId) {
    console.error('No orderId in payment intent metadata');
    return;
  }

  try {
    // Update payment status
    await updatePaymentStatus(orderId, {
      status: 'failed',
      transactionId: paymentIntent.id,
    });

    // Add shipping update
    await addShippingUpdate(orderId, {
      timestamp: new Date().toISOString(),
      status: 'Payment Failed',
      location: 'Payment Processor',
      description: 'Payment failed. Please try again or contact support.',
    });

    console.log(`Payment failed for order ${orderId}`);

  } catch (error) {
    console.error(`Error handling payment failure for order ${orderId}:`, error);
  }
}

async function handlePaymentProcessing(paymentIntent: Stripe.PaymentIntent) {
  const orderId = paymentIntent.metadata.orderId;
  
  if (!orderId) {
    console.error('No orderId in payment intent metadata');
    return;
  }

  try {
    // Update payment status
    await updatePaymentStatus(orderId, {
      status: 'processing',
      transactionId: paymentIntent.id,
    });

    console.log(`Payment processing for order ${orderId}`);

  } catch (error) {
    console.error(`Error handling payment processing for order ${orderId}:`, error);
  }
}
