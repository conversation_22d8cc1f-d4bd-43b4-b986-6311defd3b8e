#!/usr/bin/env node

/**
 * Test script for AI Chatbot functionality
 * Run with: node test-ai-chatbot.js
 */

const http = require('http');

const testChatbot = async () => {
  console.log('🤖 Testing AI Chatbot Setup...\n');

  // Test 1: Check AI Status
  console.log('1. Checking AI Status...');
  try {
    const statusResponse = await fetch('http://localhost:3000/api/chatbot');
    const statusData = await statusResponse.json();
    
    console.log(`   Status: ${statusData.status}`);
    console.log(`   AI Available: ${statusData.aiAvailable}`);
    if (statusData.provider) {
      console.log(`   Provider: ${statusData.provider.provider} (${statusData.provider.model})`);
    }
    console.log('   ✅ Status check passed\n');
  } catch (error) {
    console.log(`   ❌ Status check failed: ${error.message}\n`);
  }

  // Test 2: Test AI Response
  console.log('2. Testing AI Response...');
  try {
    const testMessage = 'Hello, can you tell me about EBAM Motors and your services?';
    const response = await fetch('http://localhost:3000/api/chatbot', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message: testMessage,
        context: 'Testing AI functionality',
        locale: 'en'
      }),
    });

    const data = await response.json();
    
    if (data.success) {
      console.log(`   ✅ AI Response received:`);
      console.log(`   "${data.response.substring(0, 100)}${data.response.length > 100 ? '...' : ''}"`);
      console.log(`   Provider: ${data.provider?.provider || 'Unknown'}\n`);
    } else {
      console.log(`   ⚠️  AI Response failed: ${data.error}`);
      console.log(`   This is normal if AI provider is not configured yet.\n`);
    }
  } catch (error) {
    console.log(`   ❌ AI Response test failed: ${error.message}\n`);
  }

  // Test 3: Environment Check
  console.log('3. Checking Environment Configuration...');
  const envFile = require('fs').readFileSync('.env.local', 'utf8');
  
  const aiProvider = envFile.match(/AI_PROVIDER=(.+)/)?.[1];
  const hasHuggingFaceKey = envFile.includes('HUGGINGFACE_API_KEY=') && !envFile.includes('HUGGINGFACE_API_KEY=your_');
  const hasGroqKey = envFile.includes('GROQ_API_KEY=') && !envFile.includes('GROQ_API_KEY=your_');
  const hasOllamaConfig = envFile.includes('OLLAMA_BASE_URL=');

  console.log(`   AI Provider: ${aiProvider || 'Not set'}`);
  console.log(`   Hugging Face Key: ${hasHuggingFaceKey ? '✅ Configured' : '❌ Not configured'}`);
  console.log(`   Groq Key: ${hasGroqKey ? '✅ Configured' : '❌ Not configured'}`);
  console.log(`   Ollama Config: ${hasOllamaConfig ? '✅ Configured' : '❌ Not configured'}\n`);

  // Recommendations
  console.log('📋 Recommendations:');
  if (aiProvider === 'fallback' || !aiProvider) {
    console.log('   • Set AI_PROVIDER to "huggingface", "groq", or "ollama" for AI responses');
  }
  if (!hasHuggingFaceKey && aiProvider === 'huggingface') {
    console.log('   • Get a free Hugging Face API key: https://huggingface.co/settings/tokens');
  }
  if (!hasGroqKey && aiProvider === 'groq') {
    console.log('   • Get a free Groq API key: https://console.groq.com/');
  }
  if (aiProvider === 'ollama') {
    console.log('   • Make sure Ollama is running: ollama serve');
    console.log('   • Install a model: ollama pull llama3.1:8b');
  }
  
  console.log('\n🎉 Test completed! Check the admin dashboard at /admin/crm for AI status.');
};

// Check if server is running
const checkServer = () => {
  return new Promise((resolve) => {
    const req = http.request('http://localhost:3000', (res) => {
      resolve(true);
    });
    req.on('error', () => {
      resolve(false);
    });
    req.end();
  });
};

// Main execution
(async () => {
  const serverRunning = await checkServer();
  
  if (!serverRunning) {
    console.log('❌ Server not running on localhost:3000');
    console.log('Please start your development server with: npm run dev');
    process.exit(1);
  }

  await testChatbot();
})();
