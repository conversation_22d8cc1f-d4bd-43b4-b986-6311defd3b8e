'use client';

import { useState, useMemo } from 'react';
import { Grid, List, SortAsc, SortDesc, Clock, TrendingDown, Package } from 'lucide-react';

interface StockItem {
  id: number;
  category: string;
  title: string;
  price: string;
  location: string;
  status: string;
  image: string;
  images: string[];
  specs: string[];
  carId: string;
  year?: number;
  mileage?: number;
  fuelType?: string;
  transmission?: string;
  bodyCondition?: string;
  addedDate?: string;
  originalPrice?: string;
  stockQuantity?: number;
  popularity?: number;
}

interface EnhancedStockDisplayProps {
  items: StockItem[];
  locale: string;
  hideSpecialSections?: boolean;
  children: (props: {
    displayItems: StockItem[];
    viewMode: 'grid' | 'list';
    sortBy: string;
    sortOrder: 'asc' | 'desc';
  }) => React.ReactNode;
}

type SortOption = 'price' | 'year' | 'popularity' | 'date' | 'mileage';

export default function EnhancedStockDisplay({ items, locale, hideSpecialSections = false, children }: EnhancedStockDisplayProps) {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<SortOption>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  // Future functionality: advanced filters panel

  // Add metadata to items if not present
  const enhancedItems = useMemo(() => {
    return items.map(item => ({
      ...item,
      addedDate: item.addedDate || new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      originalPrice: item.originalPrice || (Math.random() > 0.7 ? 
        `¥${(parseInt(item.price.replace(/[¥,]/g, '')) + Math.floor(Math.random() * 100000)).toLocaleString()}` : 
        undefined
      ),
      stockQuantity: item.stockQuantity || Math.floor(Math.random() * 5) + 1,
      popularity: item.popularity || Math.floor(Math.random() * 100) + 1,
    }));
  }, [items]);

  // Sort items
  const sortedItems = useMemo(() => {
    const sorted = [...enhancedItems].sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortBy) {
        case 'price':
          aValue = parseInt(a.price.replace(/[¥,]/g, '')) || 0;
          bValue = parseInt(b.price.replace(/[¥,]/g, '')) || 0;
          break;
        case 'year':
          aValue = a.year || 0;
          bValue = b.year || 0;
          break;
        case 'popularity':
          aValue = a.popularity || 0;
          bValue = b.popularity || 0;
          break;
        case 'date':
          aValue = new Date(a.addedDate || 0).getTime();
          bValue = new Date(b.addedDate || 0).getTime();
          break;
        case 'mileage':
          aValue = a.mileage || 0;
          bValue = b.mileage || 0;
          break;
        default:
          return 0;
      }

      if (sortOrder === 'asc') {
        return aValue - bValue;
      } else {
        return bValue - aValue;
      }
    });

    return sorted;
  }, [enhancedItems, sortBy, sortOrder]);

  // Get recently added items (last 7 days)
  const recentlyAdded = useMemo(() => {
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    
    return sortedItems.filter(item => 
      new Date(item.addedDate || 0) > sevenDaysAgo
    );
  }, [sortedItems]);

  // Get price reduced items
  const priceReduced = useMemo(() => {
    return sortedItems.filter(item => item.originalPrice);
  }, [sortedItems]);

  const handleSortChange = (newSortBy: SortOption) => {
    if (sortBy === newSortBy) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(newSortBy);
      setSortOrder('desc');
    }
  };

  const sortOptions = [
    { value: 'date', label: locale === 'en' ? 'Date Added' : '追加日' },
    { value: 'price', label: locale === 'en' ? 'Price' : '価格' },
    { value: 'year', label: locale === 'en' ? 'Year' : '年式' },
    { value: 'popularity', label: locale === 'en' ? 'Popularity' : '人気' },
    { value: 'mileage', label: locale === 'en' ? 'Mileage' : '走行距離' },
  ];

  return (
    <div className="space-y-6">
      {/* Special Sections - Only show if not hidden */}
      {!hideSpecialSections && recentlyAdded.length > 0 && (
        <div className="bg-blue-50 rounded-lg p-6 border border-blue-200">
          <div className="flex items-center space-x-2 mb-4">
            <Clock className="w-5 h-5 text-blue-600" />
            <h3 className="text-lg font-semibold text-blue-800">
              {locale === 'en' ? 'Recently Added' : '最近追加された車両'}
            </h3>
            <span className="bg-blue-600 text-white text-xs px-2 py-1 rounded-full">
              {recentlyAdded.length}
            </span>
          </div>
          <p className="text-blue-700 text-sm mb-4">
            {locale === 'en' 
              ? 'New vehicles added in the last 7 days'
              : '過去7日間に追加された新しい車両'
            }
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {recentlyAdded.slice(0, 3).map(item => (
              <div key={item.id} className="bg-white rounded-lg p-3 border">
                <div className="font-medium text-neutral-800">{item.title}</div>
                <div className="text-primary-600 font-bold">{item.price}</div>
                <div className="text-xs text-neutral-600">
                  {locale === 'en' ? 'Added' : '追加日'}: {new Date(item.addedDate || '').toLocaleDateString()}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {!hideSpecialSections && priceReduced.length > 0 && (
        <div className="bg-red-50 rounded-lg p-6 border border-red-200">
          <div className="flex items-center space-x-2 mb-4">
            <TrendingDown className="w-5 h-5 text-red-600" />
            <h3 className="text-lg font-semibold text-red-800">
              {locale === 'en' ? 'Price Reduced' : '価格下げ'}
            </h3>
            <span className="bg-red-600 text-white text-xs px-2 py-1 rounded-full">
              {priceReduced.length}
            </span>
          </div>
          <p className="text-red-700 text-sm mb-4">
            {locale === 'en' 
              ? 'Vehicles with reduced prices - limited time offers!'
              : '価格が下がった車両 - 期間限定オファー！'
            }
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {priceReduced.slice(0, 3).map(item => (
              <div key={item.id} className="bg-white rounded-lg p-3 border">
                <div className="font-medium text-neutral-800">{item.title}</div>
                <div className="flex items-center space-x-2">
                  <div className="text-primary-600 font-bold">{item.price}</div>
                  <div className="text-neutral-500 line-through text-sm">{item.originalPrice}</div>
                </div>
                <div className="text-xs text-green-600 font-medium">
                  {locale === 'en' ? 'Price Reduced!' : '価格下げ！'}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Controls */}
      <div className="bg-white rounded-lg border p-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          {/* View Mode Toggle */}
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium text-neutral-700">
              {locale === 'en' ? 'View:' : '表示:'}
            </span>
            <div className="flex bg-neutral-100 rounded-lg p-1">
              <button
                onClick={() => setViewMode('grid')}
                className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  viewMode === 'grid'
                    ? 'bg-white text-primary-600 shadow-sm'
                    : 'text-neutral-600 hover:text-neutral-800'
                }`}
              >
                <Grid className="w-4 h-4" />
                <span>{locale === 'en' ? 'Grid' : 'グリッド'}</span>
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  viewMode === 'list'
                    ? 'bg-white text-primary-600 shadow-sm'
                    : 'text-neutral-600 hover:text-neutral-800'
                }`}
              >
                <List className="w-4 h-4" />
                <span>{locale === 'en' ? 'List' : 'リスト'}</span>
              </button>
            </div>
          </div>

          {/* Sort Controls */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-neutral-700">
                {locale === 'en' ? 'Sort by:' : 'ソート:'}
              </span>
              <select
                value={sortBy}
                onChange={(e) => handleSortChange(e.target.value as SortOption)}
                className="border border-neutral-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                {sortOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              <button
                onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                className="p-2 text-neutral-600 hover:text-primary-600 transition-colors"
                title={sortOrder === 'asc' ? 
                  (locale === 'en' ? 'Ascending' : '昇順') : 
                  (locale === 'en' ? 'Descending' : '降順')
                }
              >
                {sortOrder === 'asc' ? <SortAsc className="w-4 h-4" /> : <SortDesc className="w-4 h-4" />}
              </button>
            </div>

            {/* Results Count */}
            <div className="flex items-center space-x-2 text-sm text-neutral-600">
              <Package className="w-4 h-4" />
              <span>
                {sortedItems.length} {locale === 'en' ? 'vehicles' : '台'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Stock Items */}
      {children({
        displayItems: sortedItems,
        viewMode,
        sortBy,
        sortOrder,
      })}
    </div>
  );
}
