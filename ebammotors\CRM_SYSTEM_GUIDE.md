# EBAM Motors - CRM System Guide

## 🎯 Overview

The EBAM Motors CRM (Customer Relationship Management) system provides comprehensive customer management, lead tracking, communication history, and automated follow-ups to enhance customer relationships and drive sales.

## 🚀 Features Implemented

### ✅ **Admin Customer Dashboard**
- Centralized view of all customer data
- Customer overview with stats and recent activity
- Lead management with status tracking
- Interaction history across all channels
- Follow-up management and scheduling

### ✅ **Lead Management System**
- Automatic lead capture from chatbot interactions
- Lead status tracking (new, contacted, qualified, won, lost)
- Priority assignment and source tracking
- Lead conversion to customers
- Comprehensive lead analytics

### ✅ **Communication History**
- All customer interactions logged automatically
- Multi-channel tracking (website, email, phone, WhatsApp)
- Interaction categorization and tagging
- Timeline view of customer journey
- Sentiment tracking and outcome recording

### ✅ **Automated Follow-up System**
- Abandoned cart email reminders (24 hours)
- Order delivery follow-ups (7 days post-delivery)
- Customer re-engagement campaigns (30+ days inactive)
- Lead nurturing sequences
- Customizable email templates

## 📊 CRM Dashboard Access

### **URLs**:
- **Quick Access**: `/admin/crm` (overview only)
- **Full Dashboard**: `/en/admin/crm` (complete interface)
### **Authentication**: Same admin password as other admin panels

### Dashboard Sections:

1. **Overview Tab**
   - Key metrics: New leads, active customers, pending follow-ups, total revenue
   - Recent activity feed
   - Quick stats and insights

2. **Customers Tab**
   - Complete customer list with search/filter
   - Customer details: contact info, order history, loyalty points
   - Customer status management
   - Individual customer overview with interaction history

3. **Leads Tab**
   - Lead pipeline management
   - Lead status updates and priority assignment
   - Source tracking (chatbot, contact form, manual)
   - Lead conversion tracking

4. **Interactions Tab**
   - Complete communication history
   - Multi-channel interaction logs
   - Interaction categorization and search
   - Timeline view of customer touchpoints

5. **Follow-ups Tab**
   - Pending and completed follow-ups
   - Automated follow-up scheduling
   - Manual follow-up creation
   - Follow-up performance tracking

## 🔄 Automated Processes

### **Lead Capture**
- Chatbot interactions automatically create leads
- Lead capture forms integrated with CRM
- Automatic lead scoring and prioritization
- Real-time lead notifications

### **Customer Sync**
- Order creation automatically creates/updates customer records
- Customer data synchronized across all systems
- Automatic customer segmentation
- Loyalty points and tier management

### **Interaction Logging**
- All chatbot conversations logged
- Order status changes tracked
- Website activity monitoring
- Email interactions recorded

### **Follow-up Automation**
- **Abandoned Cart**: 24-hour email reminder
- **Order Delivered**: 7-day satisfaction follow-up
- **Inactive Customers**: 30-day re-engagement campaign
- **Lead Nurturing**: Customizable sequences

## 📧 Email Templates

### **Abandoned Cart**
- Personalized vehicle details
- Urgency messaging
- Direct checkout link
- Contact information

### **Order Delivered**
- Order confirmation details
- Review request
- Referral program promotion
- Support contact info

### **Customer Re-engagement**
- Latest vehicle arrivals
- Special offers
- Loyalty program benefits
- Stock browsing link

### **Lead Follow-up**
- Inquiry acknowledgment
- Service offerings
- Contact options
- Next steps guidance

## 🛠️ API Endpoints

### **Leads Management**
- `GET /api/leads?adminKey=[key]` - Get all leads
- `POST /api/leads` - Create new lead
- `PATCH /api/leads` - Update lead status
- `DELETE /api/leads?id=[id]&adminKey=[key]` - Delete lead

### **Customer Management**
- `GET /api/customers?adminKey=[key]` - Get all customers
- `GET /api/customers?id=[id]&overview=true&adminKey=[key]` - Customer overview
- `POST /api/customers` - Create/update customer
- `PATCH /api/customers` - Update customer

### **Interactions**
- `GET /api/interactions?adminKey=[key]` - Get all interactions
- `GET /api/interactions?customerId=[id]&adminKey=[key]` - Customer interactions
- `POST /api/interactions` - Log new interaction

### **Follow-ups**
- `GET /api/followups?adminKey=[key]` - Get all follow-ups
- `GET /api/followups?pending=true&adminKey=[key]` - Pending follow-ups
- `POST /api/followups` - Create follow-up
- `PATCH /api/followups` - Update follow-up status

### **Follow-up Processing**
- `POST /api/followups/process` - Manual trigger processing
- `POST /api/followups/process` (webhook) - Automated processing

## 🔧 Setup Instructions

### 1. **Environment Variables**
Add to your `.env.local` file:
```env
# Existing admin password
ADMIN_PASSWORD=your_secure_admin_password

# For automated follow-up processing (optional)
CRON_SECRET=your_cron_secret_key

# Email service configuration (when ready for production)
# SENDGRID_API_KEY=your_sendgrid_key
# MAILGUN_API_KEY=your_mailgun_key
```

### 2. **Data Storage**
- CRM data is stored in JSON files in the `data/` directory
- Files are automatically created on first use
- All data persists between server restarts
- Files are excluded from Git for security

### 3. **Automated Processing**
The system includes automated follow-up processing that can be triggered:

#### **Manual Trigger** (for testing):
```bash
curl -X POST http://localhost:3000/api/followups/process \
  -H "Content-Type: application/json" \
  -d '{"action": "process_pending", "adminKey": "your_admin_password"}'
```

#### **Webhook Trigger** (for production):
```bash
curl -X POST http://localhost:3000/api/followups/process \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_cron_secret" \
  -d '{"action": "webhook"}'
```

### 4. **Production Deployment**

#### **Vercel Cron Jobs** (Recommended):
Create `vercel.json`:
```json
{
  "crons": [
    {
      "path": "/api/followups/process",
      "schedule": "0 * * * *"
    }
  ]
}
```

#### **GitHub Actions** (Alternative):
Create `.github/workflows/followups.yml`:
```yaml
name: Process Follow-ups
on:
  schedule:
    - cron: '0 * * * *'  # Every hour
jobs:
  process:
    runs-on: ubuntu-latest
    steps:
      - name: Trigger Follow-ups
        run: |
          curl -X POST ${{ secrets.SITE_URL }}/api/followups/process \
            -H "Authorization: Bearer ${{ secrets.CRON_SECRET }}" \
            -d '{"action": "webhook"}'
```

## 📈 Benefits

### **For Business**
- **Increased Sales**: Automated follow-ups recover abandoned carts
- **Customer Retention**: Re-engagement campaigns keep customers active
- **Lead Conversion**: Systematic lead nurturing improves conversion rates
- **Efficiency**: Automated processes reduce manual work
- **Insights**: Comprehensive analytics drive better decisions

### **For Customers**
- **Better Service**: Personalized communication and follow-ups
- **Timely Updates**: Automatic order status notifications
- **Relevant Offers**: Targeted promotions based on interests
- **Support**: Proactive assistance and problem resolution

## 🔒 Security & Privacy

- **Admin Authentication**: Secure password protection for all admin functions
- **Data Encryption**: Sensitive data handled securely
- **Privacy Compliance**: Customer data managed according to privacy standards
- **Access Control**: Role-based access to different CRM functions

## 📞 Support

For technical support or questions about the CRM system:
- **Email**: <EMAIL>
- **Phone**: +************
- **Documentation**: This guide and inline code comments

## 🚀 Next Steps

1. **Access the CRM Dashboard**:
   - Quick overview: `/admin/crm`
   - Full dashboard: `/en/admin/crm`
2. **Review Existing Data**: Check customers and leads already in the system
3. **Test Follow-ups**: Manually trigger follow-up processing
4. **Configure Email Service**: Set up production email service (SendGrid, etc.)
5. **Set Up Automation**: Configure cron jobs for automated processing
6. **Monitor Performance**: Track metrics and optimize campaigns

The CRM system is now fully integrated with your existing EBAM Motors website and ready to help you manage customer relationships more effectively!
