'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { X, Plus, Minus, ArrowRight, Star, TrendingUp, TrendingDown } from 'lucide-react';
// Removed direct import of generateStockFromFileSystem to avoid fs module error

interface StockItem {
  id: number;
  category: string;
  title: string;
  price: string;
  location: string;
  status: string;
  image: string;
  images: string[];
  specs: string[];
  carId: string;
  year?: number;
  mileage?: number;
  fuelType?: string;
  transmission?: string;
  bodyCondition?: string;
}

interface ComparisonPanelProps {
  vehicle: StockItem;
  locale: string;
  onClose?: () => void;
  isModal?: boolean;
}

export default function ComparisonPanel({ vehicle, locale, onClose, isModal = false }: ComparisonPanelProps) {
  const [compareVehicles, setCompareVehicles] = useState<StockItem[]>([vehicle]);
  const [availableVehicles, setAvailableVehicles] = useState<StockItem[]>([]);
  const [showAddVehicle, setShowAddVehicle] = useState(false);

  useEffect(() => {
    const loadVehicles = async () => {
      try {
        const response = await fetch('/api/stock');
        if (!response.ok) {
          throw new Error('Failed to fetch stock data');
        }
        const allVehicles = await response.json();
        const cars = allVehicles
          .filter((item: StockItem) => item.category === 'cars' && item.carId !== vehicle.carId)
          .slice(0, 10); // Limit to 10 for performance
        setAvailableVehicles(cars);
      } catch (error) {
        console.error('Error loading vehicles for comparison:', error);
        // Set some fallback vehicles or empty array
        setAvailableVehicles([]);
      }
    };
    loadVehicles();
  }, [vehicle.carId]);

  const addVehicleToComparison = (vehicleToAdd: StockItem) => {
    if (compareVehicles.length < 3 && !compareVehicles.find(v => v.carId === vehicleToAdd.carId)) {
      setCompareVehicles([...compareVehicles, vehicleToAdd]);
      setShowAddVehicle(false);
    }
  };

  const removeVehicleFromComparison = (vehicleId: string) => {
    if (compareVehicles.length > 1) {
      setCompareVehicles(compareVehicles.filter(v => v.carId !== vehicleId));
    }
  };

  const extractPrice = (priceStr: string): number => {
    const numericStr = priceStr.replace(/[¥,]/g, '');
    return parseInt(numericStr) || 0;
  };

  const getComparisonIcon = (currentValue: number, otherValues: number[]) => {
    const average = otherValues.reduce((sum, val) => sum + val, 0) / otherValues.length;
    if (currentValue > average * 1.1) return <TrendingUp className="w-4 h-4 text-green-600" />;
    if (currentValue < average * 0.9) return <TrendingDown className="w-4 h-4 text-red-600" />;
    return <div className="w-4 h-4"></div>;
  };

  const comparisonFields = [
    {
      label: locale === 'en' ? 'Price' : '価格',
      getValue: (v: StockItem) => v.price,
      getNumericValue: (v: StockItem) => extractPrice(v.price),
      isNumeric: true,
      lowerIsBetter: true,
    },
    {
      label: locale === 'en' ? 'Year' : '年式',
      getValue: (v: StockItem) => v.year?.toString() || 'N/A',
      getNumericValue: (v: StockItem) => v.year || 0,
      isNumeric: true,
      lowerIsBetter: false,
    },
    {
      label: locale === 'en' ? 'Mileage' : '走行距離',
      getValue: (v: StockItem) => v.mileage ? `${Math.floor(v.mileage / 1000)}k km` : 'N/A',
      getNumericValue: (v: StockItem) => v.mileage || 0,
      isNumeric: true,
      lowerIsBetter: true,
    },
    {
      label: locale === 'en' ? 'Fuel Type' : '燃料タイプ',
      getValue: (v: StockItem) => v.fuelType || 'N/A',
      getNumericValue: () => 0,
      isNumeric: false,
      lowerIsBetter: false,
    },
    {
      label: locale === 'en' ? 'Transmission' : 'トランスミッション',
      getValue: (v: StockItem) => v.transmission || 'N/A',
      getNumericValue: () => 0,
      isNumeric: false,
      lowerIsBetter: false,
    },
    {
      label: locale === 'en' ? 'Condition' : '状態',
      getValue: (v: StockItem) => v.bodyCondition || 'N/A',
      getNumericValue: () => 0,
      isNumeric: false,
      lowerIsBetter: false,
    },
  ];

  const content = (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-neutral-800">
          {locale === 'en' ? 'Vehicle Comparison' : '車両比較'}
        </h2>
        {isModal && onClose && (
          <button
            onClick={onClose}
            className="p-2 text-neutral-600 hover:text-neutral-800 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        )}
      </div>

      {/* Add Vehicle Button */}
      {compareVehicles.length < 3 && (
        <div className="flex justify-center">
          <button
            onClick={() => setShowAddVehicle(!showAddVehicle)}
            className="flex items-center space-x-2 px-3 sm:px-4 py-2 border-2 border-dashed border-neutral-300 rounded-lg text-neutral-600 hover:border-primary-500 hover:text-primary-600 transition-colors text-sm sm:text-base"
            aria-expanded={showAddVehicle}
            aria-controls="vehicle-selection"
          >
            <Plus className="w-4 h-4" />
            <span className="hidden sm:inline">{locale === 'en' ? 'Add Vehicle to Compare' : '比較する車両を追加'}</span>
            <span className="sm:hidden">{locale === 'en' ? 'Add Vehicle' : '車両追加'}</span>
          </button>
        </div>
      )}

      {/* Available Vehicles */}
      {showAddVehicle && (
        <div className="bg-neutral-50 rounded-lg p-4 border">
          <h3 className="font-semibold text-neutral-800 mb-3">
            {locale === 'en' ? 'Select Vehicle to Compare' : '比較する車両を選択'}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 max-h-60 overflow-y-auto">
            {availableVehicles.map((availableVehicle) => (
              <button
                key={availableVehicle.carId}
                onClick={() => addVehicleToComparison(availableVehicle)}
                className="flex items-center space-x-3 p-3 bg-white rounded-lg border hover:border-primary-500 transition-colors text-left"
              >
                <div className="relative w-12 h-12 rounded-lg overflow-hidden flex-shrink-0">
                  <Image
                    src={availableVehicle.image}
                    alt={availableVehicle.title}
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-neutral-800 truncate">{availableVehicle.title}</div>
                  <div className="text-sm text-neutral-600">{availableVehicle.price}</div>
                </div>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Comparison Table */}
      <div className="bg-white rounded-lg border overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="bg-neutral-50 border-b">
                <th className="text-left p-2 sm:p-4 font-semibold text-neutral-800 min-w-[100px] sm:min-w-[120px] text-sm sm:text-base">
                  {locale === 'en' ? 'Specification' : '仕様'}
                </th>
                {compareVehicles.map((compareVehicle) => (
                  <th key={compareVehicle.carId} className="text-center p-2 sm:p-4 min-w-[150px] sm:min-w-[200px]">
                    <div className="space-y-2">
                      <div className="relative w-12 h-12 sm:w-16 sm:h-16 mx-auto rounded-lg overflow-hidden">
                        <Image
                          src={compareVehicle.image}
                          alt={compareVehicle.title}
                          fill
                          className="object-cover"
                        />
                      </div>
                      <div className="font-semibold text-neutral-800 text-xs sm:text-sm">
                        {compareVehicle.title}
                      </div>
                      {compareVehicles.length > 1 && compareVehicle.carId !== vehicle.carId && (
                        <button
                          onClick={() => removeVehicleFromComparison(compareVehicle.carId)}
                          className="text-red-600 hover:text-red-800 transition-colors p-1"
                          aria-label={locale === 'en' ? 'Remove from comparison' : '比較から削除'}
                        >
                          <Minus className="w-3 h-3 sm:w-4 sm:h-4 mx-auto" />
                        </button>
                      )}
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {comparisonFields.map((field, fieldIndex) => {
                const numericValues = compareVehicles
                  .map(v => field.getNumericValue(v))
                  .filter(v => v > 0);
                
                return (
                  <tr key={fieldIndex} className="border-b hover:bg-neutral-50">
                    <td className="p-2 sm:p-4 font-medium text-neutral-700 text-sm sm:text-base">{field.label}</td>
                    {compareVehicles.map((compareVehicle) => {
                      const value = field.getValue(compareVehicle);
                      const numericValue = field.getNumericValue(compareVehicle);
                      const otherValues = numericValues.filter(v => v !== numericValue);

                      return (
                        <td key={compareVehicle.carId} className="p-2 sm:p-4 text-center">
                          <div className="flex items-center justify-center space-x-1 sm:space-x-2">
                            <span className="font-semibold text-neutral-800 text-sm sm:text-base">{value}</span>
                            {field.isNumeric && otherValues.length > 0 && (
                              <div className="flex-shrink-0">
                                {getComparisonIcon(numericValue, otherValues)}
                              </div>
                            )}
                          </div>
                        </td>
                      );
                    })}
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-wrap gap-3 justify-center">
        {compareVehicles.map((compareVehicle) => (
          <Link
            key={compareVehicle.carId}
            href={`/${locale}/stock/${compareVehicle.carId}`}
            className="flex items-center space-x-2 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
          >
            <span>{locale === 'en' ? 'View' : '表示'} {compareVehicle.title.split(' ')[0]}</span>
            <ArrowRight className="w-4 h-4" />
          </Link>
        ))}
      </div>

      {/* Comparison Tips */}
      <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
        <h3 className="font-semibold text-blue-800 mb-2 flex items-center space-x-2">
          <Star className="w-4 h-4" />
          <span>{locale === 'en' ? 'Comparison Tips' : '比較のヒント'}</span>
        </h3>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• {locale === 'en' ? 'Green arrows indicate better values' : '緑の矢印はより良い値を示します'}</li>
          <li>• {locale === 'en' ? 'Red arrows indicate areas for consideration' : '赤の矢印は検討すべき領域を示します'}</li>
          <li>• {locale === 'en' ? 'Consider total cost including shipping' : '送料を含む総費用を考慮してください'}</li>
        </ul>
      </div>
    </div>
  );

  if (isModal) {
    return (
      <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
        <div className="bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-hidden">
          <div className="p-6 overflow-y-auto max-h-[90vh]">
            {content}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg p-6 border">
      {content}
    </div>
  );
}
