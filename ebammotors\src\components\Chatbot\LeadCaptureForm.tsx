'use client';

import { useState } from 'react';
import { X, Send } from 'lucide-react';

interface LeadCaptureFormProps {
  locale: string;
  isOpen: boolean;
  onClose: () => void;
  productInterest?: string;
}

export default function LeadCaptureForm({ 
  locale, 
  isOpen, 
  onClose, 
  productInterest 
}: LeadCaptureFormProps) {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    location: '',
    message: '',
    productInterest: productInterest || ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Create lead in CRM system
      const leadData = {
        source: 'chatbot',
        status: 'new',
        priority: 'medium',
        customerInfo: {
          name: formData.name,
          email: formData.email,
          phone: formData.phone,
          location: formData.location,
          preferredContact: formData.email ? 'email' : 'phone',
        },
        inquiry: {
          subject: 'Chatbot Lead Inquiry',
          message: formData.message,
          productInterest: formData.productInterest,
        },
        tags: ['chatbot', 'website_lead'],
      };

      const response = await fetch('/api/leads', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(leadData),
      });

      const result = await response.json();

      if (result.success) {
        setIsSubmitted(true);

        // Auto-close after success
        setTimeout(() => {
          onClose();
          setIsSubmitted(false);
          setFormData({
            name: '',
            email: '',
            phone: '',
            location: '',
            message: '',
            productInterest: ''
          });
        }, 2000);
      } else {
        console.error('Failed to submit lead:', result.message);
        alert('Failed to submit inquiry. Please try again.');
      }
    } catch (error) {
      console.error('Error submitting lead:', error);
      alert('Failed to submit inquiry. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-[60] flex items-center justify-center p-4">
      <div className="bg-white rounded-xl max-w-md w-full max-h-[90vh] overflow-y-auto animate-slideInUp">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-4 rounded-t-xl flex items-center justify-between">
          <h3 className="font-semibold">
            {locale === 'en' ? 'Get a Quote' : '見積もりを取得'}
          </h3>
          <button
            onClick={onClose}
            className="text-white/80 hover:text-white text-xl"
          >
            <X size={20} />
          </button>
        </div>

        {/* Form */}
        <div className="p-6">
          {!isSubmitted ? (
            <form onSubmit={handleSubmit} className="space-y-4">
              <p className="text-sm text-neutral-600 mb-4">
                {locale === 'en' 
                  ? 'Fill out this form and we\'ll get back to you with a detailed quote!'
                  : 'このフォームにご記入いただければ、詳細な見積もりをお送りします！'
                }
              </p>

              {/* Name */}
              <div>
                <label className="block text-sm font-medium text-neutral-700 mb-1">
                  {locale === 'en' ? 'Full Name *' : 'お名前 *'}
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                  placeholder={locale === 'en' ? 'Your full name' : 'お名前をご入力ください'}
                />
              </div>

              {/* Email */}
              <div>
                <label className="block text-sm font-medium text-neutral-700 mb-1">
                  {locale === 'en' ? 'Email Address *' : 'メールアドレス *'}
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                  placeholder={locale === 'en' ? '<EMAIL>' : '<EMAIL>'}
                />
              </div>

              {/* Phone */}
              <div>
                <label className="block text-sm font-medium text-neutral-700 mb-1">
                  {locale === 'en' ? 'Phone Number' : '電話番号'}
                </label>
                <input
                  type="tel"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                  placeholder={locale === 'en' ? '+233 XX XXX XXXX' : '+81 XX XXXX XXXX'}
                />
              </div>

              {/* Location */}
              <div>
                <label className="block text-sm font-medium text-neutral-700 mb-1">
                  {locale === 'en' ? 'Location/Country *' : '所在地/国 *'}
                </label>
                <select
                  name="location"
                  value={formData.location}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                >
                  <option value="">
                    {locale === 'en' ? 'Select your location' : '所在地を選択'}
                  </option>
                  <option value="Ghana">Ghana</option>
                  <option value="Nigeria">Nigeria</option>
                  <option value="Kenya">Kenya</option>
                  <option value="South Africa">South Africa</option>
                  <option value="Other Africa">
                    {locale === 'en' ? 'Other African Country' : 'その他のアフリカ諸国'}
                  </option>
                  <option value="Other">
                    {locale === 'en' ? 'Other Country' : 'その他の国'}
                  </option>
                </select>
              </div>

              {/* Product Interest */}
              {productInterest && (
                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-1">
                    {locale === 'en' ? 'Product of Interest' : '興味のある商品'}
                  </label>
                  <input
                    type="text"
                    name="productInterest"
                    value={formData.productInterest}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm bg-neutral-50"
                    readOnly
                  />
                </div>
              )}

              {/* Message */}
              <div>
                <label className="block text-sm font-medium text-neutral-700 mb-1">
                  {locale === 'en' ? 'Message/Requirements' : 'メッセージ/要件'}
                </label>
                <textarea
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                  placeholder={locale === 'en' 
                    ? 'Tell us about your specific requirements, quantity needed, etc.'
                    : '具体的な要件、必要数量などをお聞かせください。'
                  }
                />
              </div>

              {/* Submit Button */}
              <button
                type="submit"
                disabled={isSubmitting}
                className="w-full bg-blue-500 hover:bg-blue-600 disabled:bg-neutral-300 text-white py-2 px-4 rounded-lg font-semibold transition-colors duration-200 flex items-center justify-center"
              >
                {isSubmitting ? (
                  <div className="flex items-center">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    {locale === 'en' ? 'Sending...' : '送信中...'}
                  </div>
                ) : (
                  <>
                    <Send size={16} className="mr-2" />
                    {locale === 'en' ? 'Get Quote' : '見積もりを取得'}
                  </>
                )}
              </button>
            </form>
          ) : (
            <div className="text-center py-8">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <div className="text-green-600 text-2xl">✓</div>
              </div>
              <h4 className="text-lg font-semibold text-neutral-800 mb-2">
                {locale === 'en' ? 'Thank You!' : 'ありがとうございます！'}
              </h4>
              <p className="text-neutral-600">
                {locale === 'en' 
                  ? 'We\'ll get back to you within 24 hours with a detailed quote.'
                  : '24時間以内に詳細な見積もりをお送りします。'
                }
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
