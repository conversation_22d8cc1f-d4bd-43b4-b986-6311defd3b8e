import { NextRequest, NextResponse } from 'next/server';
import {
  getAllReviews,
  addReview,
  updateReviewStatus,
  getApprovedReviews,
  getReviewById
} from '@/lib/reviewStorage';
import { emailService, ReviewNotificationData } from '@/lib/resendService';
import { getAdminAuth } from '@/lib/adminMiddleware';
import {
  checkRateLimit,
  getClientIP,
  sanitizeInput,
  isValidEmail,
  containsSuspiciousContent,
  getSecurityHeaders
} from '@/lib/security';
import { startPerformanceMonitoring, logError } from '@/lib/monitoring';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();

    // Extract and validate form fields
    const name = formData.get('name') as string;
    const location = formData.get('location') as string;
    const email = formData.get('email') as string;
    const rating = formData.get('rating') as string;
    const review = formData.get('review') as string;

    // Validate required fields
    if (!name?.trim()) {
      return NextResponse.json(
        { success: false, message: 'Name is required' },
        { status: 400 }
      );
    }
    if (!location?.trim()) {
      return NextResponse.json(
        { success: false, message: 'Location is required' },
        { status: 400 }
      );
    }
    if (!email?.trim()) {
      return NextResponse.json(
        { success: false, message: 'Email is required' },
        { status: 400 }
      );
    }
    if (!rating || isNaN(parseInt(rating)) || parseInt(rating) < 1 || parseInt(rating) > 5) {
      return NextResponse.json(
        { success: false, message: 'Valid rating (1-5) is required' },
        { status: 400 }
      );
    }
    if (!review?.trim()) {
      return NextResponse.json(
        { success: false, message: 'Review text is required' },
        { status: 400 }
      );
    }

    // Validate word count (minimum 10 words, maximum 150 words)
    const words = review.trim().split(/\s+/).filter(word => word.length > 0);
    if (words.length < 10) {
      return NextResponse.json(
        { success: false, message: 'Review must be at least 10 words long' },
        { status: 400 }
      );
    }
    if (words.length > 150) {
      return NextResponse.json(
        { success: false, message: 'Review must not exceed 150 words' },
        { status: 400 }
      );
    }

    // Prepare review data
    const reviewData = {
      name: name.trim(),
      location: location.trim(),
      email: email.trim(),
      rating: parseInt(rating),
      review: review.trim(),
      locale: (formData.get('locale') as string) || 'en',
      submittedAt: (formData.get('submittedAt') as string) || new Date().toISOString(),
      status: 'pending' as const // pending, approved, rejected
    };

    // Store review using persistent storage
    const savedReview = await addReview(reviewData);

    // Send email notification to admin about new review
    try {
      const reviewNotificationData: ReviewNotificationData = {
        customerName: reviewData.name,
        vehicleTitle: reviewData.vehicleTitle,
        rating: reviewData.rating,
        review: reviewData.review,
        reviewDate: new Date(reviewData.submittedAt).toLocaleDateString()
      };

      await emailService.sendReviewNotificationToAdmin(reviewNotificationData);
      console.log('Review notification email sent to admin');
    } catch (emailError) {
      console.error('Failed to send review notification email:', emailError);
      // Don't fail the review submission if email fails
    }

    return NextResponse.json({
      success: true,
      message: 'Review submitted successfully! It will be reviewed by our team before being published.',
      reviewId: savedReview.id
    });

  } catch (error) {
    console.error('Error processing review:', error);

    // Provide more specific error message
    let errorMessage = 'Failed to submit review';
    if (error instanceof Error) {
      errorMessage = error.message;
    }

    return NextResponse.json(
      { success: false, message: errorMessage },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  const monitor = startPerformanceMonitoring(request);

  try {
    // Check for admin authentication (supports both new and legacy methods)
    const adminAuth = getAdminAuth(request);

    if (adminAuth.isValid) {
      // Admin access - return all reviews
      const allReviews = await getAllReviews();
      monitor.finish(200);

      const response = NextResponse.json({ reviews: allReviews });

      // Add security headers
      const securityHeaders = getSecurityHeaders();
      Object.entries(securityHeaders).forEach(([key, value]) => {
        response.headers.set(key, value);
      });

      return response;
    }

    // Public access - only return approved reviews
    const approvedReviews = await getApprovedReviews();
    monitor.finish(200);

    const response = NextResponse.json({ reviews: approvedReviews });

    // Add security headers
    const securityHeaders = getSecurityHeaders();
    Object.entries(securityHeaders).forEach(([key, value]) => {
      response.headers.set(key, value);
    });

    return response;
  } catch (error) {
    logError(error instanceof Error ? error : 'Unknown error', {
      endpoint: '/api/reviews',
      method: 'GET',
      request,
      additionalContext: { action: 'fetch_reviews' }
    });

    monitor.finish(500, error instanceof Error ? error.message : 'Unknown error');

    const response = NextResponse.json(
      { success: false, message: 'Failed to fetch reviews' },
      { status: 500 }
    );

    // Add security headers even for errors
    const securityHeaders = getSecurityHeaders();
    Object.entries(securityHeaders).forEach(([key, value]) => {
      response.headers.set(key, value);
    });

    return response;
  }
}

// Admin endpoint to approve/reject reviews
export async function PATCH(request: NextRequest) {
  try {
    const { reviewId, status, adminKey } = await request.json();
    
    // Admin authentication using environment variable
    const validAdminKey = process.env.ADMIN_PASSWORD || 'admin123'; // Fallback for development
    if (adminKey !== validAdminKey) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get review data before updating (for email notification)
    const review = await getReviewById(reviewId);
    if (!review) {
      return NextResponse.json(
        { success: false, message: 'Review not found' },
        { status: 404 }
      );
    }

    // Update review status using persistent storage
    const success = await updateReviewStatus(reviewId, status);

    if (!success) {
      return NextResponse.json(
        { success: false, message: 'Failed to update review status' },
        { status: 500 }
      );
    }

    // Send email notification if review is approved
    if (status === 'approved' && review.email) {
      try {
        const reviewNotificationData: ReviewNotificationData = {
          customerName: review.name,
          vehicleTitle: review.vehicleTitle,
          rating: review.rating,
          review: review.review,
          reviewDate: new Date(review.submittedAt).toLocaleDateString(),
          isApproval: true
        };

        await emailService.sendReviewApprovalNotification(review.email, reviewNotificationData);
        console.log('Review approval email sent to customer');
      } catch (emailError) {
        console.error('Failed to send review approval email:', emailError);
        // Don't fail the approval process if email fails
      }
    }

    return NextResponse.json({
      success: true,
      message: `Review ${status} successfully`
    });

  } catch (error) {
    console.error('Error moderating review:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to moderate review' },
      { status: 500 }
    );
  }
}
