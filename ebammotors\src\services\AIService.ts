import { CHATBOT_CONFIG, validateResponse, getUncertaintyResponse } from '../config/chatbotConfig';

interface AIResponse {
  text: string;
  error?: string;
  hallucinationRisk?: 'low' | 'medium' | 'high';
  validationIssues?: string[];
}

interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export class AIService {
  private provider: string;
  private huggingfaceApiKey?: string;
  private huggingfaceModel: string;
  private ollamaBaseUrl: string;
  private ollamaModel: string;
  private groqApiKey?: string;
  private groqModel: string;

  constructor() {
    this.provider = process.env.AI_PROVIDER || 'fallback';
    this.huggingfaceApiKey = process.env.HUGGINGFACE_API_KEY;
    this.huggingfaceModel = process.env.HUGGINGFACE_MODEL || 'meta-llama/Llama-2-7b-chat-hf';
    this.ollamaBaseUrl = process.env.OLLAMA_BASE_URL || 'http://localhost:11434';
    this.ollamaModel = process.env.OLLAMA_MODEL || 'llama3.2:3b';
    this.groqApiKey = process.env.GROQ_API_KEY;
    this.groqModel = process.env.GROQ_MODEL || 'llama-3.1-8b-instant';
  }

  async generateResponse(
    userMessage: string, 
    context: string = '', 
    locale: string = 'en'
  ): Promise<AIResponse> {
    const systemPrompt = this.buildSystemPrompt(context, locale);
    
    try {
      switch (this.provider) {
        case 'huggingface':
          return await this.callHuggingFace(systemPrompt, userMessage);
        case 'ollama':
          return await this.callOllama(systemPrompt, userMessage);
        case 'groq':
          return await this.callGroq(systemPrompt, userMessage);
        default:
          return { text: '', error: 'AI provider not configured. Using fallback system.' };
      }
    } catch (error) {
      console.error('AI Service Error:', error);
      return { text: '', error: 'AI service temporarily unavailable' };
    }
  }

  private buildSystemPrompt(context: string, locale: string): string {
    const carPricing = `
EXACT Car Inventory & Pricing (FOB Japan) - DO NOT MAKE UP OTHER PRICES:
- Toyota Voxy (8 seats): ¥300,000 (CHEAPEST)
- Toyota Noah (8 seats): ¥350,000
- Toyota Sienta (7 seats): ¥320,000
- Toyota Yaris (5 seats): ¥550,000
- Toyota Vitz (5 seats): ¥325,000

ONLY these 5 cars are available. DO NOT mention other models or prices.
`;

    const antiHallucinationRules = `
CRITICAL RULES - NEVER VIOLATE:
1. ONLY use the exact car prices listed above
2. NEVER make up car models, prices, or availability
3. If asked about cars not listed, say "I don't have that information in our current inventory"
4. NEVER invent shipping costs, dates, or technical specifications
5. For unknown information, say "Let me connect you with our team for specific details"
6. ONLY use the contact information provided: WhatsApp +233245375692
7. NEVER create fake customer testimonials or reviews
8. If unsure about anything, be honest and offer to connect them with a human agent
`;

    const basePrompt = locale === 'ja' ?
      `あなたはEBAM Motorsの親切で正確なカスタマーサービス担当者です。日本からガーナ・アフリカへの中古品輸出を専門としています。

${carPricing}

${antiHallucinationRules}

会社情報:
- 中古車（トヨタ、ホンダ、日産）
- 電子機器（家電、携帯電話、ノートパソコン）
- 家具（ダイニングセット、オフィス家具）
- 自転車（シティバイク、マウンテンバイク）
- 重機
- 家庭用品

連絡先:
- WhatsApp: +233245375692
- 場所: クマシ、ガーナ
- WhatsAppチャンネル: https://whatsapp.com/channel/0029Vb5sDlFDTkK191T6Ut1G

正確な情報のみを提供し、不明な場合は素直に「詳細については担当者にお繋ぎします」と答えてください。` :
      `You are a helpful and ACCURATE customer service representative for EBAM Motors, specializing in exporting used goods from Japan to Ghana and Africa.

${carPricing}

${antiHallucinationRules}

Company Services:
- Used Cars (Toyota, Honda, Nissan)
- Electronics (appliances, phones, laptops)
- Furniture (dining sets, office furniture)
- Bicycles (city bikes, mountain bikes)
- Heavy Equipment
- Household Items

Contact Information:
- WhatsApp: +233245375692
- Location: Kumasi, Ghana
- WhatsApp Channel: https://whatsapp.com/channel/0029Vb5sDlFDTkK191T6Ut1G

Be accurate and honest. Only provide information you're certain about. When in doubt, offer to connect them with a human agent.`;

    return context ? `${basePrompt}\n\nPage Context: ${context}` : basePrompt;
  }

  private async callHuggingFace(systemPrompt: string, userMessage: string): Promise<AIResponse> {
    if (!this.huggingfaceApiKey || this.huggingfaceApiKey.includes('your_')) {
      return { text: '', error: 'Hugging Face API key not configured' };
    }

    // Use text generation with proper formatting
    const prompt = `${systemPrompt}

User: ${userMessage}
Assistant:`;

    try {
      const response = await fetch(`https://api-inference.huggingface.co/models/${this.huggingfaceModel}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.huggingfaceApiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          inputs: prompt,
          parameters: {
            max_new_tokens: 200,
            temperature: 0.7,
            do_sample: true,
            return_full_text: false,
            stop: ["User:", "Human:"]
          }
        }),
      });

      const data = await response.json();

      if (data.error) {
        return { text: '', error: data.error };
      }

      const generatedText = data[0]?.generated_text || '';
      return { text: generatedText.trim() };
    } catch (error) {
      return { text: '', error: `Hugging Face API error: ${error}` };
    }
  }

  private async callOllama(systemPrompt: string, userMessage: string): Promise<AIResponse> {
    const messages: ChatMessage[] = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: userMessage }
    ];

    const response = await fetch(`${this.ollamaBaseUrl}/api/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: this.ollamaModel,
        messages: messages,
        stream: false,
        options: {
          temperature: 0.7,
          top_p: 0.9,
          max_tokens: 150
        }
      }),
    });

    if (!response.ok) {
      return { text: '', error: 'Ollama service unavailable' };
    }

    const data = await response.json();
    return { text: data.message?.content || '' };
  }

  private async callGroq(systemPrompt: string, userMessage: string): Promise<AIResponse> {
    if (!this.groqApiKey || this.groqApiKey.includes('your_')) {
      return { text: '', error: 'Groq API key not configured' };
    }

    const messages: ChatMessage[] = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: userMessage }
    ];

    try {
      const response = await fetch('https://api.groq.com/openai/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.groqApiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: this.groqModel,
          messages: messages,
          // Anti-hallucination parameters from config
          temperature: CHATBOT_CONFIG.antiHallucination.temperature,
          max_tokens: CHATBOT_CONFIG.antiHallucination.maxTokens,
          top_p: CHATBOT_CONFIG.antiHallucination.topP,
          frequency_penalty: CHATBOT_CONFIG.antiHallucination.frequencyPenalty,
          presence_penalty: CHATBOT_CONFIG.antiHallucination.presencePenalty,
          stream: false,
          stop: CHATBOT_CONFIG.antiHallucination.stopSequences
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return { text: '', error: `Groq API error: ${errorData.error?.message || 'Unknown error'}` };
      }

      const data = await response.json();

      if (data.error) {
        return { text: '', error: data.error.message };
      }

      const aiResponse = data.choices?.[0]?.message?.content || '';

      // Comprehensive hallucination detection and response validation
      const validationResult = this.validateAndCleanResponse(aiResponse);

      return {
        text: validationResult.cleanedResponse,
        hallucinationRisk: validationResult.risk,
        validationIssues: validationResult.issues
      };
    } catch (error) {
      return { text: '', error: `Groq API request failed: ${error}` };
    }
  }

  // Comprehensive validation and hallucination detection
  private validateAndCleanResponse(response: string): {
    cleanedResponse: string;
    risk: 'low' | 'medium' | 'high';
    issues: string[];
  } {
    const validation = validateResponse(response);
    const issues = validation.issues;
    let cleanedResponse = response.trim();
    let risk: 'low' | 'medium' | 'high' = 'low';

    // Determine risk level based on issues found
    if (issues.length === 0) {
      risk = 'low';
    } else if (issues.length <= 2) {
      risk = 'medium';
    } else {
      risk = 'high';
    }

    // Handle high-risk responses
    if (risk === 'high') {
      // Replace with safe fallback
      cleanedResponse = getUncertaintyResponse() +
        ` Please contact us via WhatsApp at ${CHATBOT_CONFIG.company.whatsapp} for accurate information.`;

      // Log for monitoring
      if (CHATBOT_CONFIG.monitoring.logSuspiciousResponses) {
        console.warn('High hallucination risk detected:', {
          originalResponse: response,
          issues: issues,
          timestamp: new Date().toISOString()
        });
      }
    } else if (risk === 'medium') {
      // Add disclaimer for medium risk
      cleanedResponse += `\n\n*${CHATBOT_CONFIG.responseGuidelines.disclaimers.pricing}*`;
    }

    // Additional safety checks
    cleanedResponse = this.applySafetyFilters(cleanedResponse);

    return {
      cleanedResponse,
      risk,
      issues
    };
  }

  // Apply additional safety filters
  private applySafetyFilters(response: string): string {
    let filtered = response;

    // Replace any invalid car models with safe alternatives
    const invalidModelPattern = /Toyota [A-Z][a-z]+(?!Voxy|Noah|Sienta|Yaris|Vitz)/g;
    filtered = filtered.replace(invalidModelPattern, 'one of our available Toyota models');

    // Replace invalid prices with safe language
    const invalidPricePattern = /¥[0-9,]+(?!,000)/g;
    filtered = filtered.replace(invalidPricePattern, 'competitive pricing');

    // Remove absolute guarantees
    filtered = filtered.replace(/guaranteed|promise|100%/gi, 'typically');

    // Ensure response isn't too long
    if (filtered.length > CHATBOT_CONFIG.responseGuidelines.maxResponseLength) {
      filtered = filtered.substring(0, CHATBOT_CONFIG.responseGuidelines.maxResponseLength - 50) +
        '... Please contact us for complete details.';
    }

    return filtered;
  }

  // Check if AI service is available
  async isAvailable(): Promise<boolean> {
    try {
      switch (this.provider) {
        case 'huggingface':
          return !!this.huggingfaceApiKey;
        case 'ollama':
          const ollamaResponse = await fetch(`${this.ollamaBaseUrl}/api/tags`, { 
            method: 'GET',
            signal: AbortSignal.timeout(5000) // 5 second timeout
          });
          return ollamaResponse.ok;
        case 'groq':
          return !!this.groqApiKey;
        default:
          return false;
      }
    } catch (error) {
      return false;
    }
  }

  // Get current provider info
  getProviderInfo(): { provider: string; model: string } {
    switch (this.provider) {
      case 'huggingface':
        return { provider: 'Hugging Face', model: this.huggingfaceModel };
      case 'ollama':
        return { provider: 'Ollama', model: this.ollamaModel };
      case 'groq':
        return { provider: 'Groq', model: this.groqModel };
      default:
        return { provider: 'Fallback', model: 'Rule-based system' };
    }
  }
}

export const aiService = new AIService();
