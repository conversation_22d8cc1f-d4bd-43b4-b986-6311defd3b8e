# EBAM Motors - Production Deployment Guide

## Environment Variables for Production

### Required Environment Variables

Set these environment variables on your hosting platform (Vercel, Netlify, etc.):

```bash
# Admin Authentication - CHANGE THIS!
ADMIN_PASSWORD=your_very_secure_admin_password_2024!

# Email notifications for new reviews - REQUIRED
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_gmail_app_password
NOTIFICATION_EMAIL=<EMAIL>

# Optional: Database (for future use)
# DATABASE_URL=your_database_connection_string
```

### Platform-Specific Instructions

#### Vercel Deployment
1. Connect your GitHub repository to Vercel
2. Go to Project Settings → Environment Variables
3. Add `ADMIN_PASSWORD` with your secure password
4. Deploy the project

#### Netlify Deployment
1. Connect your GitHub repository to Netlify
2. Go to Site Settings → Environment Variables
3. Add `ADMIN_PASSWORD` with your secure password
4. Deploy the project

#### Other Platforms
- Set `ADMIN_PASSWORD` environment variable in your platform's dashboard
- Ensure the variable is available at build time and runtime

## Security Checklist

### Before Going Live
- [ ] Change `ADMIN_PASSWORD` from default value
- [ ] Use a strong password (minimum 12 characters)
- [ ] Verify `.env.local` is not committed to Git
- [ ] Test admin login with new password
- [ ] Enable HTTPS on your domain
- [ ] Consider adding rate limiting
- [ ] Set up monitoring and alerts

### Password Requirements
- Minimum 12 characters
- Mix of uppercase, lowercase, numbers, symbols
- Not based on dictionary words
- Unique to this application

### Example Strong Passwords
```
EbamMotors2024!SecureAdmin
ReviewAdmin#2024$Strong
AdminPass!2024@Ebam#Secure
```

## File Structure for Production

### Files to Include in Git
```
✅ .env.example          # Template for environment variables
✅ DEPLOYMENT_GUIDE.md   # This file
✅ ADMIN_REVIEW_GUIDE.md # Admin instructions
✅ All source code files
```

### Files to Exclude from Git (Already in .gitignore)
```
❌ .env.local           # Contains actual passwords
❌ .env                 # Contains actual passwords
❌ .env.production      # Contains actual passwords
❌ /node_modules        # Dependencies
❌ /.next              # Build files
```

## Testing Before Production

### Local Testing
1. Set `ADMIN_PASSWORD` in `.env.local`
2. Restart development server
3. Test admin login at `/admin/reviews`
4. Verify password authentication works

### Staging Testing
1. Deploy to staging environment
2. Set environment variables
3. Test all admin functionality
4. Verify reviews system works end-to-end

## Monitoring and Maintenance

### Regular Tasks
- Monitor admin login attempts
- Review and approve customer submissions
- Update admin password periodically
- Monitor for security vulnerabilities
- Backup review data regularly

### Security Monitoring
- Watch for failed login attempts
- Monitor unusual admin activity
- Set up alerts for system errors
- Regular security audits

## Troubleshooting

### Admin Login Issues
1. Verify `ADMIN_PASSWORD` environment variable is set
2. Check if password contains special characters that need escaping
3. Restart application after changing environment variables
4. Check server logs for authentication errors

### Environment Variable Issues
1. Ensure variable is set on hosting platform
2. Verify variable name matches exactly: `ADMIN_PASSWORD`
3. Check if platform requires restart after adding variables
4. Test with simple password first, then use complex one

## Future Enhancements

### Recommended Improvements
1. **Database Integration**: Replace in-memory storage with PostgreSQL/MongoDB
2. **JWT Authentication**: Implement proper session management
3. **Role-Based Access**: Add different admin permission levels
4. **Two-Factor Authentication**: Add 2FA for extra security
5. **Audit Logging**: Track all admin actions
6. **Rate Limiting**: Prevent brute force attacks
7. **Email Notifications**: Alert on new review submissions

### Security Upgrades
1. **Password Hashing**: Hash admin passwords instead of plain text comparison
2. **Session Management**: Implement proper session timeouts
3. **CSRF Protection**: Add CSRF tokens to admin forms
4. **IP Whitelisting**: Restrict admin access to specific IP addresses
5. **SSL/TLS**: Ensure all admin traffic is encrypted
