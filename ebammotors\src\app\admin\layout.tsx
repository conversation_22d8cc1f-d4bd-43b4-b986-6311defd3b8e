'use client';

import { Inter, Poppins } from "next/font/google";
import Link from "next/link";
import { useEffect, useState } from "react";
import "../globals.css";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

const poppins = Poppins({
  variable: "--font-poppins",
  subsets: ["latin"],
  weight: ["400", "500", "600", "700", "800"],
});

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className="min-h-screen bg-neutral-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading admin panel...</p>
        </div>
      </div>
    );
  }
  return (
    <div className="min-h-screen bg-neutral-50 admin-layout">
      {/* Admin Header */}
      <header className="bg-white/95 backdrop-blur-sm shadow-sm border-b border-neutral-200 sticky top-0 z-50 transition-all duration-300">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <div className="w-8 h-8 bg-red-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">A</span>
              </div>
              <h1 className="text-xl font-bold text-neutral-800">
                EBAM Motors Admin
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-neutral-600">Admin Panel</span>
              <Link
                href="/"
                className="text-sm text-primary-600 hover:text-primary-700 font-medium"
              >
                ← Back to Website
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Admin Content */}
      <main className="py-8">
        {children}
      </main>

      {/* Admin Footer */}
      <footer className="bg-white border-t border-neutral-200 mt-auto">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="text-center text-sm text-neutral-500">
            EBAM Motors Admin Panel - Handle with care
          </div>
        </div>
      </footer>
    </div>
  );
}
