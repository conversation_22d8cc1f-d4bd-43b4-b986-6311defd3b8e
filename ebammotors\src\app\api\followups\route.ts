import { NextRequest, NextResponse } from 'next/server';
import { 
  createFollowUp, 
  getAllFollowUps, 
  getFollowUpsByStatus,
  getPendingFollowUps,
  updateFollowUp,
  getFollowUpsByCustomerId
} from '@/lib/crmStorage';
import { FollowUp } from '@/types/payment';

interface CartItem {
  id: string;
  title: string;
  price: number;
  quantity: number;
}

interface CartData {
  items: CartItem[];
  total: number;
}

// GET - Fetch follow-ups
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const adminKey = searchParams.get('adminKey');
    const status = searchParams.get('status') as FollowUp['status'];
    const customerId = searchParams.get('customerId');
    const pending = searchParams.get('pending') === 'true';

    // Verify admin authentication
    const validAdminKey = process.env.ADMIN_PASSWORD || 'admin123';
    if (adminKey !== validAdminKey) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    let followups: FollowUp[] = [];

    // Get pending follow-ups (due now or overdue)
    if (pending) {
      followups = await getPendingFollowUps();
    }
    // Get follow-ups by customer ID
    else if (customerId) {
      followups = await getFollowUpsByCustomerId(customerId);
    }
    // Get follow-ups by status
    else if (status) {
      followups = await getFollowUpsByStatus(status);
    }
    // Get all follow-ups
    else {
      followups = await getAllFollowUps();
    }

    // Sort by scheduled date
    followups.sort((a, b) => new Date(a.scheduledDate).getTime() - new Date(b.scheduledDate).getTime());

    return NextResponse.json({ success: true, followups });

  } catch (error) {
    console.error('Error fetching follow-ups:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch follow-ups' },
      { status: 500 }
    );
  }
}

// POST - Create new follow-up
export async function POST(request: NextRequest) {
  try {
    const followupData = await request.json();

    // Validate required fields
    if (!followupData.title || !followupData.scheduledDate) {
      return NextResponse.json(
        { success: false, message: 'Title and scheduled date are required' },
        { status: 400 }
      );
    }

    // Set defaults
    const newFollowUpData = {
      type: 'task',
      status: 'pending',
      priority: 'medium',
      createdBy: 'admin',
      ...followupData,
    } as Omit<FollowUp, 'id' | 'createdAt' | 'updatedAt'>;

    const followup = await createFollowUp(newFollowUpData);

    return NextResponse.json({ 
      success: true, 
      message: 'Follow-up created successfully',
      followup 
    });

  } catch (error) {
    console.error('Error creating follow-up:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to create follow-up' },
      { status: 500 }
    );
  }
}

// PATCH - Update follow-up
export async function PATCH(request: NextRequest) {
  try {
    const { followupId, adminKey, ...updates } = await request.json();

    // Verify admin authentication
    const validAdminKey = process.env.ADMIN_PASSWORD || 'admin123';
    if (adminKey !== validAdminKey) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (!followupId) {
      return NextResponse.json(
        { success: false, message: 'Follow-up ID is required' },
        { status: 400 }
      );
    }

    // If marking as completed, set completion date
    if (updates.status === 'completed' && !updates.completedDate) {
      updates.completedDate = new Date().toISOString();
    }

    const success = await updateFollowUp(followupId, updates);

    if (!success) {
      return NextResponse.json(
        { success: false, message: 'Follow-up not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ 
      success: true, 
      message: 'Follow-up updated successfully' 
    });

  } catch (error) {
    console.error('Error updating follow-up:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to update follow-up' },
      { status: 500 }
    );
  }
}

// Utility functions for automated follow-ups

/**
 * Create abandoned cart follow-up
 */
export async function createAbandonedCartFollowUp(customerId: string, cartData: CartData) {
  try {
    const followupData = {
      type: 'email' as const,
      status: 'pending' as const,
      priority: 'medium' as const,
      customerId,
      title: 'Abandoned Cart Follow-up',
      description: `Customer left items in cart: ${cartData.items?.map((item: CartItem) => item.title).join(', ')}`,
      scheduledDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours later
      template: 'abandoned_cart',
      emailConfig: {
        subject: 'Complete Your Purchase - Items Still Available',
        body: `Hi there! We noticed you left some great items in your cart. Complete your purchase now before they're gone!`,
      },
      automationRule: {
        trigger: 'abandoned_cart' as const,
        delay: 24, // hours
        conditions: { cartValue: cartData.totalValue },
      },
      createdBy: 'system',
    };

    return await createFollowUp(followupData);
  } catch (error) {
    console.error('Error creating abandoned cart follow-up:', error);
    return null;
  }
}

/**
 * Create order delivery follow-up
 */
export async function createOrderDeliveryFollowUp(customerId: string, orderId: string) {
  try {
    const followupData = {
      type: 'email' as const,
      status: 'pending' as const,
      priority: 'low' as const,
      customerId,
      orderId,
      title: 'Order Delivery Follow-up',
      description: 'Follow up on order delivery and request review',
      scheduledDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days later
      template: 'order_delivered',
      emailConfig: {
        subject: 'How was your recent purchase?',
        body: `We hope you're enjoying your recent purchase! Please let us know how everything went and consider leaving a review.`,
      },
      automationRule: {
        trigger: 'order_delivered' as const,
        delay: 168, // 7 days in hours
      },
      createdBy: 'system',
    };

    return await createFollowUp(followupData);
  } catch (error) {
    console.error('Error creating order delivery follow-up:', error);
    return null;
  }
}

/**
 * Create customer inactivity follow-up
 */
export async function createInactivityFollowUp(customerId: string, daysSinceLastActivity: number) {
  try {
    const followupData = {
      type: 'email' as const,
      status: 'pending' as const,
      priority: 'low' as const,
      customerId,
      title: 'Customer Re-engagement',
      description: `Customer inactive for ${daysSinceLastActivity} days`,
      scheduledDate: new Date().toISOString(), // Send immediately
      template: 'customer_reengagement',
      emailConfig: {
        subject: 'We miss you! Check out our latest vehicles',
        body: `It's been a while since your last visit. Come back and see our latest vehicle arrivals!`,
      },
      automationRule: {
        trigger: 'no_activity' as const,
        delay: 0,
        conditions: { daysSinceLastActivity },
      },
      createdBy: 'system',
    };

    return await createFollowUp(followupData);
  } catch (error) {
    console.error('Error creating inactivity follow-up:', error);
    return null;
  }
}
