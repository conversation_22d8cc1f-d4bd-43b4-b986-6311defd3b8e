import { notFound } from 'next/navigation';
import { Inter, Poppins } from "next/font/google";
import WhatsAppFloatingButton from '@/components/WhatsAppFloatingButton';
import ChatbotWrapper from '@/components/Chatbot/ChatbotWrapper';
import BrowserExtensionHandler from '@/components/BrowserExtensionHandler';
import "../globals.css";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

const poppins = Poppins({
  variable: "--font-poppins",
  subsets: ["latin"],
  weight: ["400", "500", "600", "700", "800"],
});

const locales = ['en', 'ja'] as const;
type Locale = typeof locales[number];

export function generateStaticParams() {
  return locales.map((locale) => ({ locale }));
}

export default async function LocaleLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;

  // Validate that the incoming `locale` parameter is valid
  if (!locales.includes(locale as Locale)) {
    notFound();
  }

  return (
    <html lang={locale} suppressHydrationWarning>
      <body className={`${inter.variable} ${poppins.variable} antialiased`} suppressHydrationWarning>
        <BrowserExtensionHandler />
        {children}
        <WhatsAppFloatingButton locale={locale} />
        <ChatbotWrapper locale={locale} />
      </body>
    </html>
  );
}
