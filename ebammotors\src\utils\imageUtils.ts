import fs from 'fs';
import path from 'path';

function getImageExtensions(): string[] {
  return ['.jpg', '.jpeg', '.JPG', '.JPEG', '.png', '.PNG'];
}

function isImageFile(filename: string): boolean {
  const ext = path.extname(filename);
  return getImageExtensions().includes(ext);
}

export function getImagesFromDirectory(directoryPath: string): string[] {
  try {
    const fullPath = path.join(process.cwd(), 'public', directoryPath);
    
    if (!fs.existsSync(fullPath)) {
      console.warn(`Directory not found: ${fullPath}`);
      return [];
    }

    const files = fs.readdirSync(fullPath);
    const imageFiles = files
      .filter(file => isImageFile(file))
      .map(file => `/${directoryPath}/${file}`)
      .sort();

    return imageFiles;
  } catch (error) {
    console.error(`Error reading directory ${directoryPath}:`, error);
    return [];
  }
}

export function getRandomImagesFromDirectory(directoryPath: string, count: number = 3): string[] {
  const allImages = getImagesFromDirectory(directoryPath);

  if (allImages.length <= count) {
    return allImages;
  }

  // Use deterministic selection instead of random to avoid hydration mismatch
  // Take evenly spaced images from the sorted array
  const step = Math.floor(allImages.length / count);
  const selected: string[] = [];
  for (let i = 0; i < count && i * step < allImages.length; i++) {
    selected.push(allImages[i * step]);
  }

  // If we need more images, fill from the beginning
  while (selected.length < count && selected.length < allImages.length) {
    const nextIndex = selected.length;
    if (nextIndex < allImages.length) {
      selected.push(allImages[nextIndex]);
    }
  }

  return selected;
}

// Predefined image sets for different sections
export const getHeroImages = (): string[] => {
  const carImages = getRandomImagesFromDirectory('used cars', 3);
  const voxyImages = getRandomImagesFromDirectory('car-models/toyota-voxy/toyota_voxy_2010_TVXYA', 2);
  return [...carImages, ...voxyImages].slice(0, 4);
};

export const getServiceImages = () => {
  return {
    usedCars: getRandomImagesFromDirectory('used cars', 3),
    accidentCars: getRandomImagesFromDirectory('Accident cars', 3),
    carParts: getRandomImagesFromDirectory('car parts and tires', 3),
    furniture: getRandomImagesFromDirectory('furniture', 3),
    electronics: getRandomImagesFromDirectory('electronics', 3),
    bicycles: getRandomImagesFromDirectory('used bicycle', 3),
    heavyEquipment: getRandomImagesFromDirectory('heavy duty equipment', 3),
    household: getRandomImagesFromDirectory('household items', 3),
  };
};

export const getGalleryImages = (): string[] => {
  const categories = [
    'used cars',
    'car parts and tires', 
    'electronics',
    'move-out services'
  ];
  
  const allImages: string[] = [];
  categories.forEach(category => {
    const images = getRandomImagesFromDirectory(category, 2);
    allImages.push(...images);
  });
  
  return allImages.slice(0, 8);
};
