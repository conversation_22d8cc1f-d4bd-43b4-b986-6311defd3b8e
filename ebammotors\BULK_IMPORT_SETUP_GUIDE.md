# 🚀 Bulk CSV Import System - Setup Guide

## Overview

This guide will help you set up the **Database + Bulk CSV Import** system for managing hundreds or thousands of cars efficiently. No more manual uploads one by one!

## 🎯 What You'll Get

✅ **PostgreSQL Database** - Scalable, reliable car inventory storage  
✅ **Bulk CSV Import** - Upload hundreds of cars with one CSV file  
✅ **Admin Panel** - Comprehensive car management interface  
✅ **Search & Filters** - Find cars quickly with advanced filtering  
✅ **Bulk Operations** - Delete, update, or manage multiple cars at once  
✅ **Import History** - Track all your bulk import operations  
✅ **CSV Templates** - Pre-built templates with sample data  

## 📋 Prerequisites

1. **Database Setup** (Choose one):
   - Vercel Postgres (Recommended for Vercel deployments)
   - Supabase (Great free tier)
   - Any PostgreSQL database

2. **Dependencies Installation**:
   ```bash
   npm install @vercel/postgres tsx
   ```

## 🗄️ Step 1: Database Setup

### Option A: Vercel Postgres (Recommended)

1. Go to [Vercel Dashboard](https://vercel.com/dashboard)
2. Select your project or create new one
3. Go to **Storage** → **Create Database** → **Postgres**
4. Copy the connection strings

### Option B: Supabase

1. Go to [Supabase](https://supabase.com)
2. Create new project
3. Go to **Settings** → **Database**
4. Copy the connection string

### Option C: Other PostgreSQL

Use any PostgreSQL provider (AWS RDS, Google Cloud SQL, etc.)

## 🔧 Step 2: Environment Configuration

Add these to your `.env.local` file:

```bash
# Database Configuration (choose one)

# For Vercel Postgres:
POSTGRES_URL="postgres://..."
POSTGRES_PRISMA_URL="postgres://..."
POSTGRES_URL_NON_POOLING="postgres://..."

# OR for Supabase:
DATABASE_URL="postgresql://..."

# OR for other PostgreSQL:
DATABASE_URL="postgresql://username:password@host:port/database"

# Admin Configuration
ADMIN_PASSWORD="your-secure-admin-password"
```

## 📊 Step 3: Create Database Schema

Run this SQL in your database to create the required tables:

```sql
-- Car Inventory Table
CREATE TABLE cars (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  car_id VARCHAR(255) UNIQUE NOT NULL,
  
  -- Basic Information
  make VARCHAR(100) NOT NULL,
  model VARCHAR(100) NOT NULL,
  year INTEGER NOT NULL CHECK (year >= 1990 AND year <= 2030),
  title VARCHAR(255) NOT NULL,
  
  -- Pricing
  price INTEGER NOT NULL CHECK (price > 0),
  original_price INTEGER,
  currency VARCHAR(3) DEFAULT 'JPY',
  
  -- Technical Specifications
  mileage INTEGER CHECK (mileage >= 0),
  fuel_type VARCHAR(50),
  transmission VARCHAR(50),
  engine_size VARCHAR(50),
  drive_type VARCHAR(50),
  seats INTEGER CHECK (seats > 0 AND seats <= 50),
  doors INTEGER CHECK (doors > 0 AND doors <= 10),
  body_type VARCHAR(50),
  
  -- Condition & Features
  body_condition VARCHAR(50) DEFAULT 'Good',
  interior_condition VARCHAR(50) DEFAULT 'Good',
  exterior_color VARCHAR(50),
  interior_color VARCHAR(50),
  
  -- Images & Media
  main_image VARCHAR(500),
  images TEXT[],
  image_folder VARCHAR(255),
  
  -- Specifications Array
  specs TEXT[],
  features TEXT[],
  
  -- Status & Availability
  status VARCHAR(50) DEFAULT 'Available',
  stock_quantity INTEGER DEFAULT 1 CHECK (stock_quantity >= 0),
  location VARCHAR(100) DEFAULT 'Japan',
  
  -- SEO & Display
  slug VARCHAR(255) UNIQUE,
  description TEXT,
  meta_title VARCHAR(255),
  meta_description TEXT,
  
  -- Tracking & Analytics
  view_count INTEGER DEFAULT 0,
  popularity_score INTEGER DEFAULT 0,
  is_featured BOOLEAN DEFAULT FALSE,
  is_recently_added BOOLEAN DEFAULT TRUE,
  is_price_reduced BOOLEAN DEFAULT FALSE,
  
  -- Import & Management
  import_batch_id VARCHAR(255),
  import_source VARCHAR(100),
  import_notes TEXT,
  
  -- Timestamps
  added_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  sold_date TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- CSV Import Tracking Table
CREATE TABLE csv_imports (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  batch_id VARCHAR(255) UNIQUE NOT NULL,
  filename VARCHAR(255) NOT NULL,
  total_rows INTEGER NOT NULL,
  successful_imports INTEGER DEFAULT 0,
  failed_imports INTEGER DEFAULT 0,
  status VARCHAR(50) DEFAULT 'processing',
  error_log TEXT[],
  import_summary JSONB,
  imported_by VARCHAR(255),
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_cars_make_model ON cars(make, model);
CREATE INDEX idx_cars_year ON cars(year);
CREATE INDEX idx_cars_price ON cars(price);
CREATE INDEX idx_cars_status ON cars(status);
CREATE INDEX idx_cars_added_date ON cars(added_date DESC);
CREATE INDEX idx_cars_mileage ON cars(mileage);
CREATE INDEX idx_cars_fuel_type ON cars(fuel_type);
CREATE INDEX idx_cars_transmission ON cars(transmission);
CREATE INDEX idx_cars_body_condition ON cars(body_condition);
CREATE INDEX idx_cars_is_featured ON cars(is_featured);
CREATE INDEX idx_cars_is_recently_added ON cars(is_recently_added);
CREATE INDEX idx_cars_slug ON cars(slug);
CREATE INDEX idx_cars_car_id ON cars(car_id);

CREATE INDEX idx_csv_imports_batch_id ON csv_imports(batch_id);
CREATE INDEX idx_csv_imports_status ON csv_imports(status);
CREATE INDEX idx_csv_imports_started_at ON csv_imports(started_at DESC);

-- Full-text search index
CREATE INDEX idx_cars_search ON cars USING gin(
  to_tsvector('english', 
    COALESCE(title, '') || ' ' || 
    COALESCE(make, '') || ' ' || 
    COALESCE(model, '') || ' ' || 
    COALESCE(description, '')
  )
);
```

## 🔄 Step 4: Migrate Existing Data (Optional)

If you have existing cars in the file system, migrate them to the database:

```bash
# Test migration (dry run)
npm run migrate-cars-dry

# Run actual migration
npm run migrate-cars

# Run with detailed output
npm run migrate-cars-verbose
```

## 🎛️ Step 5: Access Admin Panel

1. Start your development server:
   ```bash
   npm run dev
   ```

2. Navigate to the admin panel:
   ```
   http://localhost:3000/admin/cars
   ```

3. Enter your admin password (from `.env.local`)

## 📁 Step 6: Bulk Import Process

### Download CSV Template

1. Go to **Admin Panel** → **Bulk Import** tab
2. Click **"Download Template"** or **"Template with Samples"**
3. Open the CSV file in Excel or Google Sheets

### Prepare Your Data

The CSV template includes these columns:

**Required Fields:**
- `car_id` - Unique identifier (e.g., toyota_voxy_2012_TVXYA)
- `make` - Car manufacturer (Toyota, Honda, etc.)
- `model` - Car model (Voxy, Noah, Sienta, etc.)
- `year` - Manufacturing year (1990-2030)
- `price` - Price in Yen (positive number)

**Optional Fields:**
- `mileage` - Mileage in kilometers
- `fuel_type` - Gasoline, Hybrid, Electric, Diesel
- `transmission` - Automatic, Manual, CVT
- `seats` - Number of seats
- `images` - Comma-separated image URLs
- `specs` - Comma-separated specifications
- `status` - Available, Reserved, Sold, Pending
- And many more...

### Upload and Import

1. Fill out your CSV file with car data
2. Go to **Admin Panel** → **Bulk Import** tab
3. Click **"Click to upload CSV file"**
4. Select your CSV file
5. Click **"Import Cars"**
6. Monitor the import progress and results

## 📊 Step 7: Manage Your Inventory

### Inventory Tab
- **Search & Filter** - Find cars by make, model, year, price, etc.
- **Bulk Operations** - Select multiple cars for bulk delete
- **Individual Actions** - View, edit, or delete single cars
- **Pagination** - Navigate through large inventories

### Import History Tab
- **Track Imports** - See all your CSV import operations
- **Success/Failure Rates** - Monitor import quality
- **Error Logs** - Debug failed imports

## 🔍 Step 8: Update Frontend (Optional)

To use the database instead of file system on your public pages:

1. Update your stock/inventory pages to use the new API:
   ```typescript
   // Replace file system calls with database API calls
   const response = await fetch('/api/cars?status=Available');
   const data = await response.json();
   const cars = data.cars;
   ```

2. The API supports all the same filtering and pagination you need

## 🚀 Production Deployment

1. **Set Environment Variables** on your hosting platform
2. **Run Database Schema** on your production database
3. **Deploy Your Code** with the new admin panel
4. **Import Your Cars** using the CSV bulk import

## 💡 Tips for Success

### CSV Import Best Practices
- **Start Small** - Test with 10-20 cars first
- **Unique IDs** - Ensure car_id values are unique
- **Image URLs** - Use accessible image URLs
- **Data Validation** - Check required fields are filled
- **Backup First** - Always backup before large imports

### Performance Optimization
- **Use Indexes** - The schema includes performance indexes
- **Batch Imports** - Import in batches of 100-500 cars
- **Monitor Database** - Watch database performance during imports
- **Clean Data** - Remove duplicates and invalid entries

### Troubleshooting
- **Check Logs** - Import errors are logged in detail
- **Validate CSV** - Ensure proper CSV format
- **Database Connection** - Verify environment variables
- **Admin Password** - Ensure correct admin authentication

## 🎉 You're Ready!

You now have a professional, scalable car inventory system that can handle:
- ✅ Hundreds or thousands of cars
- ✅ Bulk CSV imports in minutes
- ✅ Advanced search and filtering
- ✅ Professional admin interface
- ✅ Import tracking and error handling

No more manual uploads one by one! 🚗💨
