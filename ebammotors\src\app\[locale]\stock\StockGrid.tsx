'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Eye, Heart, ShoppingCart } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

interface StockItem {
  id: number;
  category: string;
  title: string;
  price: string;
  location: string;
  status: string;
  image: string;
  images: string[];
  specs: string[];
  carId: string;
}

interface StockGridProps {
  items: StockItem[];
  locale: string;
  filteredCategory: string;
  searchTerm?: string;
}

export default function StockGrid({ items, locale }: StockGridProps) {
  const [selectedItem, setSelectedItem] = useState<StockItem | null>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState<{ [key: number]: number }>({});
  const { user, isFavorite, addToFavorites, removeFromFavorites } = useAuth();

  // Items are already filtered in StockWrapper, so we use them directly
  const filteredItems = items;

  // Auto-rotate images for each item
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImageIndex(prev => {
        const newIndex = { ...prev };
        filteredItems.forEach(item => {
          if (item.images && item.images.length > 1) {
            const currentIndex = newIndex[item.id] || 0;
            newIndex[item.id] = (currentIndex + 1) % item.images.length;
          }
        });
        return newIndex;
      });
    }, 3000); // Change image every 3 seconds

    return () => clearInterval(interval);
  }, [filteredItems]);

  const handleDetailsClick = (item: StockItem) => {
    setSelectedItem(item);
  };

  const handleFavoriteClick = (e: React.MouseEvent, item: StockItem) => {
    e.stopPropagation();
    if (!user) {
      // Could show login modal here
      alert(locale === 'en' ? 'Please sign in to add favorites' : 'お気に入りに追加するにはサインインしてください');
      return;
    }

    if (isFavorite(item.carId)) {
      removeFromFavorites(item.carId);
    } else {
      addToFavorites(item.carId);
    }
  };

  const closeModal = () => {
    setSelectedItem(null);
  };

  // Color mapping based on actual car images
  const colorMap: { [key: string]: { en: string; ja: string } } = {
    // Toyota Voxy 2010
    'toyota_voxy_2010_001.jpg': { en: 'Silver Metallic', ja: 'シルバーメタリック' },
    'toyota_voxy_2010_002.jpg': { en: 'Dark Blue Metallic', ja: 'ダークブルーメタリック' },
    'toyota_voxy_2010_003.jpg': { en: 'White Pearl', ja: 'ホワイトパール' },
    'toyota_voxy_2010_004.jpg': { en: 'Black Metallic', ja: 'ブラックメタリック' },
    'toyota_voxy_2010_005.jpg': { en: 'Gray Metallic', ja: 'グレーメタリック' },
    'toyota_voxy_2010_006.jpg': { en: 'Red Metallic', ja: 'レッドメタリック' },
    'toyota_voxy_2010_007.jpg': { en: 'Blue Metallic', ja: 'ブルーメタリック' },
    
    // Toyota Voxy 2012
    'toyota_voxy_2012_001.jpg': { en: 'Pearl White', ja: 'パールホワイト' },
    'toyota_voxy_2012_002.jpg': { en: 'Attitude Black Mica', ja: 'アティチュードブラックマイカ' },
    'toyota_voxy_2012_003.jpg': { en: 'Silver Metallic', ja: 'シルバーメタリック' },
    'toyota_voxy_2012_004.jpg': { en: 'Dark Purple Mica Metallic', ja: 'ダークパープルマイカメタリック' },
    'toyota_voxy_2012_005.jpg': { en: 'Bordeaux Red Mica Metallic', ja: 'ボルドーレッドマイカメタリック' },
    
    // Toyota Noah 2010
    'toyota_noah_2010_001.jpg': { en: 'Super White II', ja: 'スーパーホワイトII' },
    'toyota_noah_2010_002.jpg': { en: 'Silver Metallic', ja: 'シルバーメタリック' },
    'toyota_noah_2010_003.jpg': { en: 'Black Mica', ja: 'ブラックマイカ' },
    'toyota_noah_2010_004.jpg': { en: 'Dark Blue Mica Metallic', ja: 'ダークブルーマイカメタリック' },
    
    // Toyota Sienta 2014
    'toyota_sienta_2014_001.jpg': { en: 'White Pearl Crystal Shine', ja: 'ホワイトパールクリスタルシャイン' },
    'toyota_sienta_2014_002.jpg': { en: 'Silver Metallic', ja: 'シルバーメタリック' },
    'toyota_sienta_2014_003.jpg': { en: 'Black Mica', ja: 'ブラックマイカ' },
    'toyota_sienta_2014_004.jpg': { en: 'Blue Metallic', ja: 'ブルーメタリック' },
    
    // Toyota Vitz 2014
    'toyota_vitz_2014_001.jpg': { en: 'Super White II', ja: 'スーパーホワイトII' },
    'toyota_vitz_2014_002.jpg': { en: 'Silver Metallic', ja: 'シルバーメタリック' },
    'toyota_vitz_2014_003.jpg': { en: 'Black Mica', ja: 'ブラックマイカ' },
    'toyota_vitz_2014_004.jpg': { en: 'Red Mica Metallic', ja: 'レッドマイカメタリック' },
    
    // Toyota Yaris 2015
    'toyota_yaris_2015_001.jpg': { en: 'White Pearl Crystal Shine', ja: 'ホワイトパールクリスタルシャイン' },
    'toyota_yaris_2015_002.jpg': { en: 'Silver Metallic', ja: 'シルバーメタリック' },
    'toyota_yaris_2015_003.jpg': { en: 'Attitude Black Mica', ja: 'アティチュードブラックマイカ' },
    'toyota_yaris_2015_004.jpg': { en: 'Blue Mica Metallic', ja: 'ブルーマイカメタリック' }
  };

  const getColorFromImage = (imagePath: string): { en: string; ja: string } => {
    const filename = imagePath.split('/').pop() || '';
    return colorMap[filename] || { en: 'Standard Color', ja: '標準色' };
  };

  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {filteredItems.map((item) => (
          <div
            key={item.id}
            className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group cursor-pointer transform hover:scale-105"
            onClick={() => handleDetailsClick(item)}
          >
            <div className="relative overflow-hidden" onClick={() => handleDetailsClick(item)}>
              <div className="h-48 relative overflow-hidden">
                <Image
                  src={item.images && item.images.length > 0
                    ? item.images[currentImageIndex[item.id] || 0]
                    : item.image}
                  alt={item.title}
                  fill
                  className="object-cover group-hover:scale-110 transition-all duration-500 ease-out"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                />
                {/* Overlay effect on hover */}
                <div className="absolute inset-0 bg-black opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>

                {/* Image counter indicator */}
                {item.images && item.images.length > 1 && (
                  <div className="absolute bottom-2 right-2 bg-black/70 text-white px-2 py-1 rounded-full text-xs">
                    {(currentImageIndex[item.id] || 0) + 1}/{item.images.length}
                  </div>
                )}
              </div>
              {/* Special badge for Toyota Voxy */}
              {item.title.includes('Toyota Voxy') && (
                <div className="absolute top-4 left-4 px-2 py-1 bg-primary-600 text-white text-xs font-semibold rounded-full">
                  8-Seater
                </div>
              )}
              <div className="absolute top-4 right-4 flex items-center space-x-2">
                <button
                  onClick={(e) => handleFavoriteClick(e, item)}
                  className={`p-2 rounded-full transition-colors ${
                    user && isFavorite(item.carId)
                      ? 'bg-red-100 text-red-600 hover:bg-red-200'
                      : 'bg-white/80 text-neutral-600 hover:bg-white hover:text-red-600'
                  }`}
                  title={locale === 'en' ? 'Add to favorites' : 'お気に入りに追加'}
                >
                  <Heart className={`w-4 h-4 ${user && isFavorite(item.carId) ? 'fill-current' : ''}`} />
                </button>
                <div className={`px-3 py-1 rounded-full text-xs font-semibold ${
                  item.status === 'Available' ? 'bg-green-100 text-green-800' :
                  item.status === 'Reserved' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-red-100 text-red-800'
                }`}>
                  {item.status}
                </div>
              </div>
            </div>

            <div className="p-6">
              <h3 className="text-xl font-semibold text-neutral-800 mb-2 group-hover:text-primary-600 transition-colors duration-200">
                {item.title}
              </h3>
              <p className="text-2xl font-bold text-primary-600 mb-3">{item.price}</p>
              <div className="flex flex-wrap gap-2 mb-4">
                {item.specs.map((spec, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-neutral-100 text-neutral-600 text-xs rounded-full"
                  >
                    {spec}
                  </span>
                ))}
              </div>
              <div className="flex items-center justify-between mb-4">
                <span className="text-sm text-neutral-500">📍 {item.location}</span>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDetailsClick(item);
                    }}
                    className="text-neutral-600 hover:text-primary-600 transition-colors duration-200"
                    title={locale === 'en' ? 'Quick View' : 'クイックビュー'}
                  >
                    <Eye className="w-4 h-4" />
                  </button>
                  <Link
                    href={`/${locale}/stock/${item.carId}`}
                    onClick={(e) => e.stopPropagation()}
                    className="text-primary-600 hover:text-primary-700 font-medium text-sm transition-colors duration-200"
                  >
                    {locale === 'en' ? 'Details' : '詳細'}
                  </Link>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2">
                <Link
                  href={`/${locale}/stock/${item.carId}?quickbuy=true`}
                  onClick={(e) => e.stopPropagation()}
                  className="flex-1 bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-semibold text-sm transition-all duration-200 flex items-center justify-center space-x-2"
                >
                  <ShoppingCart className="w-4 h-4" />
                  <span>{locale === 'en' ? 'Quick Buy' : 'クイック購入'}</span>
                </Link>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDetailsClick(item);
                  }}
                  className="px-4 py-2 border border-neutral-300 text-neutral-700 rounded-lg font-semibold text-sm hover:bg-neutral-50 transition-all duration-200 flex items-center space-x-1"
                >
                  <Eye className="w-4 h-4" />
                  <span>{locale === 'en' ? 'View' : '表示'}</span>
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredItems.length === 0 && (
        <div className="text-center py-12">
          <p className="text-neutral-500 text-lg">
            {locale === 'en' ? 'No items found in this category.' : 'このカテゴリーには商品がありません。'}
          </p>
        </div>
      )}

      {/* Modal */}
      {selectedItem && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto animate-slideInUp">
            <div className="sticky top-0 bg-white border-b border-neutral-200 px-4 sm:px-6 py-4 flex items-center justify-between">
              <h2 className="text-xl sm:text-2xl font-heading font-bold text-neutral-800">
                {selectedItem.title}
              </h2>
              <button
                onClick={closeModal}
                className="text-neutral-500 hover:text-neutral-700 text-2xl sm:text-3xl transition-all duration-300 hover:scale-110 hover:rotate-90 p-1"
              >
                ×
              </button>
            </div>

            <div className="p-4 sm:p-6 space-y-6">
              {/* Image and Basic Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                <div className="relative h-48 sm:h-64 rounded-lg overflow-hidden animate-slideInLeft group">
                  <Image
                    src={selectedItem.image}
                    alt={selectedItem.title}
                    fill
                    className="object-cover transition-transform duration-500 group-hover:scale-110"
                    sizes="(max-width: 768px) 100vw, 50vw"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>

                <div className="space-y-4 animate-slideInRight">
                  <div>
                    <h3 className="text-lg sm:text-xl font-semibold text-neutral-800 mb-2">
                      {selectedItem.title}
                    </h3>
                    <p className="text-2xl sm:text-3xl font-bold text-primary-600 mb-4">
                      {selectedItem.price}
                    </p>
                  </div>

                  <div className="flex flex-wrap gap-2">
                    {selectedItem.specs.map((spec, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 bg-primary-100 text-primary-700 text-sm rounded-full font-medium"
                      >
                        {spec}
                      </span>
                    ))}
                  </div>

                  <div className="flex items-center justify-between pt-4">
                    <span className="text-neutral-600">📍 {selectedItem.location}</span>
                    <span className={`px-3 py-1 rounded-full text-sm font-semibold ${
                      selectedItem.status === 'Available' ? 'bg-green-100 text-green-800' :
                      selectedItem.status === 'Reserved' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {selectedItem.status}
                    </span>
                  </div>
                </div>
              </div>

              {/* Additional Details for Cars */}
              {selectedItem.category === 'cars' && (
                <div className="border-t border-neutral-200 pt-6">
                  <h4 className="text-lg font-semibold text-neutral-800 mb-4">
                    {locale === 'en' ? 'Vehicle Details' : '車両詳細'}
                  </h4>
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                      <tbody>
                        <tr className="border-b border-neutral-200">
                          <td className="bg-neutral-100 px-2 sm:px-3 py-2 font-medium text-neutral-700 w-1/3">
                            {locale === 'en' ? 'Stock ID' : '在庫ID'}
                          </td>
                          <td className="px-2 sm:px-3 py-2 text-neutral-800">EBM{selectedItem.id.toString().padStart(6, '0')}</td>
                        </tr>
                        <tr className="border-b border-neutral-200">
                          <td className="bg-neutral-100 px-2 sm:px-3 py-2 font-medium text-neutral-700 w-1/3">
                            {locale === 'en' ? 'Color' : '色'}
                          </td>
                          <td className="px-2 sm:px-3 py-2 text-neutral-800">
                            {getColorFromImage(selectedItem.image)[locale as 'en' | 'ja']}
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              {/* Contact CTA */}
              <div className="border-t border-neutral-200 pt-6">
                <div className="bg-primary-50 rounded-lg p-4 text-center">
                  <h4 className="text-lg font-semibold text-primary-800 mb-2">
                    {locale === 'en' ? 'Interested in this item?' : 'この商品にご興味がありますか？'}
                  </h4>
                  <p className="text-primary-600 mb-4">
                    {locale === 'en'
                      ? 'Contact us for more details, pricing, and shipping information.'
                      : '詳細、価格、配送情報についてお問い合わせください。'
                    }
                  </p>
                  <div className="flex flex-col sm:flex-row gap-3 justify-center">
                    <a
                      href="https://whatsapp.com/channel/0029Vb5sDlFDTkK191T6Ut1G"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="bg-green-500 hover:bg-green-600 text-white px-6 py-2 rounded-lg font-semibold transition-colors duration-200 flex items-center justify-center"
                    >
                      <span className="mr-2">💬</span>
                      {locale === 'en' ? 'WhatsApp' : 'WhatsApp'}
                    </a>
                    <button
                      onClick={closeModal}
                      className="bg-neutral-200 hover:bg-neutral-300 text-neutral-700 px-6 py-2 rounded-lg font-semibold transition-colors duration-200"
                    >
                      {locale === 'en' ? 'Close' : '閉じる'}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
