import { 
  OrderConfirmationData, 
  ReviewNotificationData, 
  ContactFormData, 
  AdminNotificationData, 
  FollowUpData 
} from './resendService';

// Base email styles
const emailStyles = `
  <style>
    body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }
    .container { max-width: 600px; margin: 0 auto; background-color: white; }
    .header { background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); color: white; padding: 30px; text-align: center; }
    .logo { font-size: 28px; font-weight: bold; margin-bottom: 10px; }
    .content { padding: 30px; }
    .vehicle-card { border: 1px solid #e5e7eb; border-radius: 8px; padding: 20px; margin: 20px 0; }
    .button { display: inline-block; background: #1e40af; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }
    .footer { background-color: #f9fafb; padding: 20px; text-align: center; color: #6b7280; font-size: 14px; }
    .highlight { background-color: #eff6ff; padding: 15px; border-radius: 6px; margin: 15px 0; }
    .status-badge { display: inline-block; padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: bold; }
    .status-confirmed { background-color: #dcfce7; color: #166534; }
    .divider { height: 1px; background-color: #e5e7eb; margin: 20px 0; }
  </style>
`;

export class EmailTemplates {
  /**
   * Generate Order Confirmation HTML
   */
  static generateOrderConfirmationHTML(data: OrderConfirmationData): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Order Confirmation - ${data.orderNumber}</title>
        ${emailStyles}
      </head>
      <body>
        <div class="container">
          <!-- Header -->
          <div class="header">
            <div class="logo">🚗 EBAM Motors</div>
            <p>Your trusted partner for quality vehicles from Japan to Ghana</p>
          </div>

          <!-- Content -->
          <div class="content">
            <h1>Order Confirmation</h1>
            <p>Dear ${data.customerName},</p>
            <p>Thank you for your order! We're excited to help you get your new vehicle.</p>

            <div class="highlight">
              <h3>Order Details</h3>
              <p><strong>Order Number:</strong> ${data.orderNumber}</p>
              <p><strong>Order Date:</strong> ${data.orderDate}</p>
              <p><strong>Status:</strong> <span class="status-badge status-confirmed">Confirmed</span></p>
            </div>

            <!-- Vehicle Details -->
            <div class="vehicle-card">
              <h3>Vehicle Information</h3>
              <img src="${data.vehicle.image}" alt="${data.vehicle.title}" style="width: 100%; max-width: 300px; height: 200px; object-fit: cover; border-radius: 6px; margin-bottom: 15px;">
              <h4>${data.vehicle.title}</h4>
              <p><strong>Price:</strong> ${data.vehicle.price}</p>
            </div>

            <!-- Shipping Information -->
            <div class="highlight">
              <h3>Shipping Address</h3>
              <p>
                ${data.shippingAddress.street}<br>
                ${data.shippingAddress.city}, ${data.shippingAddress.state}<br>
                ${data.shippingAddress.country} ${data.shippingAddress.postalCode}
              </p>
              <p><strong>Estimated Delivery:</strong> ${data.estimatedDelivery}</p>
            </div>

            <!-- Order Summary -->
            <div class="divider"></div>
            <div style="text-align: right;">
              <h3>Order Total: ${data.total}</h3>
            </div>

            <!-- Next Steps -->
            <div class="highlight">
              <h3>What's Next?</h3>
              <ul>
                <li>We'll prepare your vehicle for shipping</li>
                <li>You'll receive tracking information once shipped</li>
                <li>Our team will contact you for any updates</li>
              </ul>
            </div>

            <div style="text-align: center; margin: 30px 0;">
              <a href="https://yourdomain.com/tracking?order=${data.orderNumber}" class="button">Track Your Order</a>
            </div>

            <p>If you have any questions, please don't hesitate to contact us:</p>
            <ul>
              <li>📧 Email: <EMAIL></li>
              <li>📱 WhatsApp: +233245375692</li>
              <li>📍 Location: Kumasi, Ghana</li>
            </ul>
          </div>

          <!-- Footer -->
          <div class="footer">
            <p>Thank you for choosing EBAM Motors!</p>
            <p>© 2024 EBAM Motors. All rights reserved.</p>
            <p>This email was sent to confirm your order. Please keep this for your records.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Generate Order Confirmation Text
   */
  static generateOrderConfirmationText(data: OrderConfirmationData): string {
    return `
ORDER CONFIRMATION - EBAM Motors

Dear ${data.customerName},

Thank you for your order! We're excited to help you get your new vehicle.

ORDER DETAILS:
- Order Number: ${data.orderNumber}
- Order Date: ${data.orderDate}
- Status: Confirmed

VEHICLE:
- ${data.vehicle.title}
- Price: ${data.vehicle.price}

SHIPPING ADDRESS:
${data.shippingAddress.street}
${data.shippingAddress.city}, ${data.shippingAddress.state}
${data.shippingAddress.country} ${data.shippingAddress.postalCode}

Estimated Delivery: ${data.estimatedDelivery}

ORDER TOTAL: ${data.total}

WHAT'S NEXT:
- We'll prepare your vehicle for shipping
- You'll receive tracking information once shipped
- Our team will contact you for any updates

Track your order: https://yourdomain.com/tracking?order=${data.orderNumber}

CONTACT US:
- Email: <EMAIL>
- WhatsApp: +233245375692
- Location: Kumasi, Ghana

Thank you for choosing EBAM Motors!
© 2024 EBAM Motors. All rights reserved.
    `;
  }

  /**
   * Generate Review Notification HTML for Admin
   */
  static generateReviewNotificationHTML(data: ReviewNotificationData): string {
    const stars = '⭐'.repeat(data.rating) + '☆'.repeat(5 - data.rating);
    
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>New Review Submitted</title>
        ${emailStyles}
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">🚗 EBAM Motors Admin</div>
            <p>New Review Notification</p>
          </div>

          <div class="content">
            <h1>New Review Submitted</h1>
            
            <div class="highlight">
              <h3>Review Details</h3>
              <p><strong>Customer:</strong> ${data.customerName}</p>
              <p><strong>Vehicle:</strong> ${data.vehicleTitle}</p>
              <p><strong>Rating:</strong> ${stars} (${data.rating}/5)</p>
              <p><strong>Date:</strong> ${data.reviewDate}</p>
            </div>

            <div class="vehicle-card">
              <h3>Review Content</h3>
              <p>"${data.review}"</p>
            </div>

            <div style="text-align: center; margin: 30px 0;">
              <a href="https://yourdomain.com/admin/reviews" class="button">Review & Approve</a>
            </div>
          </div>

          <div class="footer">
            <p>EBAM Motors Admin Panel</p>
            <p>Please review and approve/reject this review in the admin panel.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Generate Review Approval HTML for Customer
   */
  static generateReviewApprovalHTML(data: ReviewNotificationData): string {
    const stars = '⭐'.repeat(data.rating) + '☆'.repeat(5 - data.rating);
    
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Review Approved</title>
        ${emailStyles}
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">🚗 EBAM Motors</div>
            <p>Thank you for your feedback!</p>
          </div>

          <div class="content">
            <h1>Your Review Has Been Approved!</h1>
            <p>Dear ${data.customerName},</p>
            <p>Thank you for taking the time to review your experience with us. Your review has been approved and is now live on our website!</p>

            <div class="highlight">
              <h3>Your Review</h3>
              <p><strong>Vehicle:</strong> ${data.vehicleTitle}</p>
              <p><strong>Rating:</strong> ${stars} (${data.rating}/5)</p>
              <p><strong>Review:</strong> "${data.review}"</p>
            </div>

            <p>Your feedback helps other customers make informed decisions and helps us improve our services.</p>

            <div style="text-align: center; margin: 30px 0;">
              <a href="https://yourdomain.com/reviews" class="button">View All Reviews</a>
            </div>
          </div>

          <div class="footer">
            <p>Thank you for choosing EBAM Motors!</p>
            <p>© 2024 EBAM Motors. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Generate Contact Form Admin Notification HTML
   */
  static generateContactFormAdminHTML(data: ContactFormData): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>New Contact Form Submission</title>
        ${emailStyles}
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">🚗 EBAM Motors Admin</div>
            <p>New Contact Form Submission</p>
          </div>

          <div class="content">
            <h1>New Contact Form Submission</h1>
            
            <div class="highlight">
              <h3>Contact Details</h3>
              <p><strong>Name:</strong> ${data.name}</p>
              <p><strong>Email:</strong> ${data.email}</p>
              <p><strong>Subject:</strong> ${data.subject}</p>
              <p><strong>Submitted:</strong> ${data.submissionDate}</p>
            </div>

            <div class="vehicle-card">
              <h3>Message</h3>
              <p>${data.message.replace(/\n/g, '<br>')}</p>
            </div>

            <div style="text-align: center; margin: 30px 0;">
              <a href="mailto:${data.email}?subject=Re: ${data.subject}" class="button">Reply to Customer</a>
            </div>
          </div>

          <div class="footer">
            <p>EBAM Motors Admin Panel</p>
            <p>Please respond to this customer inquiry promptly.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Generate Abandoned Cart Follow-up HTML
   */
  static generateAbandonedCartHTML(data: FollowUpData): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Complete Your Purchase</title>
        ${emailStyles}
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">🚗 EBAM Motors</div>
            <p>Don't miss out on your perfect vehicle!</p>
          </div>

          <div class="content">
            <h1>Complete Your Purchase</h1>
            <p>Hi ${data.customerName},</p>
            <p>We noticed you were interested in some amazing vehicles but didn't complete your purchase. Don't worry - we've saved your items!</p>

            <div class="highlight">
              <h3>Your Saved Items</h3>
              ${data.data?.items?.map((item: any) => `
                <div class="vehicle-card">
                  <h4>${item.title}</h4>
                  <p><strong>Price:</strong> ${item.price}</p>
                  <p>Quantity: ${item.quantity}</p>
                </div>
              `).join('') || '<p>Your selected vehicles are waiting for you!</p>'}
            </div>

            <div style="text-align: center; margin: 30px 0;">
              <a href="https://yourdomain.com/stock" class="button">Complete Your Purchase</a>
            </div>

            <div class="highlight">
              <h3>Why Choose EBAM Motors?</h3>
              <ul>
                <li>✅ Quality guaranteed vehicles from Japan</li>
                <li>✅ Competitive pricing with transparent costs</li>
                <li>✅ Reliable shipping to Ghana and Africa</li>
                <li>✅ Expert support throughout the process</li>
              </ul>
            </div>

            <p>Need help deciding? Our team is here to assist you:</p>
            <ul>
              <li>📱 WhatsApp: +233245375692</li>
              <li>📧 Email: <EMAIL></li>
            </ul>
          </div>

          <div class="footer">
            <p>This offer won't last forever!</p>
            <p>© 2024 EBAM Motors. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Generate Delivery Update HTML
   */
  static generateDeliveryUpdateHTML(data: FollowUpData): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Delivery Update</title>
        ${emailStyles}
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">🚗 EBAM Motors</div>
            <p>Your vehicle is on its way!</p>
          </div>

          <div class="content">
            <h1>Delivery Update</h1>
            <p>Hi ${data.customerName},</p>
            <p>Great news! We have an update on your vehicle delivery.</p>

            <div class="highlight">
              <h3>Delivery Status</h3>
              <p><strong>Current Status:</strong> ${data.data?.status || 'In Transit'}</p>
              <p><strong>Location:</strong> ${data.data?.location || 'En route to destination'}</p>
              <p><strong>Estimated Arrival:</strong> ${data.data?.estimatedArrival || 'To be confirmed'}</p>
            </div>

            <div style="text-align: center; margin: 30px 0;">
              <a href="https://yourdomain.com/tracking?order=${data.data?.orderId}" class="button">Track Your Order</a>
            </div>

            <div class="vehicle-card">
              <h3>What to Expect Next</h3>
              <ul>
                <li>We'll notify you 24 hours before delivery</li>
                <li>Our delivery team will contact you directly</li>
                <li>Ensure someone is available to receive the vehicle</li>
                <li>Have your ID and order confirmation ready</li>
              </ul>
            </div>
          </div>

          <div class="footer">
            <p>Thank you for choosing EBAM Motors!</p>
            <p>© 2024 EBAM Motors. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Generate Feedback Request HTML
   */
  static generateFeedbackRequestHTML(data: FollowUpData): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>How was your experience?</title>
        ${emailStyles}
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">🚗 EBAM Motors</div>
            <p>We'd love to hear from you!</p>
          </div>

          <div class="content">
            <h1>How Was Your Experience?</h1>
            <p>Hi ${data.customerName},</p>
            <p>We hope you're enjoying your new vehicle! Your feedback helps us improve our services and helps other customers make informed decisions.</p>

            <div style="text-align: center; margin: 30px 0;">
              <a href="https://yourdomain.com/reviews" class="button">Leave a Review</a>
            </div>

            <div class="highlight">
              <h3>Share Your Experience</h3>
              <p>Tell us about:</p>
              <ul>
                <li>🚗 Vehicle quality and condition</li>
                <li>📦 Shipping and delivery experience</li>
                <li>👥 Customer service quality</li>
                <li>💰 Value for money</li>
                <li>🌟 Overall satisfaction</li>
              </ul>
            </div>

            <div class="vehicle-card">
              <h3>Why Your Review Matters</h3>
              <ul>
                <li>Helps other customers make confident decisions</li>
                <li>Helps us improve our services</li>
                <li>Builds trust in our community</li>
                <li>Takes less than 2 minutes to complete</li>
              </ul>
            </div>

            <p>As a thank you, customers who leave reviews get priority support and exclusive offers!</p>
          </div>

          <div class="footer">
            <p>Thank you for choosing EBAM Motors!</p>
            <p>© 2024 EBAM Motors. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Generate Contact Form Customer Confirmation HTML
   */
  static generateContactFormCustomerHTML(data: ContactFormData): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Thank you for contacting us</title>
        ${emailStyles}
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">🚗 EBAM Motors</div>
            <p>Thank you for reaching out!</p>
          </div>

          <div class="content">
            <h1>Thank You for Contacting Us!</h1>
            <p>Dear ${data.name},</p>
            <p>We've received your message and appreciate you taking the time to contact us. Our team will review your inquiry and respond within 24 hours.</p>

            <div class="highlight">
              <h3>Your Message Summary</h3>
              <p><strong>Subject:</strong> ${data.subject}</p>
              <p><strong>Submitted:</strong> ${data.submissionDate}</p>
              <p><strong>Reference ID:</strong> #${Date.now().toString().slice(-6)}</p>
            </div>

            <div class="vehicle-card">
              <h3>What to Expect Next</h3>
              <ul>
                <li>Our team will review your inquiry</li>
                <li>You'll receive a response within 24 hours</li>
                <li>For urgent matters, contact us on WhatsApp</li>
              </ul>
            </div>

            <p>In the meantime, feel free to:</p>
            <ul>
              <li>Browse our latest vehicle inventory</li>
              <li>Check out customer reviews</li>
              <li>Learn more about our services</li>
            </ul>

            <div style="text-align: center; margin: 30px 0;">
              <a href="https://yourdomain.com/stock" class="button">Browse Vehicles</a>
            </div>

            <p><strong>Need immediate assistance?</strong></p>
            <ul>
              <li>📱 WhatsApp: +233245375692</li>
              <li>📧 Email: <EMAIL></li>
              <li>📍 Location: Kumasi, Ghana</li>
            </ul>
          </div>

          <div class="footer">
            <p>Thank you for choosing EBAM Motors!</p>
            <p>© 2024 EBAM Motors. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }
}
