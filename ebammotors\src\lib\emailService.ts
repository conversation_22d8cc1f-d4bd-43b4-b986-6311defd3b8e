// Email service for automated follow-ups and notifications
// Note: This is a mock implementation. In production, integrate with services like:
// - SendGrid, Mailgun, AWS SES, or similar email service providers

interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  htmlBody: string;
  textBody: string;
  variables: string[];
}

interface EmailData {
  to: string;
  subject: string;
  htmlBody: string;
  textBody: string;
  from?: string;
  replyTo?: string;
  attachments?: Array<{
    filename: string;
    content: string;
    contentType: string;
  }>;
}

// Email templates for different scenarios
const EMAIL_TEMPLATES: Record<string, EmailTemplate> = {
  abandoned_cart: {
    id: 'abandoned_cart',
    name: 'Abandoned Cart Reminder',
    subject: 'Complete Your Purchase - {{customerName}}',
    htmlBody: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">Don't Miss Out on Your Selected Vehicle!</h2>
        <p>Hi {{customerName}},</p>
        <p>We noticed you were interested in purchasing a vehicle from EBAM Motors but didn't complete your order.</p>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>Your Selected Vehicle:</h3>
          <p><strong>{{vehicleTitle}}</strong></p>
          <p>Price: <strong>¥{{vehiclePrice}}</strong></p>
        </div>
        <p>Complete your purchase now to secure this vehicle before it's sold to someone else!</p>
        <a href="{{checkoutUrl}}" style="background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">Complete Purchase</a>
        <p style="margin-top: 30px; color: #666; font-size: 14px;">
          If you have any questions, feel free to contact <NAME_EMAIL> or +233245375692
        </p>
      </div>
    `,
    textBody: `Hi {{customerName}},

We noticed you were interested in purchasing {{vehicleTitle}} for ¥{{vehiclePrice}} but didn't complete your order.

Complete your purchase now: {{checkoutUrl}}

Contact us: <EMAIL> or +233245375692`,
    variables: ['customerName', 'vehicleTitle', 'vehiclePrice', 'checkoutUrl']
  },

  order_delivered: {
    id: 'order_delivered',
    name: 'Order Delivered Follow-up',
    subject: 'How was your recent purchase? - EBAM Motors',
    htmlBody: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">Thank You for Your Purchase!</h2>
        <p>Hi {{customerName}},</p>
        <p>We hope you're enjoying your recent vehicle purchase from EBAM Motors!</p>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>Your Order:</h3>
          <p><strong>{{vehicleTitle}}</strong></p>
          <p>Order #: {{orderNumber}}</p>
          <p>Delivered: {{deliveryDate}}</p>
        </div>
        <p>We'd love to hear about your experience. Would you mind leaving us a review?</p>
        <a href="{{reviewUrl}}" style="background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">Leave a Review</a>
        <p style="margin-top: 20px;">Also, don't forget to refer friends and family - you'll earn loyalty points for each successful referral!</p>
        <p style="margin-top: 30px; color: #666; font-size: 14px;">
          Need support? Contact <NAME_EMAIL> or +233245375692
        </p>
      </div>
    `,
    textBody: `Hi {{customerName}},

Thank you for purchasing {{vehicleTitle}} (Order #{{orderNumber}}).

We'd love to hear about your experience. Leave a review: {{reviewUrl}}

Contact us: <EMAIL> or +233245375692`,
    variables: ['customerName', 'vehicleTitle', 'orderNumber', 'deliveryDate', 'reviewUrl']
  },

  customer_reengagement: {
    id: 'customer_reengagement',
    name: 'Customer Re-engagement',
    subject: 'We Miss You! Check Out Our Latest Vehicles',
    htmlBody: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">We Miss You at EBAM Motors!</h2>
        <p>Hi {{customerName}},</p>
        <p>It's been a while since your last visit, and we wanted to reach out with some exciting updates!</p>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>What's New:</h3>
          <ul>
            <li>Fresh vehicle arrivals from Japan</li>
            <li>Special pricing on popular models</li>
            <li>Enhanced shipping options to Ghana</li>
            <li>New loyalty rewards program</li>
          </ul>
        </div>
        <p>Browse our latest stock and find your perfect vehicle today!</p>
        <a href="{{stockUrl}}" style="background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">View Latest Stock</a>
        <p style="margin-top: 30px; color: #666; font-size: 14px;">
          Questions? Contact <NAME_EMAIL> or +233245375692
        </p>
      </div>
    `,
    textBody: `Hi {{customerName}},

We miss you at EBAM Motors! Check out our latest vehicle arrivals and special offers.

Browse our stock: {{stockUrl}}

Contact us: <EMAIL> or +233245375692`,
    variables: ['customerName', 'stockUrl']
  },

  lead_followup: {
    id: 'lead_followup',
    name: 'Lead Follow-up',
    subject: 'Following up on your inquiry - EBAM Motors',
    htmlBody: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">Thank You for Your Inquiry!</h2>
        <p>Hi {{customerName}},</p>
        <p>Thank you for reaching out to EBAM Motors regarding {{inquirySubject}}.</p>
        <p>We wanted to follow up and see if you have any additional questions or if there's anything specific we can help you with.</p>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>Your Inquiry:</h3>
          <p>{{inquiryMessage}}</p>
          <p><em>Submitted: {{inquiryDate}}</em></p>
        </div>
        <p>Our team is ready to assist you with:</p>
        <ul>
          <li>Vehicle recommendations based on your needs</li>
          <li>Pricing and financing options</li>
          <li>Shipping arrangements to Ghana</li>
          <li>Documentation and import procedures</li>
        </ul>
        <a href="{{contactUrl}}" style="background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">Contact Us</a>
        <p style="margin-top: 30px; color: #666; font-size: 14px;">
          Direct contact: <EMAIL> or +233245375692
        </p>
      </div>
    `,
    textBody: `Hi {{customerName}},

Thank you for your inquiry about {{inquirySubject}}.

Your message: {{inquiryMessage}}

We're here to help with vehicle recommendations, pricing, shipping, and documentation.

Contact us: {{contactUrl}} or <EMAIL> or +233245375692`,
    variables: ['customerName', 'inquirySubject', 'inquiryMessage', 'inquiryDate', 'contactUrl']
  }
};

/**
 * Replace template variables with actual values
 */
function replaceTemplateVariables(template: string, variables: Record<string, string>): string {
  let result = template;
  Object.entries(variables).forEach(([key, value]) => {
    const regex = new RegExp(`{{${key}}}`, 'g');
    result = result.replace(regex, value || '');
  });
  return result;
}

/**
 * Get email template by ID
 */
export function getEmailTemplate(templateId: string): EmailTemplate | null {
  return EMAIL_TEMPLATES[templateId] || null;
}

/**
 * Prepare email from template
 */
export function prepareEmailFromTemplate(
  templateId: string, 
  variables: Record<string, string>,
  recipientEmail: string
): EmailData | null {
  const template = getEmailTemplate(templateId);
  if (!template) return null;

  return {
    to: recipientEmail,
    subject: replaceTemplateVariables(template.subject, variables),
    htmlBody: replaceTemplateVariables(template.htmlBody, variables),
    textBody: replaceTemplateVariables(template.textBody, variables),
    from: 'EBAM Motors <<EMAIL>>',
    replyTo: '<EMAIL>',
  };
}

/**
 * Send email (mock implementation)
 * In production, integrate with actual email service
 */
export async function sendEmail(emailData: EmailData): Promise<{ success: boolean; messageId?: string; error?: string }> {
  try {
    // Mock email sending - replace with actual email service integration
    console.log('📧 Email would be sent:', {
      to: emailData.to,
      subject: emailData.subject,
      from: emailData.from,
    });

    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Mock success response
    return {
      success: true,
      messageId: `mock_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };

    /* 
    // Example integration with SendGrid:
    const sgMail = require('@sendgrid/mail');
    sgMail.setApiKey(process.env.SENDGRID_API_KEY);
    
    const msg = {
      to: emailData.to,
      from: emailData.from,
      subject: emailData.subject,
      text: emailData.textBody,
      html: emailData.htmlBody,
    };
    
    const response = await sgMail.send(msg);
    return { success: true, messageId: response[0].headers['x-message-id'] };
    */

  } catch (error) {
    console.error('Email sending failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Send abandoned cart email
 */
export async function sendAbandonedCartEmail(
  customerEmail: string,
  customerName: string,
  vehicleTitle: string,
  vehiclePrice: number,
  checkoutUrl: string
): Promise<boolean> {
  const emailData = prepareEmailFromTemplate('abandoned_cart', {
    customerName,
    vehicleTitle,
    vehiclePrice: vehiclePrice.toLocaleString(),
    checkoutUrl
  }, customerEmail);

  if (!emailData) return false;

  const result = await sendEmail(emailData);
  return result.success;
}

/**
 * Send order delivered follow-up email
 */
export async function sendOrderDeliveredEmail(
  customerEmail: string,
  customerName: string,
  vehicleTitle: string,
  orderNumber: string,
  deliveryDate: string,
  reviewUrl: string
): Promise<boolean> {
  const emailData = prepareEmailFromTemplate('order_delivered', {
    customerName,
    vehicleTitle,
    orderNumber,
    deliveryDate,
    reviewUrl
  }, customerEmail);

  if (!emailData) return false;

  const result = await sendEmail(emailData);
  return result.success;
}

/**
 * Send customer re-engagement email
 */
export async function sendReengagementEmail(
  customerEmail: string,
  customerName: string,
  stockUrl: string
): Promise<boolean> {
  const emailData = prepareEmailFromTemplate('customer_reengagement', {
    customerName,
    stockUrl
  }, customerEmail);

  if (!emailData) return false;

  const result = await sendEmail(emailData);
  return result.success;
}

/**
 * Send lead follow-up email
 */
export async function sendLeadFollowUpEmail(
  customerEmail: string,
  customerName: string,
  inquirySubject: string,
  inquiryMessage: string,
  inquiryDate: string,
  contactUrl: string
): Promise<boolean> {
  const emailData = prepareEmailFromTemplate('lead_followup', {
    customerName,
    inquirySubject,
    inquiryMessage,
    inquiryDate,
    contactUrl
  }, customerEmail);

  if (!emailData) return false;

  const result = await sendEmail(emailData);
  return result.success;
}
