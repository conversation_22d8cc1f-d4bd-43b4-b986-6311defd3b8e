import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAuth } from '@/lib/auth';

/**
 * Middleware to verify admin authentication for API routes
 */
export function withAdminAuth(handler: (request: NextRequest, context?: any) => Promise<NextResponse>) {
  return async (request: NextRequest, context?: any) => {
    try {
      // Get authentication from headers or cookies
      const authHeader = request.headers.get('authorization');
      const sessionId = request.cookies.get('admin_session')?.value;

      // Verify authentication
      const authResult = verifyAdminAuth(authHeader, sessionId);

      if (!authResult.isValid) {
        return NextResponse.json(
          { success: false, message: authResult.message },
          { status: 401 }
        );
      }

      // Add admin info to request headers for the handler
      const requestWithAuth = new NextRequest(request.url, {
        method: request.method,
        headers: {
          ...Object.fromEntries(request.headers.entries()),
          'x-admin-id': authResult.adminId || 'admin',
          'x-admin-authenticated': 'true'
        },
        body: request.body,
      });

      return handler(requestWithAuth, context);
    } catch (error) {
      console.error('Admin middleware error:', error);
      return NextResponse.json(
        { success: false, message: 'Authentication error' },
        { status: 500 }
      );
    }
  };
}

/**
 * Extract admin authentication from request
 */
export function getAdminFromRequest(request: NextRequest): { adminId: string; isAuthenticated: boolean } {
  const adminId = request.headers.get('x-admin-id') || 'admin';
  const isAuthenticated = request.headers.get('x-admin-authenticated') === 'true';

  return { adminId, isAuthenticated };
}

/**
 * Verify admin authentication for legacy API routes that use adminKey
 */
export function verifyLegacyAdminKey(adminKey: string): boolean {
  const validAdminKey = process.env.ADMIN_PASSWORD || 'admin123';
  return adminKey === validAdminKey;
}

/**
 * Get admin authentication from request (supports both new and legacy methods)
 */
export function getAdminAuth(request: NextRequest, body?: any): { isValid: boolean; adminId?: string; method: string } {
  // Try new authentication method first
  const authHeader = request.headers.get('authorization');
  const sessionId = request.cookies.get('admin_session')?.value;

  const authResult = verifyAdminAuth(authHeader, sessionId);
  if (authResult.isValid) {
    return {
      isValid: true,
      adminId: authResult.adminId,
      method: 'token/session'
    };
  }

  // Fall back to legacy adminKey method
  const adminKey = body?.adminKey || request.nextUrl.searchParams.get('adminKey');
  if (adminKey && verifyLegacyAdminKey(adminKey)) {
    return {
      isValid: true,
      adminId: 'admin',
      method: 'legacy'
    };
  }

  return {
    isValid: false,
    method: 'none'
  };
}
