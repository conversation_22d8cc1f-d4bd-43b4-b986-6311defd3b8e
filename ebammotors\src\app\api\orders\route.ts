import { NextRequest, NextResponse } from 'next/server';
import { createOrder, getAllOrders, getOrdersByCustomerId, updateOrder } from '@/lib/orderStorage';
import { generateAndEmailInvoice } from '@/lib/invoiceGenerator';
import { getPaymentMethodById, getShippingMethodById } from '@/lib/paymentConfig';
// Order type is imported but used in function signatures
// CRM Integration
import { syncCustomerFromOrder } from '../customers/route';
import { createInteraction } from '@/lib/crmStorage';
import { scheduleOrderDeliveryFollowUp } from '@/lib/followupScheduler';

export async function POST(request: NextRequest) {
  try {
    const orderData = await request.json();

    // Validate required fields
    if (!orderData.customerId || !orderData.vehicle || !orderData.payment || !orderData.shipping) {
      return NextResponse.json(
        { success: false, message: 'Missing required order data' },
        { status: 400 }
      );
    }

    // Validate payment method
    const paymentMethod = getPaymentMethodById(orderData.payment.method.id);
    if (!paymentMethod) {
      return NextResponse.json(
        { success: false, message: 'Invalid payment method' },
        { status: 400 }
      );
    }

    // Validate shipping method
    const shippingMethod = getShippingMethodById(orderData.shipping.method.id);
    if (!shippingMethod) {
      return NextResponse.json(
        { success: false, message: 'Invalid shipping method' },
        { status: 400 }
      );
    }

    // Create order
    const order = await createOrder({
      customerId: orderData.customerId,
      customerInfo: orderData.customerInfo,
      vehicle: orderData.vehicle,
      payment: {
        method: paymentMethod,
        amount: orderData.payment.amount,
        currency: orderData.payment.currency || 'JPY',
        status: 'pending',
        dueDate: orderData.payment.dueDate,
      },
      shipping: {
        method: shippingMethod,
        cost: orderData.shipping.cost,
        address: orderData.shipping.address,
        estimatedDelivery: orderData.shipping.estimatedDelivery,
        status: 'pending',
        updates: [],
      },
      status: 'pending_payment',
      totalAmount: orderData.totalAmount,
      currency: orderData.currency || 'JPY',
      notes: orderData.notes,
      invoiceGenerated: false,
    });

    // Generate invoice
    try {
      const invoice = await generateAndEmailInvoice(order);
      await updateOrder(order.id, {
        invoiceGenerated: true,
        invoiceUrl: invoice.pdfUrl
      });
    } catch (invoiceError) {
      console.error('Failed to generate invoice:', invoiceError);
      // Continue without failing the order creation
    }

    // CRM Integration: Sync customer data and log interaction
    try {
      const customer = await syncCustomerFromOrder(orderData);
      if (customer) {
        // Log order creation interaction
        await createInteraction({
          customerId: customer.id,
          type: 'order',
          direction: 'inbound',
          channel: 'website',
          content: `Order created: ${order.vehicle.title} for ¥${order.totalAmount.toLocaleString()}`,
          subject: 'New Order',
          tags: ['order_creation', 'purchase'],
          relatedOrderId: order.id,
          relatedProductId: order.vehicle.id,
          createdBy: customer.id,
        });
      }
    } catch (crmError) {
      console.error('CRM integration error:', crmError);
      // Continue without failing the order creation
    }

    return NextResponse.json({
      success: true,
      message: 'Order created successfully',
      order,
    });

  } catch (error) {
    console.error('Error creating order:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to create order' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const customerId = searchParams.get('customerId');
    const adminKey = searchParams.get('adminKey');

    // Admin access to all orders
    const validAdminKey = process.env.ADMIN_PASSWORD || 'admin123';
    if (adminKey === validAdminKey) {
      const allOrders = await getAllOrders();
      return NextResponse.json({ orders: allOrders });
    }

    // Customer access to their own orders
    if (customerId) {
      const orders = await getOrdersByCustomerId(customerId);
      return NextResponse.json({ orders });
    }

    return NextResponse.json(
      { success: false, message: 'Customer ID required' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Error fetching orders:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch orders' },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const { orderId, updates, adminKey } = await request.json();

    // Admin authentication
    const validAdminKey = process.env.ADMIN_PASSWORD || 'admin123';
    if (adminKey !== validAdminKey) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const success = await updateOrder(orderId, updates);

    if (!success) {
      return NextResponse.json(
        { success: false, message: 'Order not found' },
        { status: 404 }
      );
    }

    // CRM Integration: Log order status changes and schedule follow-ups
    try {
      const { getOrderById } = await import('@/lib/orderStorage');
      const order = await getOrderById(orderId);

      if (order) {
        const { getCustomerByEmail } = await import('@/lib/crmStorage');
        const customer = await getCustomerByEmail(order.customerInfo.email);

        if (customer) {
          // Log status change interaction
          await createInteraction({
            customerId: customer.id,
            type: 'order',
            direction: 'outbound',
            channel: 'website',
            content: `Order status updated: ${Object.keys(updates).map(key => `${key}: ${updates[key]}`).join(', ')}`,
            subject: 'Order Status Update',
            tags: ['order_update', 'status_change'],
            relatedOrderId: order.id,
            createdBy: 'admin',
          });

          // Schedule follow-up if order is delivered
          if (updates.status === 'delivered' || updates.shipping?.status === 'delivered') {
            await scheduleOrderDeliveryFollowUp(customer.id, order.id, {
              vehicleTitle: order.vehicle.title,
              orderNumber: order.orderNumber,
            });
          }
        }
      }
    } catch (crmError) {
      console.error('CRM integration error during order update:', crmError);
      // Continue without failing the order update
    }

    return NextResponse.json({
      success: true,
      message: 'Order updated successfully',
    });

  } catch (error) {
    console.error('Error updating order:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to update order' },
      { status: 500 }
    );
  }
}
