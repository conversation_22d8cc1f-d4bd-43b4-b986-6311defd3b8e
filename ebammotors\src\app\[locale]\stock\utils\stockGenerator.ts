import path from 'path';
import { CAR_MODEL_CONFIGS, getAdjustedCarPrice } from '@/lib/pricingConfig';

interface StockItem {
  id: number;
  category: string;
  title: string;
  price: string;
  location: string;
  status: string;
  image: string;
  images: string[]; // All images for this car
  specs: string[];
  carId: string; // Unique identifier for each car folder
  year?: number; // For filtering
  mileage?: number; // For filtering
  fuelType?: string; // For filtering
  transmission?: string; // For filtering
  bodyCondition?: string; // For filtering
  addedDate?: string; // For recently added section
  originalPrice?: string; // For price reduced section
  stockQuantity?: number; // For stock indicators
  popularity?: number; // For sorting
}

// Using centralized pricing configuration from @/lib/pricingConfig

function extractYearFromFolderName(folderName: string): number {
  const yearMatch = folderName.match(/(\d{4})/);
  return yearMatch ? parseInt(yearMatch[1]) : 2010;
}

function generateDeterministicMileage(seed: string): number {
  // Create a simple hash from the seed string for deterministic "randomness"
  let hash = 0;
  for (let i = 0; i < seed.length; i++) {
    const char = seed.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }

  // Use the hash to generate a deterministic value between 30000 and 120000
  const range = 120000 - 30000;
  const normalized = Math.abs(hash) / 2147483647; // Normalize to 0-1
  return Math.floor(normalized * range + 30000);
}

function generateBodyCondition(seed: string): string {
  const conditions = ['Excellent', 'Very Good', 'Good', 'Fair', 'Needs Work'];
  let hash = 0;
  for (let i = 0; i < seed.length; i++) {
    const char = seed.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash;
  }
  return conditions[Math.abs(hash) % conditions.length];
}

function getImageExtensions(): string[] {
  return ['.jpg', '.jpeg', '.JPG', '.JPEG', '.png', '.PNG'];
}

function isImageFile(filename: string): boolean {
  const ext = path.extname(filename);
  return getImageExtensions().includes(ext);
}

function generateFallbackStock(): StockItem[] {
  // Fallback stock data for client-side rendering or when file system is not available
  return [
    {
      id: 1,
      category: 'cars',
      title: 'Toyota Voxy 2015',
      price: '¥450,000',
      location: 'Japan',
      status: 'Available',
      image: '/car-models/voxy/voxy-2015-001/1.jpg',
      images: ['/car-models/voxy/voxy-2015-001/1.jpg', '/car-models/voxy/voxy-2015-001/2.jpg'],
      specs: ['8 seats', 'CVT', '85k km', 'Gasoline', 'Excellent'],
      carId: 'voxy-2015-001',
      year: 2015,
      mileage: 85000,
      fuelType: 'Gasoline',
      transmission: 'CVT',
      bodyCondition: 'Excellent'
    },
    {
      id: 2,
      category: 'cars',
      title: 'Toyota Noah 2016',
      price: '¥480,000',
      location: 'Japan',
      status: 'Available',
      image: '/car-models/noah/noah-2016-001/1.jpg',
      images: ['/car-models/noah/noah-2016-001/1.jpg', '/car-models/noah/noah-2016-001/2.jpg'],
      specs: ['8 seats', 'CVT', '72k km', 'Gasoline', 'Very Good'],
      carId: 'noah-2016-001',
      year: 2016,
      mileage: 72000,
      fuelType: 'Gasoline',
      transmission: 'CVT',
      bodyCondition: 'Very Good'
    },
    {
      id: 3,
      category: 'cars',
      title: 'Toyota Sienta 2017',
      price: '¥520,000',
      location: 'Japan',
      status: 'Available',
      image: '/car-models/sienta/sienta-2017-001/1.jpg',
      images: ['/car-models/sienta/sienta-2017-001/1.jpg', '/car-models/sienta/sienta-2017-001/2.jpg'],
      specs: ['7 seats', 'CVT', '58k km', 'Gasoline', 'Excellent'],
      carId: 'sienta-2017-001',
      year: 2017,
      mileage: 58000,
      fuelType: 'Gasoline',
      transmission: 'CVT',
      bodyCondition: 'Excellent'
    }
  ];
}

export async function generateStockFromFileSystem(): Promise<StockItem[]> {
  // Check if we're running on the server side
  if (typeof window !== 'undefined') {
    // We're on the client side, return empty array or fallback data
    return generateFallbackStock();
  }

  const stockItems: StockItem[] = [];
  const carModelsPath = path.join(process.cwd(), 'public', 'car-models');

  try {
    // Dynamic import of fs module only on server side
    const fs = await import('fs');

    if (!fs.existsSync(carModelsPath)) {
      console.warn('Car models directory not found:', carModelsPath);
      return generateFallbackStock();
    }

    const modelFolders = fs.readdirSync(carModelsPath);
    let itemId = 1;

    for (const modelFolder of modelFolders) {
      const modelPath = path.join(carModelsPath, modelFolder);

      if (!fs.statSync(modelPath).isDirectory()) continue;

      const config = CAR_MODEL_CONFIGS[modelFolder];
      if (!config) continue;

      // Get all car folders within this model
      const carFolders = fs.readdirSync(modelPath);

      for (const carFolder of carFolders) {
        const carPath = path.join(modelPath, carFolder);

        if (!fs.statSync(carPath).isDirectory()) continue;

        // Get all images in this car folder
        const imageFiles = fs.readdirSync(carPath)
          .filter(file => isImageFile(file))
          .sort(); // Sort to ensure consistent order

        if (imageFiles.length === 0) continue;

        // Extract year and generate car details
        const year = extractYearFromFolderName(carFolder);
        const mileage = generateDeterministicMileage(carFolder);
        const bodyCondition = generateBodyCondition(carFolder);

        // Use centralized price adjustment logic
        const adjustedPrice = getAdjustedCarPrice(modelFolder, year);

        // Generate all image paths
        const allImages = imageFiles.map(file =>
          `/car-models/${modelFolder}/${carFolder}/${file}`
        );

        // Generate additional metadata (deterministic based on carFolder)
        const folderHash = carFolder.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
        const isRecentlyAdded = folderHash % 4 === 0; // 25% chance of being recently added
        const hasPriceReduction = folderHash % 5 === 0; // 20% chance of price reduction
        const addedDaysAgo = isRecentlyAdded ? (folderHash % 7) : (folderHash % 30) + 7;
        const addedDate = new Date();
        addedDate.setDate(addedDate.getDate() - addedDaysAgo);

        // Create stock item
        const stockItem: StockItem = {
          id: itemId++,
          category: 'cars',
          title: `${config.name} ${year}`,
          price: `¥${adjustedPrice.toLocaleString()}`,
          location: 'Japan',
          status: carFolder.length % 10 === 0 ? 'Reserved' : 'Available', // Deterministic status based on folder name
          image: allImages[0], // First image as main image
          images: allImages, // All images for animation and modal
          specs: [
            config.seats,
            config.transmission,
            `${Math.floor(mileage / 1000)}k km`,
            config.fuelType,
            bodyCondition
          ],
          carId: carFolder,
          year,
          mileage,
          fuelType: config.fuelType,
          transmission: config.transmission,
          bodyCondition,
          addedDate: addedDate.toISOString(),
          originalPrice: hasPriceReduction ? `¥${(adjustedPrice + 20000).toLocaleString()}` : undefined,
          stockQuantity: (folderHash % 3) + 1, // 1-3 units (deterministic)
          popularity: (folderHash % 100) + 1 // 1-100 popularity score (deterministic)
        };

        stockItems.push(stockItem);
      }
    }

    // Only cars are included in stock

    return stockItems;
  } catch (error) {
    console.error('Error generating stock from file system:', error);
    return generateFallbackStock();
  }
}
