import { NextRequest, NextResponse } from 'next/server';
import { 
  calculateShippingOptions, 
  getRecommendedShipping,
  calculateTotalShippingCost,
  getShippingTimeline,
  validateShippingDestination,
  getShippingRestrictions,
  getDeliveryDateRange
} from '@/lib/shippingCalculator';
import { VehicleInfo, ShippingDestination } from '@/lib/shippingCalculator';

export async function POST(request: NextRequest) {
  try {
    const { vehicle, destination, preference = 'balanced' } = await request.json();

    // Validate input
    if (!vehicle || !destination) {
      return NextResponse.json(
        { success: false, message: 'Vehicle and destination information required' },
        { status: 400 }
      );
    }

    // Validate destination
    const destinationValidation = validateShippingDestination(destination);
    if (!destinationValidation.valid) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Invalid destination', 
          errors: destinationValidation.errors 
        },
        { status: 400 }
      );
    }

    // Calculate shipping options
    const shippingOptions = calculateShippingOptions(vehicle, destination);

    if (shippingOptions.length === 0) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'No shipping options available for this vehicle and destination' 
        },
        { status: 400 }
      );
    }

    // Get recommended option
    const recommended = getRecommendedShipping(shippingOptions, preference);

    // Calculate detailed costs for each option
    const detailedOptions = shippingOptions.map(option => {
      const vehicleValue = vehicle.price || 500000; // Default value if not provided
      const totalCosts = calculateTotalShippingCost(
        option.cost,
        vehicleValue,
        true, // Include insurance
        true  // Include handling
      );

      const timeline = getShippingTimeline(option.method);
      const dateRange = getDeliveryDateRange(option.method);

      return {
        ...option,
        costs: totalCosts,
        timeline,
        dateRange,
        isRecommended: recommended?.method.id === option.method.id,
      };
    });

    // Get shipping restrictions
    const restrictions = getShippingRestrictions(destination);

    return NextResponse.json({
      success: true,
      data: {
        options: detailedOptions,
        recommended: detailedOptions.find(opt => opt.isRecommended),
        restrictions,
        destination,
        vehicle: {
          category: vehicle.category,
          estimatedWeight: detailedOptions[0]?.weight || 1500,
        },
      },
    });

  } catch (error) {
    console.error('Error calculating shipping:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to calculate shipping options' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const vehicleCategory = searchParams.get('category');
    const country = searchParams.get('country');
    const city = searchParams.get('city');

    if (!vehicleCategory || !country || !city) {
      return NextResponse.json(
        { success: false, message: 'Vehicle category, country, and city are required' },
        { status: 400 }
      );
    }

    const vehicle: VehicleInfo = {
      category: vehicleCategory,
      year: 2020, // Default year
    };

    const destination: ShippingDestination = {
      country,
      city,
    };

    // Quick calculation for basic shipping estimates
    const shippingOptions = calculateShippingOptions(vehicle, destination);
    const restrictions = getShippingRestrictions(destination);

    const quickEstimates = shippingOptions.map(option => ({
      methodId: option.method.id,
      methodName: option.method.name,
      estimatedCost: option.cost,
      estimatedDays: option.method.estimatedDays,
      estimatedDelivery: option.estimatedDelivery,
    }));

    return NextResponse.json({
      success: true,
      data: {
        estimates: quickEstimates,
        restrictions,
        supportedDestination: shippingOptions.length > 0,
      },
    });

  } catch (error) {
    console.error('Error getting shipping estimates:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to get shipping estimates' },
      { status: 500 }
    );
  }
}
