'use client';

import { useState, useEffect, useRef } from 'react';
import { Mi<PERSON>, MicOff } from 'lucide-react';

interface VoiceRecognitionProps {
  locale: string;
  isListening: boolean;
  setIsListening: (listening: boolean) => void;
  onTranscript: (transcript: string) => void;
}

export default function VoiceRecognition({ 
  locale, 
  isListening, 
  setIsListening, 
  onTranscript 
}: VoiceRecognitionProps) {
  const [isSupported, setIsSupported] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const recognitionRef = useRef<any>(null);

  useEffect(() => {
    // Check if speech recognition is supported
    if (typeof window !== 'undefined') {
      const SpeechRecognition = window.SpeechRecognition || (window as any).webkitSpeechRecognition;

      if (SpeechRecognition) {
        setIsSupported(true);

        const recognition = new SpeechRecognition();
        recognition.continuous = false;
        recognition.interimResults = false;
        recognition.lang = locale === 'ja' ? 'ja-JP' : 'en-US';
        recognition.maxAlternatives = 1;

        recognition.onstart = () => {
          setError(null);
          console.log('Speech recognition started');
        };

        recognition.onresult = (event: any) => {
          try {
            if (event.results && event.results.length > 0) {
              const transcript = event.results[0][0].transcript;
              if (transcript && transcript.trim()) {
                onTranscript(transcript);
              }
            }
          } catch (err) {
            console.error('Error processing speech result:', err);
            setError('processing_error');
          }
          setIsListening(false);
        };

        recognition.onerror = (event: any) => {
          console.log('Speech recognition error:', event.error);

          // Handle different error types gracefully
          switch (event.error) {
            case 'aborted':
              // User stopped or component unmounted - not a real error
              break;
            case 'no-speech':
              setError('no_speech');
              break;
            case 'audio-capture':
              setError('microphone_error');
              break;
            case 'not-allowed':
              setError('permission_denied');
              break;
            default:
              setError(event.error);
          }
          setIsListening(false);
        };

        recognition.onend = () => {
          setIsListening(false);
          console.log('Speech recognition ended');
        };

        recognitionRef.current = recognition;
      } else {
        setIsSupported(false);
      }
    }

    return () => {
      if (recognitionRef.current && isListening) {
        try {
          recognitionRef.current.abort();
        } catch (err) {
          // Ignore cleanup errors
        }
      }
    };
  }, [locale, onTranscript, setIsListening, isListening]);

  const toggleListening = () => {
    if (!isSupported || !recognitionRef.current) return;

    if (isListening) {
      try {
        recognitionRef.current.stop();
      } catch (err) {
        // Ignore stop errors
      }
      setIsListening(false);
    } else {
      try {
        // Check if another recognition is already running
        if (recognitionRef.current) {
          recognitionRef.current.abort(); // Stop any existing recognition
        }

        // Small delay to ensure cleanup
        setTimeout(() => {
          try {
            recognitionRef.current.start();
            setIsListening(true);
            setError(null);
          } catch (err) {
            console.error('Failed to start speech recognition:', err);
            setError('start_failed');
            setIsListening(false);
          }
        }, 100);
      } catch (err) {
        console.error('Failed to initialize speech recognition:', err);
        setError('init_failed');
        setIsListening(false);
      }
    }
  };

  const getErrorMessage = (errorType: string): string => {
    const errorMessages = {
      en: {
        no_speech: 'No speech detected',
        microphone_error: 'Mic access failed',
        permission_denied: 'Mic permission denied',
        start_failed: 'Failed to start',
        init_failed: 'Voice not available',
        processing_error: 'Processing failed'
      },
      ja: {
        no_speech: '音声が検出されません',
        microphone_error: 'マイクアクセス失敗',
        permission_denied: 'マイク許可が拒否されました',
        start_failed: '開始に失敗',
        init_failed: '音声利用不可',
        processing_error: '処理に失敗'
      }
    };

    const messages = errorMessages[locale as 'en' | 'ja'] || errorMessages.en;
    return messages[errorType as keyof typeof messages] || (locale === 'en' ? 'Voice error' : '音声エラー');
  };

  if (!isSupported) {
    return null; // Hide button if not supported
  }

  return (
    <div className="relative">
      <button
        onClick={toggleListening}
        className={`p-2 rounded-lg transition-all duration-200 ${
          isListening
            ? 'bg-red-500 hover:bg-red-600 text-white animate-pulse'
            : 'bg-neutral-200 hover:bg-neutral-300 text-neutral-600'
        }`}
        title={
          isListening
            ? (locale === 'en' ? 'Stop listening' : '録音停止')
            : (locale === 'en' ? 'Start voice input' : '音声入力開始')
        }
      >
        {isListening ? <MicOff size={16} /> : <Mic size={16} />}
      </button>

      {/* Listening indicator */}
      {isListening && (
        <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-red-500 text-white text-xs px-2 py-1 rounded whitespace-nowrap">
          {locale === 'en' ? 'Listening...' : '聞いています...'}
        </div>
      )}

      {/* Error indicator */}
      {error && error !== 'aborted' && (
        <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-red-500 text-white text-xs px-2 py-1 rounded whitespace-nowrap max-w-32 text-center">
          {getErrorMessage(error)}
        </div>
      )}
    </div>
  );
}

// Extend Window interface for TypeScript
declare global {
  interface Window {
    SpeechRecognition: any;
    webkitSpeechRecognition: any;
  }
}
