import { NextRequest, NextResponse } from 'next/server';
import { 
  processPendingFollowUps, 
  scheduleInactiveCustomerFollowUps,
  scheduleAbandonedCartFollowUp,
  scheduleOrderDeliveryFollowUp
} from '@/lib/followupScheduler';

// POST - Process pending follow-ups or schedule new ones
export async function POST(request: NextRequest) {
  try {
    const { action, adminKey, ...data } = await request.json();

    // Verify admin authentication for manual triggers
    if (action !== 'webhook') {
      const validAdminKey = process.env.ADMIN_PASSWORD || 'admin123';
      if (adminKey !== validAdminKey) {
        return NextResponse.json(
          { success: false, message: 'Unauthorized' },
          { status: 401 }
        );
      }
    }

    switch (action) {
      case 'process_pending':
        await processPendingFollowUps();
        return NextResponse.json({ 
          success: true, 
          message: 'Pending follow-ups processed successfully' 
        });

      case 'schedule_inactive':
        await scheduleInactiveCustomerFollowUps();
        return NextResponse.json({ 
          success: true, 
          message: 'Inactive customer follow-ups scheduled successfully' 
        });

      case 'abandoned_cart':
        if (!data.customerId || !data.cartData) {
          return NextResponse.json(
            { success: false, message: 'Customer ID and cart data are required' },
            { status: 400 }
          );
        }
        await scheduleAbandonedCartFollowUp(data.customerId, data.cartData);
        return NextResponse.json({ 
          success: true, 
          message: 'Abandoned cart follow-up scheduled successfully' 
        });

      case 'order_delivered':
        if (!data.customerId || !data.orderId || !data.orderData) {
          return NextResponse.json(
            { success: false, message: 'Customer ID, order ID, and order data are required' },
            { status: 400 }
          );
        }
        await scheduleOrderDeliveryFollowUp(data.customerId, data.orderId, data.orderData);
        return NextResponse.json({ 
          success: true, 
          message: 'Order delivery follow-up scheduled successfully' 
        });

      case 'webhook':
        // This can be called by external cron services like Vercel Cron, GitHub Actions, etc.
        const authHeader = request.headers.get('authorization');
        const expectedAuth = `Bearer ${process.env.CRON_SECRET || 'default-cron-secret'}`;
        
        if (authHeader !== expectedAuth) {
          return NextResponse.json(
            { success: false, message: 'Unauthorized webhook' },
            { status: 401 }
          );
        }

        // Process both pending follow-ups and inactive customers
        await Promise.all([
          processPendingFollowUps(),
          scheduleInactiveCustomerFollowUps()
        ]);

        return NextResponse.json({ 
          success: true, 
          message: 'Webhook processed successfully' 
        });

      default:
        return NextResponse.json(
          { success: false, message: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Error processing follow-up action:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to process follow-up action' },
      { status: 500 }
    );
  }
}

// GET - Get follow-up processing status
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const adminKey = searchParams.get('adminKey');

    // Verify admin authentication
    const validAdminKey = process.env.ADMIN_PASSWORD || 'admin123';
    if (adminKey !== validAdminKey) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get pending follow-ups count
    const { getPendingFollowUps } = await import('@/lib/crmStorage');
    const pendingFollowUps = await getPendingFollowUps();

    return NextResponse.json({
      success: true,
      data: {
        pendingFollowUps: pendingFollowUps.length,
        lastProcessed: new Date().toISOString(),
        nextScheduledRun: new Date(Date.now() + 60 * 60 * 1000).toISOString(), // Next hour
      }
    });

  } catch (error) {
    console.error('Error getting follow-up status:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to get follow-up status' },
      { status: 500 }
    );
  }
}
