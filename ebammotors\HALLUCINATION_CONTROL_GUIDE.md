# AI Chatbot Hallucination Control Guide

## 🎯 What is Hallucination?

**Hallucination** occurs when AI models generate false, made-up, or inaccurate information that sounds plausible but isn't based on real data.

### Examples of Hallucination in Your Context:
- ❌ "We have Honda Civic for ¥450,000" (you don't sell Honda Civic)
- ❌ "All cars come with 2-year warranty" (you don't offer warranties)
- ❌ "Free shipping to Ghana" (shipping isn't free)
- ❌ "Toyota Camry available for ¥280,000" (not in your inventory)

## 🛡️ How We Control Hallucination

### 1. **System Prompt Engineering**
```
EXACT Car Inventory & Pricing (FOB Japan) - DO NOT MAKE UP OTHER PRICES:
- Toyota Voxy (8 seats): ¥300,000 (CHEAPEST)
- Toyota Noah (8 seats): ¥350,000  
- Toyota Sienta (7 seats): ¥320,000
- Toyota Yaris (5 seats): ¥550,000
- Toyota Vitz (5 seats): ¥325,000

ONLY these 5 cars are available. DO NOT mention other models or prices.
```

### 2. **Model Parameters (Anti-Hallucination Settings)**
```javascript
temperature: 0.3,        // Lower = more focused, less creative
top_p: 0.8,             // Nucleus sampling - more focused
frequency_penalty: 0.5,  // Reduce repetition
presence_penalty: 0.3,   // Encourage staying on topic
max_tokens: 200,        // Limit response length
```

### 3. **Response Validation**
- **Pattern Detection**: Automatically detects suspicious content
- **Price Validation**: Only allows exact prices from inventory
- **Model Validation**: Only allows known car models
- **Safety Filters**: Removes absolute guarantees and false claims

### 4. **Risk Assessment**
- **Low Risk**: Accurate responses ✅
- **Medium Risk**: Minor issues, adds disclaimer ⚠️
- **High Risk**: Replaces with safe fallback ❌

## 🔧 Configuration Options

### Adjust Hallucination Control

Edit `src/config/chatbotConfig.ts`:

```javascript
// Make AI more conservative (less hallucination)
temperature: 0.1,        // Very focused
top_p: 0.7,             // More restrictive
max_tokens: 150,        // Shorter responses

// Make AI more creative (more hallucination risk)
temperature: 0.7,        // More creative
top_p: 0.9,             // Less restrictive
max_tokens: 300,        // Longer responses
```

### Add New Validation Rules

```javascript
suspiciousPatterns: [
  /¥[0-9,]+(?!,000)/g,                    // Invalid price formats
  /Honda [A-Z][a-z]+/g,                   // Specific Honda models
  /free shipping|no cost/gi,              // Free shipping claims
  /[0-9]+ years? warranty/gi,             // Warranty claims
  // Add your own patterns here
]
```

## 📊 Monitoring Dashboard

Access the **Hallucination Monitor** in your admin panel:
- **URL**: `/admin/crm` → Scroll to "AI Hallucination Monitor"
- **Features**:
  - Real-time hallucination rate tracking
  - Risk level classification
  - Response validation logs
  - Quick actions for incident management

### Key Metrics:
- **Target Rate**: <5% hallucination
- **Warning Level**: >10% hallucination
- **Critical Level**: >15% hallucination

## 🚨 Common Hallucination Triggers

### 1. **Pricing Questions**
- ❌ User: "What's the price of Honda Accord?"
- 🤖 Bad: "Honda Accord is ¥380,000"
- ✅ Good: "I can only provide information about our current inventory: Toyota Voxy, Noah, Sienta, Yaris, and Vitz."

### 2. **Technical Specifications**
- ❌ User: "What's the engine size of Voxy?"
- 🤖 Bad: "Voxy has a 2.0L V6 engine with 180hp"
- ✅ Good: "For detailed technical specifications, I'd recommend speaking directly with our team."

### 3. **Shipping & Delivery**
- ❌ User: "How long does shipping take?"
- 🤖 Bad: "Shipping takes exactly 21 days to Ghana"
- ✅ Good: "Shipping times vary by destination. Please contact us for accurate delivery estimates."

## 🛠️ Advanced Controls

### 1. **Enable Strict Mode**
```javascript
// In chatbotConfig.ts
monitoring: {
  requireHumanReview: true,  // All responses reviewed
  flagPotentialHallucinations: true,
  logSuspiciousResponses: true
}
```

### 2. **Custom Fallback Responses**
```javascript
fallbackResponses: {
  unknownCar: "I can only provide information about our current inventory...",
  unknownPrice: "For current pricing, please contact us...",
  technicalSpecs: "For technical details, speak with our specialists...",
}
```

### 3. **Response Length Limits**
```javascript
responseGuidelines: {
  maxResponseLength: 200,  // Shorter = less hallucination risk
}
```

## 🧪 Testing for Hallucination

### Test Questions to Try:
1. "Do you have Honda Civic?" (Should not make up Honda models)
2. "What's the cheapest car under ¥200,000?" (Should say Voxy is cheapest at ¥300,000)
3. "Do you offer free shipping?" (Should not claim free shipping)
4. "What warranty do you provide?" (Should not make up warranty terms)
5. "How many Toyota Prius do you have?" (Should say not in current inventory)

### Expected Safe Responses:
- "I can only provide information about our current inventory..."
- "For specific details, please contact our team..."
- "Let me connect you with our specialists..."
- "Please contact us via WhatsApp for accurate information..."

## 📈 Best Practices

### 1. **Regular Monitoring**
- Check hallucination rate weekly
- Review high-risk incidents
- Update validation patterns as needed

### 2. **Prompt Maintenance**
- Keep inventory data current
- Add new validation rules for emerging issues
- Test with edge cases regularly

### 3. **User Training**
- Train staff to recognize hallucination
- Have escalation procedures for suspicious responses
- Regular review of chatbot conversations

### 4. **Gradual Adjustments**
- Start with conservative settings
- Gradually adjust based on performance
- Monitor impact of changes

## 🔄 Quick Fixes

### If Hallucination Rate is High (>15%):
1. **Lower temperature**: Set to 0.1-0.2
2. **Reduce max_tokens**: Set to 100-150
3. **Enable strict mode**: `requireHumanReview: true`
4. **Add more validation patterns**

### If Responses are Too Robotic:
1. **Slightly increase temperature**: Set to 0.4-0.5
2. **Adjust prompts**: Add more conversational examples
3. **Review validation patterns**: Remove overly strict rules

## 📞 Emergency Actions

### If Critical Hallucination Detected:
1. **Immediate**: Set `AI_PROVIDER=fallback` in `.env.local`
2. **Review**: Check recent conversations in admin panel
3. **Adjust**: Update validation rules and prompts
4. **Test**: Verify fixes before re-enabling AI

### Contact for Support:
- Check admin dashboard for real-time monitoring
- Review logs in Hallucination Monitor
- Test with known problematic queries

---

## 🎯 Summary

**Your AI chatbot now has comprehensive hallucination controls:**
- ✅ Strict inventory validation
- ✅ Response pattern detection
- ✅ Risk-based filtering
- ✅ Real-time monitoring
- ✅ Safe fallback responses

**Result**: Accurate, trustworthy responses that stick to your actual inventory and services!
