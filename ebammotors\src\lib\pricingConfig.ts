// Centralized pricing configuration for EBAM Motors
// This file serves as the single source of truth for all pricing information
// Update prices here and they will automatically reflect in the chatbot and throughout the system

export interface CarModelConfig {
  name: string;
  basePrice: number;
  seats: string;
  transmission: string;
  fuelType: string;
}

export interface ElectronicsConfig {
  category: string;
  minPrice: number;
  maxPrice: number;
  examples: string[];
}

export interface FurnitureConfig {
  category: string;
  minPrice: number;
  examples: string[];
}

/**
 * Car model pricing configuration
 * These prices are used across the stock system and chatbot
 */
export const CAR_MODEL_CONFIGS: Record<string, CarModelConfig> = {
  'toyota-voxy': {
    name: 'Toyota Voxy',
    basePrice: 300000,
    seats: '8-seater',
    transmission: 'Automatic',
    fuelType: 'Gasoline'
  },
  'toyota-noah': {
    name: 'Toyota Noah',
    basePrice: 350000,
    seats: '8-seater',
    transmission: 'Automatic',
    fuelType: 'Gasoline'
  },
  'toyota-sienta': {
    name: 'Toyota Sienta',
    basePrice: 320000,
    seats: '7-seater',
    transmission: 'CVT',
    fuelType: 'Hybrid'
  },
  'toyota-vitz': {
    name: 'Toyota Vitz',
    basePrice: 325000,
    seats: '5-seater',
    transmission: 'Manual',
    fuelType: 'Gasoline'
  },
  'toyota-yaris': {
    name: 'Toyota Yaris',
    basePrice: 550000,
    seats: '5-seater',
    transmission: 'CVT',
    fuelType: 'Hybrid'
  }
};

/**
 * Electronics pricing configuration
 */
export const ELECTRONICS_CONFIG: ElectronicsConfig = {
  category: 'Electronics',
  minPrice: 28000,
  maxPrice: 45000,
  examples: ['Refrigerators', 'Air Conditioners', 'Washing Machines', 'Televisions', 'Microwaves']
};

/**
 * Furniture pricing configuration
 */
export const FURNITURE_CONFIG: FurnitureConfig = {
  category: 'Furniture',
  minPrice: 32000,
  examples: ['Dining sets', 'Office furniture', 'Sofas', 'Beds', 'Wardrobes']
};

/**
 * Price adjustment factors
 */
export const PRICE_ADJUSTMENTS = {
  yearAdjustment: {
    old: -5000,  // Deduct for cars 2010 and older
    new: 5000    // Add for cars 2012 and newer
  },
  conditionMultiplier: {
    excellent: 1.1,
    veryGood: 1.0,
    good: 0.95,
    fair: 0.9
  }
};

/**
 * Get adjusted price for a car based on year
 */
export function getAdjustedCarPrice(modelKey: string, year: number): number {
  const config = CAR_MODEL_CONFIGS[modelKey];
  if (!config) return 0;

  let adjustedPrice = config.basePrice;
  
  if (year <= 2010) {
    adjustedPrice += PRICE_ADJUSTMENTS.yearAdjustment.old;
  } else if (year >= 2012) {
    adjustedPrice += PRICE_ADJUSTMENTS.yearAdjustment.new;
  }

  return Math.max(adjustedPrice, 0); // Ensure price doesn't go negative
}

/**
 * Get price range for a car model (considering year adjustments)
 */
export function getCarPriceRange(modelKey: string): { min: number; max: number } {
  const config = CAR_MODEL_CONFIGS[modelKey];
  if (!config) return { min: 0, max: 0 };

  const minPrice = config.basePrice + PRICE_ADJUSTMENTS.yearAdjustment.old;
  const maxPrice = config.basePrice + PRICE_ADJUSTMENTS.yearAdjustment.new;

  return {
    min: Math.max(minPrice, 0),
    max: maxPrice
  };
}

/**
 * Get all car models with their price ranges
 */
export function getAllCarModelsWithPrices(): Array<{
  name: string;
  seats: string;
  priceRange: { min: number; max: number };
}> {
  return Object.entries(CAR_MODEL_CONFIGS).map(([key, config]) => ({
    name: config.name,
    seats: config.seats,
    priceRange: getCarPriceRange(key)
  }));
}

/**
 * Format price in Japanese Yen
 */
export function formatPrice(price: number): string {
  return `¥${price.toLocaleString()}`;
}

/**
 * Format price range
 */
export function formatPriceRange(min: number, max: number): string {
  return `${formatPrice(min)}-${formatPrice(max)}`;
}
