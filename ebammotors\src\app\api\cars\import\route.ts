// API endpoints for CSV bulk import
import { NextRequest, NextResponse } from 'next/server';
import { processBulkCSVImport, getCSVImportByBatchId, getAllCSVImports } from '@/lib/csvImport';

// POST - Upload and process CSV file for bulk import
export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    
    // Get admin key and validate
    const adminKey = formData.get('adminKey') as string;
    const validAdminKey = process.env.ADMIN_PASSWORD || 'admin123';
    
    if (adminKey !== validAdminKey) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get CSV file
    const csvFile = formData.get('csvFile') as File;
    if (!csvFile) {
      return NextResponse.json(
        { success: false, message: 'CSV file is required' },
        { status: 400 }
      );
    }

    // Validate file type
    if (!csvFile.name.toLowerCase().endsWith('.csv')) {
      return NextResponse.json(
        { success: false, message: 'File must be a CSV file' },
        { status: 400 }
      );
    }

    // Check file size (limit to 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (csvFile.size > maxSize) {
      return NextResponse.json(
        { success: false, message: 'File size must be less than 10MB' },
        { status: 400 }
      );
    }

    // Read CSV content
    const csvContent = await csvFile.text();
    
    if (!csvContent.trim()) {
      return NextResponse.json(
        { success: false, message: 'CSV file is empty' },
        { status: 400 }
      );
    }

    // Get imported by (could be admin user ID in the future)
    const importedBy = formData.get('importedBy') as string || 'admin';

    // Process the CSV import
    const result = await processBulkCSVImport(csvContent, csvFile.name, importedBy);

    return NextResponse.json({
      success: result.success,
      message: result.success 
        ? `Successfully imported ${result.successful_imports} cars` 
        : 'Import completed with errors',
      batch_id: result.batch_id,
      total_rows: result.total_rows,
      successful_imports: result.successful_imports,
      failed_imports: result.failed_imports,
      errors: result.errors,
      imported_cars: result.imported_cars.map(car => ({
        id: car.id,
        car_id: car.car_id,
        title: car.title,
        make: car.make,
        model: car.model,
        year: car.year,
        price: car.price,
        status: car.status,
      })), // Return simplified car data to avoid large response
    });

  } catch (error) {
    console.error('Error processing CSV import:', error);
    
    let errorMessage = 'Failed to process CSV import';
    if (error instanceof Error) {
      errorMessage = error.message;
    }

    return NextResponse.json(
      { success: false, message: errorMessage },
      { status: 500 }
    );
  }
}

// GET - Get import status or list all imports
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const adminKey = searchParams.get('adminKey');
    const batchId = searchParams.get('batch_id');

    // Admin authentication
    const validAdminKey = process.env.ADMIN_PASSWORD || 'admin123';
    if (adminKey !== validAdminKey) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (batchId) {
      // Get specific import by batch ID
      const importRecord = await getCSVImportByBatchId(batchId);
      
      if (!importRecord) {
        return NextResponse.json(
          { success: false, message: 'Import record not found' },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        import: importRecord
      });
    } else {
      // Get all imports with pagination
      const page = parseInt(searchParams.get('page') || '1');
      const perPage = parseInt(searchParams.get('per_page') || '20');

      const result = await getAllCSVImports(page, perPage);

      return NextResponse.json({
        success: true,
        ...result
      });
    }

  } catch (error) {
    console.error('Error fetching import records:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch import records' },
      { status: 500 }
    );
  }
}
