@echo off
echo 🤖 EBAM Motors AI Chatbot Setup
echo ================================
echo.

echo This script will set up a free AI chatbot using Ollama (completely free, no API keys needed)
echo.

echo Step 1: Checking if Ollama is installed...
where ollama >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Ollama not found. Please install it first:
    echo.
    echo 1. Go to: https://ollama.ai/
    echo 2. Download and install Ollama for Windows
    echo 3. Run this script again
    echo.
    pause
    exit /b 1
)

echo ✅ Ollama is installed!
echo.

echo Step 2: Starting Ollama service...
start /B ollama serve
timeout /t 3 >nul

echo Step 3: Downloading AI model (llama3.2:3b - fast and efficient)...
echo This may take a few minutes depending on your internet connection...
ollama pull llama3.2:3b

if %errorlevel% neq 0 (
    echo ❌ Failed to download model. Please check your internet connection.
    pause
    exit /b 1
)

echo ✅ Model downloaded successfully!
echo.

echo Step 4: Testing the AI model...
echo Testing... | ollama run llama3.2:3b "Respond with just 'AI is working!' if you can understand this."

echo.
echo ✅ Setup complete! Your AI chatbot is now ready.
echo.
echo 🎉 What's configured:
echo   • AI Provider: Ollama (local, completely free)
echo   • Model: llama3.2:3b (fast and efficient)
echo   • No API keys needed
echo   • No usage limits
echo   • Private (runs locally)
echo.
echo 🚀 Next steps:
echo   1. Start your development server: npm run dev
echo   2. Test the chatbot on your website
echo   3. Check AI status in admin dashboard: /admin/crm
echo.
echo 💡 The chatbot will now give natural, intelligent responses!
echo.
pause
