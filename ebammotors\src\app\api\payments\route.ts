import { NextRequest, NextResponse } from 'next/server';
import { updatePaymentStatus, getOrderById, addShippingUpdate } from '@/lib/orderStorage';
import { getPaymentMethodById, getPaymentInstructions } from '@/lib/paymentConfig';
import { markInvoiceAsPaid, getInvoiceByOrderId } from '@/lib/invoiceGenerator';
import { emailService, OrderConfirmationData, AdminNotificationData } from '@/lib/resendService';
import Stripe from 'stripe';

// Initialize Stripe (only if API key is available)
const stripe = process.env.STRIPE_SECRET_KEY ? new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2024-12-18.acacia',
}) : null;

export async function POST(request: NextRequest) {
  try {
    const { orderId, paymentMethodId, amount, currency = 'JPY' } = await request.json();

    if (!orderId || !paymentMethodId || !amount) {
      return NextResponse.json(
        { success: false, message: 'Order ID, payment method, and amount are required' },
        { status: 400 }
      );
    }

    // Get order
    const order = await getOrderById(orderId);
    if (!order) {
      return NextResponse.json(
        { success: false, message: 'Order not found' },
        { status: 404 }
      );
    }

    // Get payment method
    const paymentMethod = getPaymentMethodById(paymentMethodId);
    if (!paymentMethod) {
      return NextResponse.json(
        { success: false, message: 'Invalid payment method' },
        { status: 400 }
      );
    }

    // Handle different payment methods
    switch (paymentMethod.type) {
      case 'stripe':
        return await handleStripePayment(orderId, amount, currency);
      
      case 'bank_transfer':
      case 'mobile_money':
      case 'cash_agent':
        return await handleManualPayment(orderId, paymentMethodId, amount, currency);
      
      default:
        return NextResponse.json(
          { success: false, message: 'Unsupported payment method' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Error processing payment:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to process payment' },
      { status: 500 }
    );
  }
}

async function handleStripePayment(orderId: string, amount: number, currency: string) {
  if (!stripe) {
    return NextResponse.json(
      { success: false, message: 'Stripe not configured' },
      { status: 500 }
    );
  }

  try {
    // Create Stripe payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(amount * 100), // Stripe expects amount in cents
      currency: currency.toLowerCase(),
      metadata: {
        orderId,
      },
    });

    // Update order payment status
    await updatePaymentStatus(orderId, {
      status: 'processing',
      transactionId: paymentIntent.id,
    });

    return NextResponse.json({
      success: true,
      data: {
        clientSecret: paymentIntent.client_secret,
        paymentIntentId: paymentIntent.id,
      },
    });

  } catch (error) {
    console.error('Stripe payment error:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to create payment intent' },
      { status: 500 }
    );
  }
}

async function handleManualPayment(orderId: string, paymentMethodId: string, amount: number, currency: string) {
  try {
    // Get payment instructions
    const instructions = getPaymentInstructions(paymentMethodId, amount, orderId);

    // Update order payment status to pending
    await updatePaymentStatus(orderId, {
      status: 'pending',
    });

    return NextResponse.json({
      success: true,
      data: {
        instructions,
        paymentMethod: paymentMethodId,
        amount,
        currency,
        orderId,
      },
    });

  } catch (error) {
    console.error('Manual payment error:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to process manual payment' },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const { orderId, status, transactionId, adminKey } = await request.json();

    // Admin authentication for manual payment confirmation
    const validAdminKey = process.env.ADMIN_PASSWORD || 'admin123';
    if (adminKey !== validAdminKey) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (!orderId || !status) {
      return NextResponse.json(
        { success: false, message: 'Order ID and status are required' },
        { status: 400 }
      );
    }

    // Update payment status
    const updateData: {
      status: string;
      transactionId?: string;
      paidAt?: string;
    } = { status };
    if (transactionId) {
      updateData.transactionId = transactionId;
    }
    if (status === 'completed') {
      updateData.paidAt = new Date().toISOString();
    }

    const success = await updatePaymentStatus(orderId, updateData);

    if (!success) {
      return NextResponse.json(
        { success: false, message: 'Order not found' },
        { status: 404 }
      );
    }

    // If payment is completed, mark invoice as paid and update order status
    if (status === 'completed') {
      const invoice = await getInvoiceByOrderId(orderId);
      if (invoice) {
        await markInvoiceAsPaid(invoice.id);
      }

      // Update order status to paid
      const order = await getOrderById(orderId);
      if (order) {
        await updatePaymentStatus(orderId, { status: 'completed' });

        // Add shipping update
        await addShippingUpdate(orderId, {
          timestamp: new Date().toISOString(),
          status: 'Payment Confirmed',
          location: 'EBAM Motors Office',
          description: 'Payment received and confirmed. Vehicle preparation will begin shortly.',
        });

        // Send order confirmation email
        try {
          const orderConfirmationData: OrderConfirmationData = {
            customerName: order.customer.name,
            orderNumber: order.id,
            orderDate: new Date(order.createdAt).toLocaleDateString(),
            vehicle: {
              title: order.vehicle.title,
              price: `¥${order.vehicle.price.toLocaleString()}`,
              image: order.vehicle.image
            },
            total: `¥${order.payment.amount.toLocaleString()}`,
            shippingAddress: order.shipping.address,
            estimatedDelivery: order.shipping.estimatedDelivery || 'To be determined'
          };

          await emailService.sendOrderConfirmation(order.customer.email, orderConfirmationData);
          console.log('Order confirmation email sent successfully');

          // Send admin notification about new order
          const adminNotificationData: AdminNotificationData = {
            type: 'new_order',
            title: 'New Order Received',
            message: `Order ${order.id} has been paid and confirmed. Customer: ${order.customer.name} (${order.customer.email}). Vehicle: ${order.vehicle.title}. Amount: ¥${order.payment.amount.toLocaleString()}`,
            data: {
              orderId: order.id,
              customerName: order.customer.name,
              customerEmail: order.customer.email,
              vehicleTitle: order.vehicle.title,
              amount: order.payment.amount,
              paymentMethod: order.payment.method
            },
            timestamp: new Date().toISOString()
          };

          await emailService.sendAdminNotification(adminNotificationData);
          console.log('Admin notification email sent successfully');
        } catch (emailError) {
          console.error('Failed to send notification emails:', emailError);
          // Don't fail the payment process if email fails
        }
      }
    }

    return NextResponse.json({
      success: true,
      message: `Payment status updated to ${status}`,
    });

  } catch (error) {
    console.error('Error updating payment status:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to update payment status' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const orderId = searchParams.get('orderId');

    if (!orderId) {
      return NextResponse.json(
        { success: false, message: 'Order ID is required' },
        { status: 400 }
      );
    }

    const order = await getOrderById(orderId);
    if (!order) {
      return NextResponse.json(
        { success: false, message: 'Order not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        orderId: order.id,
        status: order.payment.status,
        amount: order.payment.amount,
        currency: order.payment.currency,
        method: order.payment.method.name,
        transactionId: order.payment.transactionId,
        paidAt: order.payment.paidAt,
        dueDate: order.payment.dueDate,
      },
    });

  } catch (error) {
    console.error('Error getting payment status:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to get payment status' },
      { status: 500 }
    );
  }
}
