// API endpoint for CSV template generation
import { NextRequest, NextResponse } from 'next/server';

// CSV template headers with descriptions
const CSV_TEMPLATE_HEADERS = [
  'car_id', // Required: Unique identifier (e.g., toyota_voxy_2012_TVXYA)
  'make', // Required: Car manufacturer (e.g., Toyota, Honda)
  'model', // Required: Car model (e.g., Voxy, Noah, Sienta)
  'year', // Required: Manufacturing year (1990-2030)
  'price', // Required: Price in Yen (positive number)
  'original_price', // Optional: Original price for discount tracking
  'mileage', // Optional: Mileage in kilometers
  'fuel_type', // Optional: Gasoline, Hybrid, Electric, Diesel
  'transmission', // Optional: Automatic, Manual, CVT
  'engine_size', // Optional: Engine size (e.g., 1.8L, 2.0L)
  'drive_type', // Optional: FWD, AWD, RWD
  'seats', // Optional: Number of seats (1-50)
  'doors', // Optional: Number of doors (1-10)
  'body_type', // Optional: Sedan, SUV, Hatchback, Van, etc.
  'body_condition', // Optional: Excellent, Good, Fair, Needs Work
  'interior_condition', // Optional: Excellent, Good, Fair, Needs Work
  'exterior_color', // Optional: Car exterior color
  'interior_color', // Optional: Car interior color
  'main_image', // Optional: URL to main display image
  'images', // Optional: Comma-separated image URLs
  'image_folder', // Optional: Folder path for images
  'specs', // Optional: Comma-separated specifications
  'features', // Optional: Comma-separated features
  'status', // Optional: Available, Reserved, Sold, Pending
  'stock_quantity', // Optional: Stock quantity (default: 1)
  'location', // Optional: Location (default: Japan)
  'description', // Optional: Car description
  'is_featured', // Optional: true/false, 1/0, yes/no
];

// Sample data for the CSV template
const SAMPLE_DATA = [
  {
    car_id: 'toyota_voxy_2012_TVXYA',
    make: 'Toyota',
    model: 'Voxy',
    year: '2012',
    price: '300000',
    original_price: '320000',
    mileage: '85000',
    fuel_type: 'Gasoline',
    transmission: 'Automatic',
    engine_size: '2.0L',
    drive_type: 'FWD',
    seats: '8',
    doors: '4',
    body_type: 'Van',
    body_condition: 'Good',
    interior_condition: 'Good',
    exterior_color: 'Silver',
    interior_color: 'Black',
    main_image: '/car-models/toyota-voxy/toyota_voxy_2012_TVXYA/toyota_voxy_2012_TVXYA001.jpg',
    images: '/car-models/toyota-voxy/toyota_voxy_2012_TVXYA/toyota_voxy_2012_TVXYA001.jpg,/car-models/toyota-voxy/toyota_voxy_2012_TVXYA/toyota_voxy_2012_TVXYA002.jpg',
    image_folder: '/car-models/toyota-voxy/toyota_voxy_2012_TVXYA',
    specs: '8-seater,Automatic,85k km,Gasoline',
    features: 'Air Conditioning,Power Steering,Electric Windows',
    status: 'Available',
    stock_quantity: '1',
    location: 'Japan',
    description: 'Well-maintained Toyota Voxy with low mileage. Perfect family car with spacious interior.',
    is_featured: 'false',
  },
  {
    car_id: 'toyota_noah_2015_TNAHA',
    make: 'Toyota',
    model: 'Noah',
    year: '2015',
    price: '350000',
    original_price: '',
    mileage: '65000',
    fuel_type: 'Gasoline',
    transmission: 'Automatic',
    engine_size: '2.0L',
    drive_type: 'FWD',
    seats: '8',
    doors: '4',
    body_type: 'Van',
    body_condition: 'Excellent',
    interior_condition: 'Excellent',
    exterior_color: 'White',
    interior_color: 'Gray',
    main_image: '/car-models/toyota-noah/toyota_noah_2015_TNAHA/toyota_noah_2015_TNAHA001.jpg',
    images: '/car-models/toyota-noah/toyota_noah_2015_TNAHA/toyota_noah_2015_TNAHA001.jpg,/car-models/toyota-noah/toyota_noah_2015_TNAHA/toyota_noah_2015_TNAHA002.jpg',
    image_folder: '/car-models/toyota-noah/toyota_noah_2015_TNAHA',
    specs: '8-seater,Automatic,65k km,Gasoline',
    features: 'Air Conditioning,Power Steering,Navigation System,Backup Camera',
    status: 'Available',
    stock_quantity: '1',
    location: 'Japan',
    description: 'Excellent condition Toyota Noah with modern features and low mileage.',
    is_featured: 'true',
  },
  {
    car_id: 'toyota_sienta_2018_TSIHA',
    make: 'Toyota',
    model: 'Sienta',
    year: '2018',
    price: '320000',
    original_price: '',
    mileage: '45000',
    fuel_type: 'Hybrid',
    transmission: 'CVT',
    engine_size: '1.5L',
    drive_type: 'FWD',
    seats: '7',
    doors: '4',
    body_type: 'Van',
    body_condition: 'Excellent',
    interior_condition: 'Good',
    exterior_color: 'Blue',
    interior_color: 'Black',
    main_image: '/car-models/toyota-sienta/toyota_sienta_2018_TSIHA/toyota_sienta_2018_TSIHA001.jpg',
    images: '/car-models/toyota-sienta/toyota_sienta_2018_TSIHA/toyota_sienta_2018_TSIHA001.jpg',
    image_folder: '/car-models/toyota-sienta/toyota_sienta_2018_TSIHA',
    specs: '7-seater,CVT,45k km,Hybrid',
    features: 'Hybrid Engine,Air Conditioning,Power Steering,Eco Mode',
    status: 'Available',
    stock_quantity: '1',
    location: 'Japan',
    description: 'Fuel-efficient hybrid Toyota Sienta with excellent fuel economy.',
    is_featured: 'false',
  },
];

// GET - Download CSV template
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const adminKey = searchParams.get('adminKey');
    const withSamples = searchParams.get('with_samples') === 'true';

    // Admin authentication
    const validAdminKey = process.env.ADMIN_PASSWORD || 'admin123';
    if (adminKey !== validAdminKey) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Generate CSV content
    let csvContent = CSV_TEMPLATE_HEADERS.join(',') + '\n';

    if (withSamples) {
      // Add sample data rows
      SAMPLE_DATA.forEach(row => {
        const values = CSV_TEMPLATE_HEADERS.map(header => {
          const value = row[header as keyof typeof row] || '';
          // Escape commas and quotes in CSV values
          if (value.includes(',') || value.includes('"') || value.includes('\n')) {
            return `"${value.replace(/"/g, '""')}"`;
          }
          return value;
        });
        csvContent += values.join(',') + '\n';
      });
    }

    // Set response headers for file download
    const headers = new Headers();
    headers.set('Content-Type', 'text/csv');
    headers.set('Content-Disposition', `attachment; filename="car_import_template${withSamples ? '_with_samples' : ''}.csv"`);

    return new NextResponse(csvContent, { headers });

  } catch (error) {
    console.error('Error generating CSV template:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to generate CSV template' },
      { status: 500 }
    );
  }
}

// POST - Get template information (headers, validation rules, etc.)
export async function POST(request: NextRequest) {
  try {
    const { adminKey } = await request.json();

    // Admin authentication
    const validAdminKey = process.env.ADMIN_PASSWORD || 'admin123';
    if (adminKey !== validAdminKey) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Return template information
    return NextResponse.json({
      success: true,
      template_info: {
        headers: CSV_TEMPLATE_HEADERS,
        required_fields: ['car_id', 'make', 'model', 'year', 'price'],
        optional_fields: CSV_TEMPLATE_HEADERS.filter(h => !['car_id', 'make', 'model', 'year', 'price'].includes(h)),
        validation_rules: {
          car_id: 'Unique identifier, no spaces or special characters except underscore',
          make: 'Car manufacturer name (e.g., Toyota, Honda)',
          model: 'Car model name (e.g., Voxy, Noah)',
          year: 'Manufacturing year, must be between 1990 and 2030',
          price: 'Price in Yen, must be a positive number',
          mileage: 'Mileage in kilometers, must be non-negative',
          seats: 'Number of seats, must be between 1 and 50',
          doors: 'Number of doors, must be between 1 and 10',
          stock_quantity: 'Stock quantity, must be non-negative',
          is_featured: 'Boolean value: true/false, 1/0, yes/no',
          images: 'Comma-separated list of image URLs',
          specs: 'Comma-separated list of specifications',
          features: 'Comma-separated list of features',
          status: 'One of: Available, Reserved, Sold, Pending',
          body_condition: 'One of: Excellent, Good, Fair, Needs Work',
          fuel_type: 'One of: Gasoline, Hybrid, Electric, Diesel',
          transmission: 'One of: Automatic, Manual, CVT',
        },
        sample_data: SAMPLE_DATA,
        tips: [
          'Use unique car_id values to avoid duplicates',
          'Price should be in Japanese Yen without currency symbols',
          'Use comma-separated values for arrays (images, specs, features)',
          'Boolean fields accept: true/false, 1/0, yes/no',
          'Leave optional fields empty if not applicable',
          'Ensure image URLs are accessible and properly formatted',
          'Test with a small batch first before importing large datasets',
        ],
      },
    });

  } catch (error) {
    console.error('Error getting template info:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to get template information' },
      { status: 500 }
    );
  }
}
