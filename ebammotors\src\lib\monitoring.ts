import { NextRequest } from 'next/server';

// Performance metrics storage (use external service in production)
interface PerformanceMetric {
  timestamp: number;
  endpoint: string;
  method: string;
  duration: number;
  status: number;
  userAgent?: string;
  ip?: string;
  error?: string;
}

interface ErrorLog {
  timestamp: number;
  error: string;
  stack?: string;
  endpoint: string;
  method: string;
  userAgent?: string;
  ip?: string;
  context?: any;
}

// In-memory storage (replace with database/external service in production)
const performanceMetrics: PerformanceMetric[] = [];
const errorLogs: ErrorLog[] = [];
const MAX_STORED_METRICS = 1000;
const MAX_STORED_ERRORS = 500;

/**
 * Start performance monitoring for a request
 */
export function startPerformanceMonitoring(request: NextRequest) {
  const startTime = Date.now();
  const endpoint = new URL(request.url).pathname;
  const method = request.method;
  
  return {
    endpoint,
    method,
    startTime,
    finish: (status: number, error?: string) => {
      const duration = Date.now() - startTime;
      const userAgent = request.headers.get('user-agent') || undefined;
      const ip = request.headers.get('x-forwarded-for') || 
                request.headers.get('x-real-ip') || undefined;
      
      const metric: PerformanceMetric = {
        timestamp: Date.now(),
        endpoint,
        method,
        duration,
        status,
        userAgent,
        ip,
        error
      };
      
      // Store metric
      performanceMetrics.push(metric);
      
      // Keep only recent metrics
      if (performanceMetrics.length > MAX_STORED_METRICS) {
        performanceMetrics.splice(0, performanceMetrics.length - MAX_STORED_METRICS);
      }
      
      // Log slow requests
      if (duration > 5000) { // 5 seconds
        console.warn(`[PERFORMANCE] Slow request detected: ${method} ${endpoint} took ${duration}ms`);
      }
      
      // Log errors
      if (status >= 400) {
        console.error(`[ERROR] ${method} ${endpoint} returned ${status}${error ? `: ${error}` : ''}`);
      }
    }
  };
}

/**
 * Log an error with context
 */
export function logError(
  error: Error | string, 
  context: {
    endpoint?: string;
    method?: string;
    request?: NextRequest;
    additionalContext?: any;
  }
) {
  const errorMessage = error instanceof Error ? error.message : error;
  const stack = error instanceof Error ? error.stack : undefined;
  
  const errorLog: ErrorLog = {
    timestamp: Date.now(),
    error: errorMessage,
    stack,
    endpoint: context.endpoint || 'unknown',
    method: context.method || 'unknown',
    userAgent: context.request?.headers.get('user-agent') || undefined,
    ip: context.request?.headers.get('x-forwarded-for') || 
        context.request?.headers.get('x-real-ip') || undefined,
    context: context.additionalContext
  };
  
  // Store error
  errorLogs.push(errorLog);
  
  // Keep only recent errors
  if (errorLogs.length > MAX_STORED_ERRORS) {
    errorLogs.splice(0, errorLogs.length - MAX_STORED_ERRORS);
  }
  
  // Console log for immediate visibility
  console.error(`[ERROR] ${errorMessage}`, {
    endpoint: errorLog.endpoint,
    method: errorLog.method,
    ip: errorLog.ip,
    context: errorLog.context
  });
}

/**
 * Get performance statistics
 */
export function getPerformanceStats(timeRange: number = 60 * 60 * 1000) { // Default: 1 hour
  const now = Date.now();
  const cutoff = now - timeRange;
  
  const recentMetrics = performanceMetrics.filter(m => m.timestamp > cutoff);
  
  if (recentMetrics.length === 0) {
    return {
      totalRequests: 0,
      averageResponseTime: 0,
      errorRate: 0,
      slowRequests: 0,
      endpointStats: {}
    };
  }
  
  const totalRequests = recentMetrics.length;
  const averageResponseTime = recentMetrics.reduce((sum, m) => sum + m.duration, 0) / totalRequests;
  const errorCount = recentMetrics.filter(m => m.status >= 400).length;
  const errorRate = (errorCount / totalRequests) * 100;
  const slowRequests = recentMetrics.filter(m => m.duration > 5000).length;
  
  // Group by endpoint
  const endpointStats: Record<string, {
    requests: number;
    averageTime: number;
    errors: number;
    slowRequests: number;
  }> = {};
  
  recentMetrics.forEach(metric => {
    if (!endpointStats[metric.endpoint]) {
      endpointStats[metric.endpoint] = {
        requests: 0,
        averageTime: 0,
        errors: 0,
        slowRequests: 0
      };
    }
    
    const stats = endpointStats[metric.endpoint];
    stats.requests++;
    stats.averageTime = (stats.averageTime * (stats.requests - 1) + metric.duration) / stats.requests;
    
    if (metric.status >= 400) stats.errors++;
    if (metric.duration > 5000) stats.slowRequests++;
  });
  
  return {
    totalRequests,
    averageResponseTime: Math.round(averageResponseTime),
    errorRate: Math.round(errorRate * 100) / 100,
    slowRequests,
    endpointStats
  };
}

/**
 * Get recent errors
 */
export function getRecentErrors(limit: number = 50) {
  return errorLogs
    .slice(-limit)
    .reverse() // Most recent first
    .map(error => ({
      timestamp: new Date(error.timestamp).toISOString(),
      error: error.error,
      endpoint: error.endpoint,
      method: error.method,
      ip: error.ip,
      context: error.context
    }));
}

/**
 * Health check function
 */
export function getHealthStatus() {
  const stats = getPerformanceStats(5 * 60 * 1000); // Last 5 minutes
  const recentErrors = getRecentErrors(10);
  
  // Determine health status
  let status = 'healthy';
  const issues: string[] = [];
  
  if (stats.errorRate > 10) {
    status = 'unhealthy';
    issues.push(`High error rate: ${stats.errorRate}%`);
  } else if (stats.errorRate > 5) {
    status = 'degraded';
    issues.push(`Elevated error rate: ${stats.errorRate}%`);
  }
  
  if (stats.averageResponseTime > 10000) {
    status = 'unhealthy';
    issues.push(`Very slow response time: ${stats.averageResponseTime}ms`);
  } else if (stats.averageResponseTime > 5000) {
    if (status === 'healthy') status = 'degraded';
    issues.push(`Slow response time: ${stats.averageResponseTime}ms`);
  }
  
  if (stats.slowRequests > stats.totalRequests * 0.1) {
    if (status === 'healthy') status = 'degraded';
    issues.push(`High number of slow requests: ${stats.slowRequests}`);
  }
  
  return {
    status,
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    issues,
    stats: {
      requests: stats.totalRequests,
      averageResponseTime: stats.averageResponseTime,
      errorRate: stats.errorRate,
      slowRequests: stats.slowRequests
    },
    recentErrors: recentErrors.slice(0, 3) // Only show 3 most recent errors
  };
}

/**
 * Database health check
 */
export async function checkDatabaseHealth(): Promise<{ healthy: boolean; latency?: number; error?: string }> {
  try {
    const start = Date.now();
    
    // Simple database query to check connectivity
    // This should be replaced with actual database health check
    const { sql } = await import('@vercel/postgres');
    await sql`SELECT 1 as health_check`;
    
    const latency = Date.now() - start;
    
    return { healthy: true, latency };
  } catch (error) {
    return { 
      healthy: false, 
      error: error instanceof Error ? error.message : 'Unknown database error' 
    };
  }
}

/**
 * Memory usage monitoring
 */
export function getMemoryUsage() {
  const usage = process.memoryUsage();
  
  return {
    rss: Math.round(usage.rss / 1024 / 1024), // MB
    heapTotal: Math.round(usage.heapTotal / 1024 / 1024), // MB
    heapUsed: Math.round(usage.heapUsed / 1024 / 1024), // MB
    external: Math.round(usage.external / 1024 / 1024), // MB
    arrayBuffers: Math.round(usage.arrayBuffers / 1024 / 1024), // MB
  };
}

/**
 * Clean up old metrics and errors
 */
export function cleanupOldData() {
  const oneHourAgo = Date.now() - (60 * 60 * 1000);
  
  // Remove old performance metrics
  const validMetrics = performanceMetrics.filter(m => m.timestamp > oneHourAgo);
  performanceMetrics.splice(0, performanceMetrics.length, ...validMetrics);
  
  // Remove old error logs
  const validErrors = errorLogs.filter(e => e.timestamp > oneHourAgo);
  errorLogs.splice(0, errorLogs.length, ...validErrors);
}

// Clean up old data every 10 minutes
setInterval(cleanupOldData, 10 * 60 * 1000);
