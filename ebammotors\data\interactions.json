[{"leadId": "52e832f7-6877-4477-a48b-713307c05149", "type": "chat", "direction": "inbound", "channel": "manual", "content": "Testing CRM lead creation functionality", "subject": "Test Lead", "tags": ["lead_creation"], "createdBy": "system", "id": "00ede7f1-1747-41e4-9878-a55145209761", "createdAt": "2025-06-30T12:03:12.700Z"}, {"customerId": "4e44e16c-97c1-4b6c-99f7-5c4a2b099e15", "type": "support", "direction": "inbound", "channel": "website", "content": "Customer profile created/updated", "subject": "Customer Registration", "tags": ["customer_registration", "profile_update"], "createdBy": "system", "id": "3952eeaf-ca02-4e6f-b047-8ba18dc80843", "createdAt": "2025-06-30T12:03:15.791Z"}, {"direction": "outbound", "channel": "email", "tags": ["welcome", "automated"], "createdBy": "system", "customerId": "4e44e16c-97c1-4b6c-99f7-5c4a2b099e15", "leadId": "52e832f7-6877-4477-a48b-713307c05149", "type": "email", "content": "Welcome email sent to new customer", "subject": "Welcome to EBAM Motors", "id": "058da7fd-31f7-47c6-a05e-15c5342262c5", "createdAt": "2025-06-30T12:03:17.661Z"}, {"leadId": "badcd834-d58c-47f4-87cd-c6a8c8c35e72", "type": "chat", "direction": "inbound", "channel": "website", "content": "I am interested in purchasing a Toyota Yaris. Can you provide more details about available models and pricing?", "subject": "Toyota Yaris Inquiry", "tags": ["lead_creation"], "createdBy": "system", "id": "82fcedf1-d902-42ce-94f3-e4e1debb92f3", "createdAt": "2025-06-30T12:12:07.142Z"}, {"customerId": "8af0c801-cbf0-4ab6-81fe-8d183035c463", "type": "support", "direction": "inbound", "channel": "website", "content": "Customer profile created/updated", "subject": "Customer Registration", "tags": ["customer_registration", "profile_update"], "createdBy": "system", "id": "566966cd-bd1c-4225-934d-4d1b54625d40", "createdAt": "2025-06-30T12:12:09.735Z"}, {"direction": "outbound", "channel": "email", "tags": ["follow_up", "product_info", "email"], "createdBy": "admin", "customerId": "8af0c801-cbf0-4ab6-81fe-8d183035c463", "leadId": "badcd834-d58c-47f4-87cd-c6a8c8c35e72", "type": "email", "content": "<PERSON><PERSON> detailed Toyota Yaris brochure and pricing information", "subject": "Toyota Yaris Information Package", "outcome": "successful", "id": "7979b115-88d2-46ff-a441-aab90233da5a", "createdAt": "2025-06-30T12:12:12.823Z"}, {"leadId": "badcd834-d58c-47f4-87cd-c6a8c8c35e72", "type": "support", "direction": "outbound", "channel": "website", "content": "Lead updated: status, notes, assignedTo, followUpDate", "subject": "Lead Status Update", "tags": ["lead_update", "admin_action"], "createdBy": "admin", "id": "2bab0d88-a817-4816-ba50-8e92a24740b6", "createdAt": "2025-06-30T12:12:19.720Z"}, {"leadId": "7e386b78-dd09-4cdd-829d-6f1d2277b9ee", "type": "chat", "direction": "inbound", "channel": "website", "content": "I am interested in purchasing a Toyota Yaris. Can you provide more details about available models and pricing?", "subject": "Toyota Yaris Inquiry", "tags": ["lead_creation"], "createdBy": "system", "id": "8e8c51c3-2e5a-4461-8f71-4f7038128da9", "createdAt": "2025-06-30T12:14:29.771Z"}, {"customerId": "8af0c801-cbf0-4ab6-81fe-8d183035c463", "type": "support", "direction": "inbound", "channel": "website", "content": "Customer profile created/updated", "subject": "Customer Registration", "tags": ["customer_registration", "profile_update"], "createdBy": "system", "id": "cfa07f15-028f-4416-8d45-33160ace7baa", "createdAt": "2025-06-30T12:14:31.122Z"}, {"direction": "outbound", "channel": "email", "tags": ["follow_up", "product_info", "email"], "createdBy": "admin", "customerId": "8af0c801-cbf0-4ab6-81fe-8d183035c463", "leadId": "7e386b78-dd09-4cdd-829d-6f1d2277b9ee", "type": "email", "content": "<PERSON><PERSON> detailed Toyota Yaris brochure and pricing information", "subject": "Toyota Yaris Information Package", "outcome": "successful", "id": "e6cadfd4-33b7-43f3-b487-9afa0b36a272", "createdAt": "2025-06-30T12:14:33.408Z"}, {"leadId": "7e386b78-dd09-4cdd-829d-6f1d2277b9ee", "type": "support", "direction": "outbound", "channel": "website", "content": "Lead updated: status, notes, assignedTo, followUpDate", "subject": "Lead Status Update", "tags": ["lead_update", "admin_action"], "createdBy": "admin", "id": "604679e0-9f35-4edf-a101-3a23dd339381", "createdAt": "2025-06-30T12:14:34.991Z"}, {"direction": "inbound", "channel": "website", "tags": ["chatbot", "user_message"], "createdBy": "anonymous", "type": "chat", "content": "What is  the cheapest car on this site", "subject": "Chatbot Conversation", "id": "7c740c6a-b9dc-4c13-87a2-949e316d7624", "createdAt": "2025-07-01T00:43:18.845Z"}, {"direction": "outbound", "channel": "website", "tags": ["chatbot", "bot_response"], "createdBy": "system", "type": "chat", "content": "Hello! Welcome to EBAM Motors! 👋 I'm here to help you with information about our Japanese used goods export to Ghana and Africa. What can I help you with today?", "subject": "Chatbot Response", "id": "8d290db1-c2c5-47cc-8829-bdd46a939070", "createdAt": "2025-07-01T00:43:20.092Z"}, {"direction": "inbound", "channel": "website", "tags": ["chatbot", "user_message"], "createdBy": "anonymous", "type": "chat", "content": "I want to know the cheapest car on this site", "subject": "Chatbot Conversation", "id": "11e87195-ecae-4ae6-99a6-7e92981a2e7b", "createdAt": "2025-07-01T00:44:01.658Z"}, {"direction": "outbound", "channel": "website", "tags": ["chatbot", "bot_response"], "createdBy": "system", "type": "chat", "content": "Hello! Welcome to EBAM Motors! 👋 I'm here to help you with information about our Japanese used goods export to Ghana and Africa. What can I help you with today?", "subject": "Chatbot Response", "id": "aaa6b82c-1920-44c9-bd44-3908652ad7d2", "createdAt": "2025-07-01T00:44:02.277Z"}, {"direction": "inbound", "channel": "website", "tags": ["chatbot", "user_message"], "createdBy": "anonymous", "type": "chat", "content": "What is the cheapest car on thia site", "subject": "Chatbot Conversation", "id": "8debc396-676a-43c2-bb34-c86b51ab2998", "createdAt": "2025-07-01T01:04:19.975Z"}, {"direction": "outbound", "channel": "website", "tags": ["chatbot", "bot_response"], "createdBy": "system", "type": "chat", "content": "The cheapest car available on our site is the Toyota Voxy with 8 seats, priced at ¥300,000. It's a great option for those looking for a spacious and reliable vehicle at an affordable price. Would you like to know more about this model or would you like to inquire about other options?", "subject": "Chatbot Response", "id": "f540d6bc-c4fe-47b4-9ef8-40473cadf106", "createdAt": "2025-07-01T01:04:20.865Z"}, {"direction": "inbound", "channel": "website", "tags": ["chatbot", "user_message"], "createdBy": "anonymous", "type": "chat", "content": "Tell me more about this model", "subject": "Chatbot Conversation", "id": "d8e0074b-d51b-4527-a478-940f7b33bc38", "createdAt": "2025-07-01T01:05:10.142Z"}, {"direction": "outbound", "channel": "website", "tags": ["chatbot", "bot_response"], "createdBy": "system", "type": "chat", "content": "You're interested in the Toyota Voxy? It's a popular choice for families and groups, offering 8 seats and plenty of space for luggage or passengers. The Voxy is known for its comfort, reliability, and fuel efficiency. It's also relatively affordable, with a price tag of ¥300,000 FOB Japan.\n\nWould you like to know more about the specs or features of the Voxy? Or perhaps you'd like to inquire about other models or services we offer?", "subject": "Chatbot Response", "id": "26a6bd9f-ce33-4f6d-9bc5-2c4acf46fd6b", "createdAt": "2025-07-01T01:05:11.539Z"}, {"direction": "inbound", "channel": "website", "tags": ["chatbot", "user_message"], "createdBy": "anonymous", "type": "chat", "content": "tell me about Adam modules", "subject": "Chatbot Conversation", "id": "cad230ca-ca51-4902-84fc-d298ea8cfeae", "createdAt": "2025-07-01T01:06:01.852Z"}, {"direction": "outbound", "channel": "website", "tags": ["chatbot", "bot_response"], "createdBy": "system", "type": "chat", "content": "You're interested in Adam modules? We actually have a few options available for you. Adam is a brand that offers high-quality, affordable car audio systems.\n\nFor the Adam modules, we have a few different models in stock, but I can give you some general information. Our prices for the Adam modules start from around ¥15,000 to ¥50,000. We have a range of products, including amplifiers, equalizers, and speakers.\n\nIf you're looking for a specific model, please let me know and I can check our current stock and pricing for you. We also have a few demo units available for viewing.\n\nWould you like to know more about a specific model or would you like me to check our current inventory for you?", "subject": "Chatbot Response", "id": "3bc21630-b61c-4fa6-817e-c8f67e32f4ee", "createdAt": "2025-07-01T01:06:02.573Z"}, {"direction": "inbound", "channel": "website", "tags": ["chatbot", "user_message"], "createdBy": "anonymous", "type": "chat", "content": "Do you offer free shipping", "subject": "Chatbot Conversation", "id": "809ed183-5aba-4d46-8e72-4cfeace2de92", "createdAt": "2025-07-01T01:16:17.519Z"}, {"direction": "outbound", "channel": "website", "tags": ["chatbot", "bot_response"], "createdBy": "system", "type": "chat", "content": "Hi there! I'm the EBAM Motors assistant. How can I help you find the perfect used goods from Japan today?", "subject": "Chatbot Response", "id": "9d3a4298-fde9-4fe3-a9e0-608b88586380", "createdAt": "2025-07-01T01:16:18.751Z"}, {"direction": "inbound", "channel": "website", "tags": ["chatbot", "user_message"], "createdBy": "anonymous", "type": "chat", "content": "Do you offer free shipping", "subject": "Chatbot Conversation", "id": "c5c11643-a292-4271-885f-4e0c3e177129", "createdAt": "2025-07-01T01:16:56.135Z"}, {"direction": "outbound", "channel": "website", "tags": ["chatbot", "bot_response"], "createdBy": "system", "type": "chat", "content": "Hi there! I'm the EBAM Motors assistant. How can I help you find the perfect used goods from Japan today?", "subject": "Chatbot Response", "id": "57d81a23-a3ef-42d1-9692-f3be477cb96a", "createdAt": "2025-07-01T01:16:57.781Z"}, {"direction": "inbound", "channel": "website", "tags": ["chatbot", "user_message"], "createdBy": "anonymous", "type": "chat", "content": "How much is the shipping cost", "subject": "Chatbot Conversation", "id": "c47827f9-ee4e-4fd3-b335-2a7d0d67e51c", "createdAt": "2025-07-01T01:20:37.190Z"}, {"direction": "outbound", "channel": "website", "tags": ["chatbot", "bot_response"], "createdBy": "system", "type": "chat", "content": "**EBAM Motors Pricing Guide:**\n\n🚗 **Popular Cars (FOB Japan):**\n• Toyota Voxy (8-seater): ¥295,000-¥305,000\n• Toyota Noah (8-seater): ¥345,000-¥355,000\n• Toyota Sienta (7-seater): ¥315,000-¥325,000\n• Toyota Vitz (5-seater): ¥320,000-¥330,000\n• Toyota Yaris (5-seater): ¥545,000-¥555,000\n\n📱 **Electronics:** ¥28,000-¥45,000\n• Refrigerators, Air Conditioners, Washing Machines\n\n🪑 **Furniture:** ¥32,000+\n• Dining sets, Office furniture, Sofas\n\n**Shipping to Ghana/Africa:**\n• Sea Freight (Standard): ¥150,000 (4-5 weeks)\n• Sea Freight (Express): ¥200,000 (3-4 weeks)\n• Air Freight: ¥500,000 (1-2 weeks)\n\nStock ID format: EBM000001. All prices FOB Japan. Contact us for detailed quotes!", "subject": "Chatbot Response", "id": "79b4c07d-4372-4ec6-a7ac-b5852b81d31f", "createdAt": "2025-07-01T01:20:38.439Z"}, {"direction": "inbound", "channel": "website", "tags": ["chatbot", "user_message"], "createdBy": "anonymous", "type": "chat", "content": "What are your prices?", "subject": "Chatbot Conversation", "id": "a9a8163c-3c24-4ce1-a962-a58529e54064", "createdAt": "2025-07-01T01:43:30.671Z"}, {"direction": "outbound", "channel": "website", "tags": ["chatbot", "bot_response"], "createdBy": "system", "type": "chat", "content": "We offer competitive pricing for quality Japanese goods! Prices are updated regularly based on market conditions. Contact us for current quotes on specific items.", "subject": "Chatbot Response", "id": "acb05c20-eb29-41f6-b3aa-d2022cb974e7", "createdAt": "2025-07-01T01:43:34.223Z"}, {"direction": "inbound", "channel": "website", "tags": ["chatbot", "user_message"], "createdBy": "anonymous", "type": "chat", "content": "Toyota voxy prices", "subject": "Chatbot Conversation", "id": "0da17ad3-2ad9-4a63-a088-5badac91d889", "createdAt": "2025-07-01T01:44:18.959Z"}, {"direction": "outbound", "channel": "website", "tags": ["chatbot", "bot_response"], "createdBy": "system", "type": "chat", "content": "We maintain fresh stock of quality Japanese vehicles. All cars come with detailed photos, specifications, and condition reports. What type of vehicle are you looking for?", "subject": "Chatbot Response", "id": "c69afb25-27d5-4443-8377-9e5db758351c", "createdAt": "2025-07-01T01:44:20.111Z"}, {"customerId": "42bf074f-78ea-43b9-adb5-9a0fc2637309", "type": "order", "direction": "inbound", "channel": "website", "content": "Order created: Toyota Voxy 2012 for ¥652,500", "subject": "New Order", "tags": ["order_creation", "purchase"], "relatedOrderId": "be1142ff-9304-4eea-869b-77d26ee08359", "relatedProductId": "toyota_voxy_2012_TVXYA", "createdBy": "42bf074f-78ea-43b9-adb5-9a0fc2637309", "id": "94f01867-a3f4-4004-8417-f03610d52a37", "createdAt": "2025-07-01T02:37:16.176Z"}]