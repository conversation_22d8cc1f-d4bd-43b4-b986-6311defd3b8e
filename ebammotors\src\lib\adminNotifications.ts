import { emailService, AdminNotificationData } from './resendService';

/**
 * Send admin notification for new orders
 */
export async function notifyAdminNewOrder(orderData: {
  orderId: string;
  customerName: string;
  customerEmail: string;
  vehicleTitle: string;
  amount: number;
  paymentMethod: string;
}) {
  const notification: AdminNotificationData = {
    type: 'new_order',
    title: 'New Order Received',
    message: `Order ${orderData.orderId} has been paid and confirmed. Customer: ${orderData.customerName} (${orderData.customerEmail}). Vehicle: ${orderData.vehicleTitle}. Amount: ¥${orderData.amount.toLocaleString()}`,
    data: orderData,
    timestamp: new Date().toISOString()
  };

  try {
    await emailService.sendAdminNotification(notification);
    console.log('Admin new order notification sent successfully');
  } catch (error) {
    console.error('Failed to send admin new order notification:', error);
  }
}

/**
 * Send admin notification for new reviews
 */
export async function notifyAdminNewReview(reviewData: {
  customerName: string;
  vehicleTitle: string;
  rating: number;
  review: string;
  reviewId: string;
}) {
  const notification: AdminNotificationData = {
    type: 'new_review',
    title: 'New Review Submitted',
    message: `New review submitted by ${reviewData.customerName} for ${reviewData.vehicleTitle}. Rating: ${reviewData.rating}/5 stars. Review ID: ${reviewData.reviewId}`,
    data: reviewData,
    timestamp: new Date().toISOString()
  };

  try {
    await emailService.sendAdminNotification(notification);
    console.log('Admin new review notification sent successfully');
  } catch (error) {
    console.error('Failed to send admin new review notification:', error);
  }
}

/**
 * Send admin notification for contact form submissions
 */
export async function notifyAdminContactForm(contactData: {
  name: string;
  email: string;
  subject: string;
  message: string;
  submissionDate: string;
}) {
  const notification: AdminNotificationData = {
    type: 'contact_form',
    title: 'New Contact Form Submission',
    message: `New contact form submitted by ${contactData.name} (${contactData.email}). Subject: ${contactData.subject}`,
    data: contactData,
    timestamp: new Date().toISOString()
  };

  try {
    await emailService.sendAdminNotification(notification);
    console.log('Admin contact form notification sent successfully');
  } catch (error) {
    console.error('Failed to send admin contact form notification:', error);
  }
}

/**
 * Send admin notification for low stock alerts
 */
export async function notifyAdminLowStock(stockData: {
  vehicleId: string;
  vehicleTitle: string;
  currentStock: number;
  threshold: number;
}) {
  const notification: AdminNotificationData = {
    type: 'low_stock',
    title: 'Low Stock Alert',
    message: `Low stock alert for ${stockData.vehicleTitle} (ID: ${stockData.vehicleId}). Current stock: ${stockData.currentStock}, Threshold: ${stockData.threshold}`,
    data: stockData,
    timestamp: new Date().toISOString()
  };

  try {
    await emailService.sendAdminNotification(notification);
    console.log('Admin low stock notification sent successfully');
  } catch (error) {
    console.error('Failed to send admin low stock notification:', error);
  }
}

/**
 * Send admin notification for system alerts
 */
export async function notifyAdminSystemAlert(alertData: {
  alertType: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  details?: any;
}) {
  const notification: AdminNotificationData = {
    type: 'system_alert',
    title: `System Alert - ${alertData.alertType}`,
    message: `${alertData.severity.toUpperCase()} severity alert: ${alertData.message}`,
    data: alertData,
    timestamp: new Date().toISOString()
  };

  try {
    await emailService.sendAdminNotification(notification);
    console.log('Admin system alert notification sent successfully');
  } catch (error) {
    console.error('Failed to send admin system alert notification:', error);
  }
}

/**
 * Send admin notification for failed payments
 */
export async function notifyAdminFailedPayment(paymentData: {
  orderId: string;
  customerName: string;
  customerEmail: string;
  amount: number;
  paymentMethod: string;
  errorMessage: string;
}) {
  const notification: AdminNotificationData = {
    type: 'system_alert',
    title: 'Payment Failed',
    message: `Payment failed for order ${paymentData.orderId}. Customer: ${paymentData.customerName} (${paymentData.customerEmail}). Amount: ¥${paymentData.amount.toLocaleString()}. Error: ${paymentData.errorMessage}`,
    data: paymentData,
    timestamp: new Date().toISOString()
  };

  try {
    await emailService.sendAdminNotification(notification);
    console.log('Admin failed payment notification sent successfully');
  } catch (error) {
    console.error('Failed to send admin failed payment notification:', error);
  }
}

/**
 * Send admin notification for new leads from chatbot
 */
export async function notifyAdminNewLead(leadData: {
  leadId: string;
  customerName: string;
  customerEmail: string;
  source: string;
  priority: string;
  inquiry: string;
}) {
  const notification: AdminNotificationData = {
    type: 'new_review', // Using existing type for leads
    title: 'New Lead Generated',
    message: `New lead generated from ${leadData.source}. Customer: ${leadData.customerName} (${leadData.customerEmail}). Priority: ${leadData.priority}. Lead ID: ${leadData.leadId}`,
    data: leadData,
    timestamp: new Date().toISOString()
  };

  try {
    await emailService.sendAdminNotification(notification);
    console.log('Admin new lead notification sent successfully');
  } catch (error) {
    console.error('Failed to send admin new lead notification:', error);
  }
}

/**
 * Send admin notification for shipping updates
 */
export async function notifyAdminShippingUpdate(shippingData: {
  orderId: string;
  customerName: string;
  status: string;
  location: string;
  description: string;
}) {
  const notification: AdminNotificationData = {
    type: 'system_alert',
    title: 'Shipping Update Required',
    message: `Shipping update for order ${shippingData.orderId}. Customer: ${shippingData.customerName}. Status: ${shippingData.status}. Location: ${shippingData.location}`,
    data: shippingData,
    timestamp: new Date().toISOString()
  };

  try {
    await emailService.sendAdminNotification(notification);
    console.log('Admin shipping update notification sent successfully');
  } catch (error) {
    console.error('Failed to send admin shipping update notification:', error);
  }
}

/**
 * Send daily summary notification to admin
 */
export async function sendDailySummaryToAdmin(summaryData: {
  date: string;
  newOrders: number;
  totalRevenue: number;
  newReviews: number;
  newLeads: number;
  contactForms: number;
  pendingTasks: number;
}) {
  const notification: AdminNotificationData = {
    type: 'system_alert',
    title: 'Daily Summary Report',
    message: `Daily summary for ${summaryData.date}: ${summaryData.newOrders} new orders, ¥${summaryData.totalRevenue.toLocaleString()} revenue, ${summaryData.newReviews} reviews, ${summaryData.newLeads} leads, ${summaryData.contactForms} contact forms, ${summaryData.pendingTasks} pending tasks.`,
    data: summaryData,
    timestamp: new Date().toISOString()
  };

  try {
    await emailService.sendAdminNotification(notification);
    console.log('Daily summary notification sent successfully');
  } catch (error) {
    console.error('Failed to send daily summary notification:', error);
  }
}
