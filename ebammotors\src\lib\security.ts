import { NextRequest } from 'next/server';

// Rate limiting configuration
const RATE_LIMITS = {
  auth: { maxAttempts: 5, windowMs: 15 * 60 * 1000 }, // 5 attempts per 15 minutes
  api: { maxRequests: 100, windowMs: 60 * 1000 }, // 100 requests per minute
  contact: { maxSubmissions: 3, windowMs: 60 * 60 * 1000 }, // 3 submissions per hour
  review: { maxSubmissions: 2, windowMs: 60 * 60 * 1000 }, // 2 reviews per hour
};

// In-memory rate limit store (use Redis in production)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

/**
 * Generic rate limiter
 */
export function checkRateLimit(
  identifier: string, 
  type: keyof typeof RATE_LIMITS
): { allowed: boolean; remaining: number; resetTime: number } {
  const config = RATE_LIMITS[type];
  const now = Date.now();
  const key = `${type}:${identifier}`;
  
  const record = rateLimitStore.get(key);
  
  // No previous record or window expired
  if (!record || now > record.resetTime) {
    const newRecord = { count: 1, resetTime: now + config.windowMs };
    rateLimitStore.set(key, newRecord);
    return {
      allowed: true,
      remaining: config.maxAttempts - 1,
      resetTime: newRecord.resetTime
    };
  }
  
  // Check if limit exceeded
  if (record.count >= config.maxAttempts) {
    return {
      allowed: false,
      remaining: 0,
      resetTime: record.resetTime
    };
  }
  
  // Increment count
  record.count++;
  rateLimitStore.set(key, record);
  
  return {
    allowed: true,
    remaining: config.maxAttempts - record.count,
    resetTime: record.resetTime
  };
}

/**
 * Reset rate limit for a specific identifier and type
 */
export function resetRateLimit(identifier: string, type: keyof typeof RATE_LIMITS): void {
  const key = `${type}:${identifier}`;
  rateLimitStore.delete(key);
}

/**
 * Clean up expired rate limit records
 */
export function cleanupRateLimits(): void {
  const now = Date.now();
  for (const [key, record] of rateLimitStore.entries()) {
    if (now > record.resetTime) {
      rateLimitStore.delete(key);
    }
  }
}

/**
 * Get client IP address from request
 */
export function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const remoteAddr = request.headers.get('remote-addr');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  return realIP || remoteAddr || 'unknown';
}

/**
 * Input sanitization
 */
export function sanitizeInput(input: string): string {
  if (typeof input !== 'string') return '';
  
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .substring(0, 1000); // Limit length
}

/**
 * Validate email format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) && email.length <= 254;
}

/**
 * Validate phone number format
 */
export function isValidPhone(phone: string): boolean {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
}

/**
 * Check for suspicious patterns in text
 */
export function containsSuspiciousContent(text: string): boolean {
  const suspiciousPatterns = [
    /script/gi,
    /javascript/gi,
    /vbscript/gi,
    /onload/gi,
    /onerror/gi,
    /onclick/gi,
    /<iframe/gi,
    /<object/gi,
    /<embed/gi,
    /eval\(/gi,
    /document\.cookie/gi,
    /window\.location/gi,
  ];
  
  return suspiciousPatterns.some(pattern => pattern.test(text));
}

/**
 * Validate file upload
 */
export function validateFileUpload(file: File, allowedTypes: string[], maxSize: number): { valid: boolean; error?: string } {
  // Check file size
  if (file.size > maxSize) {
    return { valid: false, error: `File size exceeds ${maxSize / 1024 / 1024}MB limit` };
  }
  
  // Check file type
  if (!allowedTypes.includes(file.type)) {
    return { valid: false, error: `File type ${file.type} not allowed` };
  }
  
  // Check file name for suspicious content
  if (containsSuspiciousContent(file.name)) {
    return { valid: false, error: 'File name contains suspicious content' };
  }
  
  return { valid: true };
}

/**
 * Generate secure random string
 */
export function generateSecureToken(length: number = 32): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  
  return result;
}

/**
 * Security headers for API responses
 */
export function getSecurityHeaders(): Record<string, string> {
  return {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';",
  };
}

/**
 * Log security events
 */
export function logSecurityEvent(event: string, details: any, request: NextRequest): void {
  const timestamp = new Date().toISOString();
  const clientIP = getClientIP(request);
  const userAgent = request.headers.get('user-agent') || 'unknown';
  
  console.warn(`[SECURITY] ${timestamp} - ${event}`, {
    ip: clientIP,
    userAgent,
    details,
    url: request.url,
  });
}

/**
 * Check if request is from a suspicious source
 */
export function isSuspiciousRequest(request: NextRequest): boolean {
  const userAgent = request.headers.get('user-agent') || '';
  const referer = request.headers.get('referer') || '';
  
  // Check for common bot patterns
  const botPatterns = [
    /bot/i,
    /crawler/i,
    /spider/i,
    /scraper/i,
    /curl/i,
    /wget/i,
  ];
  
  // Check for suspicious user agents
  if (botPatterns.some(pattern => pattern.test(userAgent))) {
    return true;
  }
  
  // Check for empty user agent
  if (!userAgent.trim()) {
    return true;
  }
  
  // Check for suspicious referers
  if (referer && !referer.includes(request.headers.get('host') || '')) {
    // External referer - might be suspicious depending on context
    return false; // Allow for now, but log
  }
  
  return false;
}

// Cleanup expired rate limits every 5 minutes
setInterval(cleanupRateLimits, 5 * 60 * 1000);
