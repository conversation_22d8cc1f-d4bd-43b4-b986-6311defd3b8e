// Date utility functions to prevent hydration mismatches

/**
 * Format date consistently for SSR/client rendering
 * Uses a fixed format to avoid locale-based hydration mismatches
 */
export function formatDate(dateString: string | Date): string {
  try {
    const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
    
    // Use a consistent format that works across server/client
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    return `${day}/${month}/${year}`;
  } catch (error) {
    return 'Invalid Date';
  }
}

/**
 * Format date and time consistently for SSR/client rendering
 */
export function formatDateTime(dateString: string | Date): string {
  try {
    const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
    
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${day}/${month}/${year} ${hours}:${minutes}`;
  } catch (error) {
    return 'Invalid Date';
  }
}

/**
 * Format number consistently for SSR/client rendering
 * Avoids locale-based number formatting issues
 */
export function formatNumber(num: number): string {
  try {
    // Use a simple comma separator that works consistently
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  } catch (error) {
    return '0';
  }
}

/**
 * Format currency consistently
 */
export function formatCurrency(amount: number, currency: string = '¥'): string {
  try {
    return `${currency}${formatNumber(amount)}`;
  } catch (error) {
    return `${currency}0`;
  }
}

/**
 * Get relative time string (e.g., "2 days ago")
 * Uses consistent calculation to avoid hydration issues
 */
export function getRelativeTime(dateString: string | Date): string {
  try {
    const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
    const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));

    if (diffInDays > 0) {
      return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
    } else if (diffInHours > 0) {
      return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
    } else if (diffInMinutes > 0) {
      return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;
    } else {
      return 'Just now';
    }
  } catch (error) {
    return 'Unknown';
  }
}

/**
 * Check if a date is today
 */
export function isToday(dateString: string | Date): boolean {
  try {
    const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
    const today = new Date();
    
    return date.getDate() === today.getDate() &&
           date.getMonth() === today.getMonth() &&
           date.getFullYear() === today.getFullYear();
  } catch (error) {
    return false;
  }
}

/**
 * Check if a date is within the last N days
 */
export function isWithinDays(dateString: string | Date, days: number): boolean {
  try {
    const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInDays = diffInMs / (1000 * 60 * 60 * 24);
    
    return diffInDays <= days && diffInDays >= 0;
  } catch (error) {
    return false;
  }
}
