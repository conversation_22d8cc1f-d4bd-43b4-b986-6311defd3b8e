import { promises as fs } from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { Lead, Customer, Interaction, FollowUp, CustomerActivity } from '@/types/payment';

// File paths
const DATA_DIR = path.join(process.cwd(), 'data');
const LEADS_FILE = path.join(DATA_DIR, 'leads.json');
const CUSTOMERS_FILE = path.join(DATA_DIR, 'customers.json');
const INTERACTIONS_FILE = path.join(DATA_DIR, 'interactions.json');
const FOLLOWUPS_FILE = path.join(DATA_DIR, 'followups.json');
const ACTIVITIES_FILE = path.join(DATA_DIR, 'activities.json');

// Check if running in serverless environment
const isServerless = process.env.VERCEL || process.env.NETLIFY || process.env.AWS_LAMBDA_FUNCTION_NAME;

// In-memory storage for serverless environments
let leadsMemoryStore: Lead[] = [];
let customersMemoryStore: Customer[] = [];
let interactionsMemoryStore: Interaction[] = [];
let followupsMemoryStore: FollowUp[] = [];
let activitiesMemoryStore: CustomerActivity[] = [];

/**
 * Ensure data directory exists
 */
async function ensureDataDirectory(): Promise<void> {
  if (isServerless) return;
  
  try {
    await fs.access(DATA_DIR);
  } catch {
    await fs.mkdir(DATA_DIR, { recursive: true });
  }
}

// LEADS MANAGEMENT

/**
 * Load leads from storage
 */
async function loadLeads(): Promise<Lead[]> {
  if (isServerless) {
    return leadsMemoryStore;
  }

  try {
    await ensureDataDirectory();
    const data = await fs.readFile(LEADS_FILE, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    return [];
  }
}

/**
 * Save leads to storage
 */
async function saveLeads(leads: Lead[]): Promise<void> {
  if (isServerless) {
    leadsMemoryStore = leads;
    return;
  }

  await ensureDataDirectory();
  await fs.writeFile(LEADS_FILE, JSON.stringify(leads, null, 2));
}

/**
 * Create a new lead
 */
export async function createLead(leadData: Omit<Lead, 'id' | 'createdAt' | 'updatedAt'>): Promise<Lead> {
  const leads = await loadLeads();
  
  const newLead: Lead = {
    ...leadData,
    id: uuidv4(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  leads.push(newLead);
  await saveLeads(leads);
  
  return newLead;
}

/**
 * Get all leads
 */
export async function getAllLeads(): Promise<Lead[]> {
  return await loadLeads();
}

/**
 * Get lead by ID
 */
export async function getLeadById(leadId: string): Promise<Lead | null> {
  const leads = await loadLeads();
  return leads.find(lead => lead.id === leadId) || null;
}

/**
 * Update lead
 */
export async function updateLead(leadId: string, updates: Partial<Lead>): Promise<boolean> {
  const leads = await loadLeads();
  const leadIndex = leads.findIndex(lead => lead.id === leadId);
  
  if (leadIndex === -1) return false;
  
  leads[leadIndex] = {
    ...leads[leadIndex],
    ...updates,
    updatedAt: new Date().toISOString(),
  };
  
  await saveLeads(leads);
  return true;
}

/**
 * Delete lead
 */
export async function deleteLead(leadId: string): Promise<boolean> {
  const leads = await loadLeads();
  const filteredLeads = leads.filter(lead => lead.id !== leadId);
  
  if (filteredLeads.length === leads.length) return false;
  
  await saveLeads(filteredLeads);
  return true;
}

/**
 * Get leads by status
 */
export async function getLeadsByStatus(status: Lead['status']): Promise<Lead[]> {
  const leads = await loadLeads();
  return leads.filter(lead => lead.status === status);
}

/**
 * Get leads by source
 */
export async function getLeadsBySource(source: Lead['source']): Promise<Lead[]> {
  const leads = await loadLeads();
  return leads.filter(lead => lead.source === source);
}

// CUSTOMERS MANAGEMENT

/**
 * Load customers from storage
 */
async function loadCustomers(): Promise<Customer[]> {
  if (isServerless) {
    return customersMemoryStore;
  }

  try {
    await ensureDataDirectory();
    const data = await fs.readFile(CUSTOMERS_FILE, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    return [];
  }
}

/**
 * Save customers to storage
 */
async function saveCustomers(customers: Customer[]): Promise<void> {
  if (isServerless) {
    customersMemoryStore = customers;
    return;
  }

  await ensureDataDirectory();
  await fs.writeFile(CUSTOMERS_FILE, JSON.stringify(customers, null, 2));
}

/**
 * Create or update customer
 */
export async function upsertCustomer(customerData: Omit<Customer, 'id' | 'createdAt' | 'updatedAt'>): Promise<Customer> {
  const customers = await loadCustomers();
  
  // Check if customer exists by email
  const existingCustomerIndex = customers.findIndex(c => c.personalInfo.email === customerData.personalInfo.email);
  
  if (existingCustomerIndex !== -1) {
    // Update existing customer
    customers[existingCustomerIndex] = {
      ...customers[existingCustomerIndex],
      ...customerData,
      updatedAt: new Date().toISOString(),
    };
    await saveCustomers(customers);
    return customers[existingCustomerIndex];
  } else {
    // Create new customer
    const newCustomer: Customer = {
      ...customerData,
      id: uuidv4(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    customers.push(newCustomer);
    await saveCustomers(customers);
    return newCustomer;
  }
}

/**
 * Get all customers
 */
export async function getAllCustomers(): Promise<Customer[]> {
  return await loadCustomers();
}

/**
 * Get customer by ID
 */
export async function getCustomerById(customerId: string): Promise<Customer | null> {
  const customers = await loadCustomers();
  return customers.find(customer => customer.id === customerId) || null;
}

/**
 * Get customer by email
 */
export async function getCustomerByEmail(email: string): Promise<Customer | null> {
  const customers = await loadCustomers();
  return customers.find(customer => customer.personalInfo.email === email) || null;
}

/**
 * Update customer
 */
export async function updateCustomer(customerId: string, updates: Partial<Customer>): Promise<boolean> {
  const customers = await loadCustomers();
  const customerIndex = customers.findIndex(customer => customer.id === customerId);
  
  if (customerIndex === -1) return false;
  
  customers[customerIndex] = {
    ...customers[customerIndex],
    ...updates,
    updatedAt: new Date().toISOString(),
  };
  
  await saveCustomers(customers);
  return true;
}

// INTERACTIONS MANAGEMENT

/**
 * Load interactions from storage
 */
async function loadInteractions(): Promise<Interaction[]> {
  if (isServerless) {
    return interactionsMemoryStore;
  }

  try {
    await ensureDataDirectory();
    const data = await fs.readFile(INTERACTIONS_FILE, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    return [];
  }
}

/**
 * Save interactions to storage
 */
async function saveInteractions(interactions: Interaction[]): Promise<void> {
  if (isServerless) {
    interactionsMemoryStore = interactions;
    return;
  }

  await ensureDataDirectory();
  await fs.writeFile(INTERACTIONS_FILE, JSON.stringify(interactions, null, 2));
}

/**
 * Create a new interaction
 */
export async function createInteraction(interactionData: Omit<Interaction, 'id' | 'createdAt'>): Promise<Interaction> {
  const interactions = await loadInteractions();
  
  const newInteraction: Interaction = {
    ...interactionData,
    id: uuidv4(),
    createdAt: new Date().toISOString(),
  };

  interactions.push(newInteraction);
  await saveInteractions(interactions);
  
  return newInteraction;
}

/**
 * Get all interactions
 */
export async function getAllInteractions(): Promise<Interaction[]> {
  return await loadInteractions();
}

/**
 * Get interactions by customer ID
 */
export async function getInteractionsByCustomerId(customerId: string): Promise<Interaction[]> {
  const interactions = await loadInteractions();
  return interactions.filter(interaction => interaction.customerId === customerId);
}

/**
 * Get interactions by lead ID
 */
export async function getInteractionsByLeadId(leadId: string): Promise<Interaction[]> {
  const interactions = await loadInteractions();
  return interactions.filter(interaction => interaction.leadId === leadId);
}

// FOLLOW-UPS MANAGEMENT

/**
 * Load follow-ups from storage
 */
async function loadFollowUps(): Promise<FollowUp[]> {
  if (isServerless) {
    return followupsMemoryStore;
  }

  try {
    await ensureDataDirectory();
    const data = await fs.readFile(FOLLOWUPS_FILE, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    return [];
  }
}

/**
 * Save follow-ups to storage
 */
async function saveFollowUps(followups: FollowUp[]): Promise<void> {
  if (isServerless) {
    followupsMemoryStore = followups;
    return;
  }

  await ensureDataDirectory();
  await fs.writeFile(FOLLOWUPS_FILE, JSON.stringify(followups, null, 2));
}

/**
 * Create a new follow-up
 */
export async function createFollowUp(followupData: Omit<FollowUp, 'id' | 'createdAt' | 'updatedAt'>): Promise<FollowUp> {
  const followups = await loadFollowUps();

  const newFollowUp: FollowUp = {
    ...followupData,
    id: uuidv4(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  followups.push(newFollowUp);
  await saveFollowUps(followups);

  return newFollowUp;
}

/**
 * Get all follow-ups
 */
export async function getAllFollowUps(): Promise<FollowUp[]> {
  return await loadFollowUps();
}

/**
 * Get follow-ups by status
 */
export async function getFollowUpsByStatus(status: FollowUp['status']): Promise<FollowUp[]> {
  const followups = await loadFollowUps();
  return followups.filter(followup => followup.status === status);
}

/**
 * Get pending follow-ups (due now or overdue)
 */
export async function getPendingFollowUps(): Promise<FollowUp[]> {
  const followups = await loadFollowUps();
  const now = new Date().toISOString();

  return followups.filter(followup =>
    followup.status === 'pending' &&
    followup.scheduledDate <= now
  );
}

/**
 * Update follow-up
 */
export async function updateFollowUp(followupId: string, updates: Partial<FollowUp>): Promise<boolean> {
  const followups = await loadFollowUps();
  const followupIndex = followups.findIndex(followup => followup.id === followupId);

  if (followupIndex === -1) return false;

  followups[followupIndex] = {
    ...followups[followupIndex],
    ...updates,
    updatedAt: new Date().toISOString(),
  };

  await saveFollowUps(followups);
  return true;
}

/**
 * Get follow-ups by customer ID
 */
export async function getFollowUpsByCustomerId(customerId: string): Promise<FollowUp[]> {
  const followups = await loadFollowUps();
  return followups.filter(followup => followup.customerId === customerId);
}

// CUSTOMER ACTIVITIES MANAGEMENT

/**
 * Load activities from storage
 */
async function loadActivities(): Promise<CustomerActivity[]> {
  if (isServerless) {
    return activitiesMemoryStore;
  }

  try {
    await ensureDataDirectory();
    const data = await fs.readFile(ACTIVITIES_FILE, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    return [];
  }
}

/**
 * Save activities to storage
 */
async function saveActivities(activities: CustomerActivity[]): Promise<void> {
  if (isServerless) {
    activitiesMemoryStore = activities;
    return;
  }

  await ensureDataDirectory();
  await fs.writeFile(ACTIVITIES_FILE, JSON.stringify(activities, null, 2));
}

/**
 * Create a new customer activity
 */
export async function createCustomerActivity(activityData: Omit<CustomerActivity, 'id' | 'timestamp'>): Promise<CustomerActivity> {
  const activities = await loadActivities();

  const newActivity: CustomerActivity = {
    ...activityData,
    id: uuidv4(),
    timestamp: new Date().toISOString(),
  };

  activities.push(newActivity);
  await saveActivities(activities);

  return newActivity;
}

/**
 * Get activities by customer ID
 */
export async function getActivitiesByCustomerId(customerId: string): Promise<CustomerActivity[]> {
  const activities = await loadActivities();
  return activities.filter(activity => activity.customerId === customerId);
}

/**
 * Get recent activities (last 30 days)
 */
export async function getRecentActivities(days: number = 30): Promise<CustomerActivity[]> {
  const activities = await loadActivities();
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - days);

  return activities.filter(activity =>
    new Date(activity.timestamp) >= cutoffDate
  ).sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
}

// UTILITY FUNCTIONS

/**
 * Get customer overview with stats
 */
export async function getCustomerOverview(customerId: string) {
  const customer = await getCustomerById(customerId);
  if (!customer) return null;

  const interactions = await getInteractionsByCustomerId(customerId);
  const followups = await getFollowUpsByCustomerId(customerId);
  const activities = await getActivitiesByCustomerId(customerId);

  return {
    customer,
    stats: {
      totalInteractions: interactions.length,
      pendingFollowUps: followups.filter(f => f.status === 'pending').length,
      recentActivities: activities.filter(a =>
        new Date(a.timestamp) >= new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
      ).length,
      lastInteraction: interactions.sort((a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      )[0]?.createdAt,
    },
    recentInteractions: interactions
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, 5),
    upcomingFollowUps: followups
      .filter(f => f.status === 'pending')
      .sort((a, b) => new Date(a.scheduledDate).getTime() - new Date(b.scheduledDate).getTime())
      .slice(0, 3),
  };
}
