import { PaymentMethod, ShippingMethod } from '@/types/payment';

/**
 * Available payment methods for Ghana and international customers
 */
export const PAYMENT_METHODS: PaymentMethod[] = [
  {
    id: 'bank_transfer_ghana',
    type: 'bank_transfer',
    name: 'Bank Transfer (Ghana)',
    description: 'Direct bank transfer to our Ghana account',
    icon: '🏦',
    enabled: true,
    config: {
      bankName: 'Ghana Commercial Bank',
      accountNumber: '*************',
      accountName: 'EBAM Motors Ghana Ltd',
      swiftCode: 'GCBLGHAC',
    },
  },
  {
    id: 'bank_transfer_japan',
    type: 'bank_transfer',
    name: 'Bank Transfer (Japan)',
    description: 'Direct bank transfer to our Japan account',
    icon: '🏦',
    enabled: true,
    config: {
      bankName: 'Mizuho Bank',
      accountNumber: '*************',
      accountName: 'EBAM Motors Japan KK',
      swiftCode: 'MHCBJPJT',
    },
  },
  {
    id: 'mtn_mobile_money',
    type: 'mobile_money',
    name: 'MTN Mobile Money',
    description: 'Pay using MTN Mobile Money',
    icon: '📱',
    enabled: true,
    config: {
      provider: 'mtn',
      number: '+************',
    },
  },
  {
    id: 'vodafone_cash',
    type: 'mobile_money',
    name: 'Vodafone Cash',
    description: 'Pay using Vodafone Cash',
    icon: '📱',
    enabled: true,
    config: {
      provider: 'vodafone',
      number: '+************',
    },
  },
  {
    id: 'airtel_money',
    type: 'mobile_money',
    name: 'AirtelTigo Money',
    description: 'Pay using AirtelTigo Money',
    icon: '📱',
    enabled: true,
    config: {
      provider: 'airtel',
      number: '+************',
    },
  },
  {
    id: 'stripe_card',
    type: 'stripe',
    name: 'Credit/Debit Card',
    description: 'Pay securely with your credit or debit card',
    icon: '💳',
    enabled: true,
    config: {
      publishableKey: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,
    },
  },
  {
    id: 'cash_agent_kumasi',
    type: 'cash_agent',
    name: 'Cash Payment (Kumasi)',
    description: 'Pay cash through our agent in Kumasi',
    icon: '💵',
    enabled: true,
    config: {
      agentLocations: ['Kumasi Central Market', 'Adum Shopping Center', 'KNUST Campus'],
    },
  },
  {
    id: 'cash_agent_accra',
    type: 'cash_agent',
    name: 'Cash Payment (Accra)',
    description: 'Pay cash through our agent in Accra',
    icon: '💵',
    enabled: true,
    config: {
      agentLocations: ['Makola Market', 'Osu Oxford Street', 'East Legon'],
    },
  },
];

/**
 * Available shipping methods
 */
export const SHIPPING_METHODS: ShippingMethod[] = [
  {
    id: 'sea_freight_standard',
    name: 'Sea Freight (Standard)',
    description: 'Most economical option, 4-6 weeks delivery',
    estimatedDays: 35,
    basePrice: 150000, // ¥150,000
    pricePerKg: 50,
    maxWeight: 2000,
    trackingIncluded: true,
    insuranceIncluded: true,
  },
  {
    id: 'sea_freight_express',
    name: 'Sea Freight (Express)',
    description: 'Faster sea shipping, 3-4 weeks delivery',
    estimatedDays: 25,
    basePrice: 200000, // ¥200,000
    pricePerKg: 75,
    maxWeight: 2000,
    trackingIncluded: true,
    insuranceIncluded: true,
  },
  {
    id: 'air_freight',
    name: 'Air Freight',
    description: 'Fastest option, 1-2 weeks delivery',
    estimatedDays: 10,
    basePrice: 500000, // ¥500,000
    pricePerKg: 200,
    maxWeight: 500,
    trackingIncluded: true,
    insuranceIncluded: true,
  },
];

/**
 * Vehicle weight estimation based on category
 */
export const VEHICLE_WEIGHTS: Record<string, number> = {
  'compact': 1200, // kg
  'sedan': 1400,
  'suv': 1800,
  'van': 1600,
  'truck': 2500,
  'motorcycle': 200,
  'default': 1500,
};

/**
 * Get payment method by ID
 */
export function getPaymentMethodById(id: string): PaymentMethod | null {
  return PAYMENT_METHODS.find(method => method.id === id) || null;
}

/**
 * Get enabled payment methods
 */
export function getEnabledPaymentMethods(): PaymentMethod[] {
  return PAYMENT_METHODS.filter(method => method.enabled);
}

/**
 * Get shipping method by ID
 */
export function getShippingMethodById(id: string): ShippingMethod | null {
  return SHIPPING_METHODS.find(method => method.id === id) || null;
}

/**
 * Calculate shipping cost
 */
export function calculateShippingCost(
  shippingMethodId: string,
  vehicleCategory: string,
  destination: string = 'ghana'
): number {
  const method = getShippingMethodById(shippingMethodId);
  if (!method) return 0;

  const weight = VEHICLE_WEIGHTS[vehicleCategory.toLowerCase()] || VEHICLE_WEIGHTS.default;
  const weightCost = method.pricePerKg ? weight * method.pricePerKg : 0;
  
  // Add destination multiplier
  let destinationMultiplier = 1;
  if (destination.toLowerCase() !== 'ghana') {
    destinationMultiplier = 1.2; // 20% extra for other destinations
  }

  return Math.round((method.basePrice + weightCost) * destinationMultiplier);
}

/**
 * Get estimated delivery date
 */
export function getEstimatedDeliveryDate(shippingMethodId: string): string {
  const method = getShippingMethodById(shippingMethodId);
  if (!method) return '';

  const deliveryDate = new Date();
  deliveryDate.setDate(deliveryDate.getDate() + method.estimatedDays);
  
  return deliveryDate.toISOString().split('T')[0];
}

/**
 * Validate payment method for customer location
 */
export function validatePaymentMethodForLocation(
  paymentMethodId: string,
  customerCountry: string
): boolean {
  const method = getPaymentMethodById(paymentMethodId);
  if (!method) return false;

  // Mobile money only available in Ghana
  if (method.type === 'mobile_money' && customerCountry.toLowerCase() !== 'ghana') {
    return false;
  }

  // Cash agents only available in Ghana
  if (method.type === 'cash_agent' && customerCountry.toLowerCase() !== 'ghana') {
    return false;
  }

  return true;
}

/**
 * Get payment instructions for a method
 */
export function getPaymentInstructions(paymentMethodId: string, amount: number, orderId: string): string {
  const method = getPaymentMethodById(paymentMethodId);
  if (!method) return '';

  switch (method.type) {
    case 'bank_transfer':
      return `Transfer ¥${amount.toLocaleString()} to:
Bank: ${method.config?.bankName}
Account: ${method.config?.accountNumber}
Name: ${method.config?.accountName}
Reference: ${orderId}`;

    case 'mobile_money':
      return `Send ¥${amount.toLocaleString()} to ${method.config?.number}
Reference: ${orderId}
Network: ${method.name}`;

    case 'cash_agent':
      return `Visit one of our agent locations with ¥${amount.toLocaleString()}:
${method.config?.agentLocations?.join(', ')}
Reference: ${orderId}`;

    case 'stripe':
      return `You will be redirected to secure payment page to complete your payment of ¥${amount.toLocaleString()}.`;

    default:
      return `Payment amount: ¥${amount.toLocaleString()}
Order reference: ${orderId}`;
  }
}
