import { Resend } from 'resend';
import { EmailTemplates } from './emailTemplates';

// Initialize Resend with API key
const resend = new Resend(process.env.RESEND_API_KEY);

// Email configuration
export const EMAIL_CONFIG = {
  from: process.env.RESEND_FROM_EMAIL || 'EBAM Motors <<EMAIL>>',
  adminEmail: process.env.ADMIN_EMAIL || '<EMAIL>',
  supportEmail: process.env.SUPPORT_EMAIL || '<EMAIL>',
  noReplyEmail: process.env.NO_REPLY_EMAIL || '<EMAIL>',
};

// Email template interfaces
export interface EmailTemplate {
  to: string | string[];
  subject: string;
  html: string;
  text?: string;
}

export interface OrderConfirmationData {
  customerName: string;
  orderNumber: string;
  orderDate: string;
  vehicle: {
    title: string;
    price: string;
    image: string;
  };
  total: string;
  shippingAddress: {
    street: string;
    city: string;
    state: string;
    country: string;
    postalCode: string;
  };
  estimatedDelivery: string;
}

export interface ReviewNotificationData {
  customerName: string;
  vehicleTitle: string;
  rating: number;
  review: string;
  reviewDate: string;
  isApproval?: boolean;
}

export interface ContactFormData {
  name: string;
  email: string;
  subject: string;
  message: string;
  submissionDate: string;
}

export interface AdminNotificationData {
  type: 'new_order' | 'new_review' | 'contact_form' | 'low_stock' | 'system_alert';
  title: string;
  message: string;
  data?: any;
  timestamp: string;
}

export interface FollowUpData {
  customerName: string;
  customerEmail: string;
  type: 'abandoned_cart' | 'delivery_update' | 'feedback_request' | 'maintenance_reminder';
  data?: any;
}

// Base email service class
export class ResendEmailService {
  private resend: Resend;

  constructor() {
    this.resend = resend;
  }

  /**
   * Send a generic email
   */
  async sendEmail(template: EmailTemplate): Promise<{ success: boolean; messageId?: string; error?: string }> {
    try {
      if (!process.env.RESEND_API_KEY) {
        console.warn('Resend API key not configured. Email not sent.');
        return { success: false, error: 'Resend API key not configured' };
      }

      const result = await this.resend.emails.send({
        from: EMAIL_CONFIG.from,
        to: template.to,
        subject: template.subject,
        html: template.html,
        text: template.text,
      });

      if (result.error) {
        console.error('Resend email error:', result.error);
        return { success: false, error: result.error.message };
      }

      console.log('Email sent successfully:', result.data?.id);
      return { success: true, messageId: result.data?.id };
    } catch (error) {
      console.error('Email service error:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Send order confirmation email
   */
  async sendOrderConfirmation(customerEmail: string, data: OrderConfirmationData): Promise<{ success: boolean; error?: string }> {
    const template: EmailTemplate = {
      to: customerEmail,
      subject: `Order Confirmation - ${data.orderNumber} | EBAM Motors`,
      html: this.generateOrderConfirmationHTML(data),
      text: this.generateOrderConfirmationText(data),
    };

    const result = await this.sendEmail(template);
    return result;
  }

  /**
   * Send review notification to admin
   */
  async sendReviewNotificationToAdmin(data: ReviewNotificationData): Promise<{ success: boolean; error?: string }> {
    const template: EmailTemplate = {
      to: EMAIL_CONFIG.adminEmail,
      subject: `New Review Submitted - ${data.vehicleTitle} | EBAM Motors`,
      html: this.generateReviewNotificationHTML(data),
      text: this.generateReviewNotificationText(data),
    };

    const result = await this.sendEmail(template);
    return result;
  }

  /**
   * Send review approval notification to customer
   */
  async sendReviewApprovalNotification(customerEmail: string, data: ReviewNotificationData): Promise<{ success: boolean; error?: string }> {
    const template: EmailTemplate = {
      to: customerEmail,
      subject: `Your Review Has Been Approved | EBAM Motors`,
      html: this.generateReviewApprovalHTML(data),
      text: this.generateReviewApprovalText(data),
    };

    const result = await this.sendEmail(template);
    return result;
  }

  /**
   * Send contact form submission notification
   */
  async sendContactFormNotification(data: ContactFormData): Promise<{ success: boolean; error?: string }> {
    // Send to admin
    const adminTemplate: EmailTemplate = {
      to: EMAIL_CONFIG.adminEmail,
      subject: `New Contact Form Submission - ${data.subject} | EBAM Motors`,
      html: this.generateContactFormAdminHTML(data),
      text: this.generateContactFormAdminText(data),
    };

    // Send confirmation to customer
    const customerTemplate: EmailTemplate = {
      to: data.email,
      subject: `Thank you for contacting EBAM Motors`,
      html: this.generateContactFormCustomerHTML(data),
      text: this.generateContactFormCustomerText(data),
    };

    const [adminResult, customerResult] = await Promise.all([
      this.sendEmail(adminTemplate),
      this.sendEmail(customerTemplate),
    ]);

    return {
      success: adminResult.success && customerResult.success,
      error: adminResult.error || customerResult.error,
    };
  }

  /**
   * Send admin notification
   */
  async sendAdminNotification(data: AdminNotificationData): Promise<{ success: boolean; error?: string }> {
    const template: EmailTemplate = {
      to: EMAIL_CONFIG.adminEmail,
      subject: `${data.title} | EBAM Motors Admin`,
      html: this.generateAdminNotificationHTML(data),
      text: this.generateAdminNotificationText(data),
    };

    const result = await this.sendEmail(template);
    return result;
  }

  /**
   * Send follow-up email
   */
  async sendFollowUpEmail(data: FollowUpData): Promise<{ success: boolean; error?: string }> {
    let subject = '';
    let html = '';
    let text = '';

    switch (data.type) {
      case 'abandoned_cart':
        subject = 'Complete Your Purchase - Items Still Available | EBAM Motors';
        html = this.generateAbandonedCartHTML(data);
        text = this.generateAbandonedCartText(data);
        break;
      case 'delivery_update':
        subject = 'Delivery Update for Your Order | EBAM Motors';
        html = this.generateDeliveryUpdateHTML(data);
        text = this.generateDeliveryUpdateText(data);
        break;
      case 'feedback_request':
        subject = 'How was your experience with EBAM Motors?';
        html = this.generateFeedbackRequestHTML(data);
        text = this.generateFeedbackRequestText(data);
        break;
      case 'maintenance_reminder':
        subject = 'Vehicle Maintenance Reminder | EBAM Motors';
        html = this.generateMaintenanceReminderHTML(data);
        text = this.generateMaintenanceReminderText(data);
        break;
      default:
        return { success: false, error: 'Unknown follow-up type' };
    }

    const template: EmailTemplate = {
      to: data.customerEmail,
      subject,
      html,
      text,
    };

    const result = await this.sendEmail(template);
    return result;
  }

  // HTML template generators using EmailTemplates class
  private generateOrderConfirmationHTML(data: OrderConfirmationData): string {
    return EmailTemplates.generateOrderConfirmationHTML(data);
  }

  private generateOrderConfirmationText(data: OrderConfirmationData): string {
    return EmailTemplates.generateOrderConfirmationText(data);
  }

  private generateReviewNotificationHTML(data: ReviewNotificationData): string {
    return EmailTemplates.generateReviewNotificationHTML(data);
  }

  private generateReviewNotificationText(data: ReviewNotificationData): string {
    return `New Review from ${data.customerName} for ${data.vehicleTitle} - Rating: ${data.rating}/5`;
  }

  private generateReviewApprovalHTML(data: ReviewNotificationData): string {
    return EmailTemplates.generateReviewApprovalHTML(data);
  }

  private generateReviewApprovalText(data: ReviewNotificationData): string {
    return `Your review for ${data.vehicleTitle} has been approved. Thank you ${data.customerName}!`;
  }

  private generateContactFormAdminHTML(data: ContactFormData): string {
    return EmailTemplates.generateContactFormAdminHTML(data);
  }

  private generateContactFormAdminText(data: ContactFormData): string {
    return `New Contact Form from ${data.name} (${data.email}) - Subject: ${data.subject}`;
  }

  private generateContactFormCustomerHTML(data: ContactFormData): string {
    return EmailTemplates.generateContactFormCustomerHTML(data);
  }

  private generateContactFormCustomerText(data: ContactFormData): string {
    return `Thank you for contacting EBAM Motors, ${data.name}. We received your message about "${data.subject}" and will respond within 24 hours.`;
  }

  private generateAdminNotificationHTML(data: AdminNotificationData): string {
    return `<h1>${data.title}</h1><p>${data.message}</p>`;
  }

  private generateAdminNotificationText(data: AdminNotificationData): string {
    return `${data.title}: ${data.message}`;
  }

  private generateAbandonedCartHTML(data: FollowUpData): string {
    return EmailTemplates.generateAbandonedCartHTML(data);
  }

  private generateAbandonedCartText(data: FollowUpData): string {
    return `Hi ${data.customerName}! You left some amazing vehicles in your cart. Complete your purchase at EBAM Motors and get them shipped to Ghana. Don't miss out!`;
  }

  private generateDeliveryUpdateHTML(data: FollowUpData): string {
    return EmailTemplates.generateDeliveryUpdateHTML(data);
  }

  private generateDeliveryUpdateText(data: FollowUpData): string {
    return `Delivery update for ${data.customerName}: ${data.data?.status || 'Your vehicle is on its way'}. Track your order at ebammotors.com`;
  }

  private generateFeedbackRequestHTML(data: FollowUpData): string {
    return EmailTemplates.generateFeedbackRequestHTML(data);
  }

  private generateFeedbackRequestText(data: FollowUpData): string {
    return `Hi ${data.customerName}! How was your experience with EBAM Motors? We'd love to hear your feedback. Leave a review at ebammotors.com/reviews`;
  }

  private generateMaintenanceReminderHTML(data: FollowUpData): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Vehicle Maintenance Reminder</title>
        <style>
          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }
          .container { max-width: 600px; margin: 0 auto; background-color: white; }
          .header { background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); color: white; padding: 30px; text-align: center; }
          .logo { font-size: 28px; font-weight: bold; margin-bottom: 10px; }
          .content { padding: 30px; }
          .highlight { background-color: #eff6ff; padding: 15px; border-radius: 6px; margin: 15px 0; }
          .button { display: inline-block; background: #1e40af; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }
          .footer { background-color: #f9fafb; padding: 20px; text-align: center; color: #6b7280; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">🚗 EBAM Motors</div>
            <p>Keep your vehicle in perfect condition</p>
          </div>
          <div class="content">
            <h1>Vehicle Maintenance Reminder</h1>
            <p>Hi ${data.customerName},</p>
            <p>It's time for your vehicle's scheduled maintenance to keep it running smoothly and safely.</p>
            <div class="highlight">
              <h3>Recommended Maintenance</h3>
              <p><strong>Vehicle:</strong> ${data.data?.vehicleTitle || 'Your vehicle'}</p>
              <p><strong>Mileage:</strong> ${data.data?.currentMileage || 'Check your odometer'}</p>
              <p><strong>Service Due:</strong> ${data.data?.serviceType || 'Regular maintenance'}</p>
            </div>
            <div style="text-align: center; margin: 30px 0;">
              <a href="https://yourdomain.com/contact" class="button">Schedule Service</a>
            </div>
            <p>Regular maintenance helps ensure:</p>
            <ul>
              <li>🔧 Optimal performance and fuel efficiency</li>
              <li>🛡️ Safety and reliability</li>
              <li>💰 Prevention of costly repairs</li>
              <li>📈 Maintained resale value</li>
            </ul>
          </div>
          <div class="footer">
            <p>Thank you for choosing EBAM Motors!</p>
            <p>© 2024 EBAM Motors. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  private generateMaintenanceReminderText(data: FollowUpData): string {
    return `Hi ${data.customerName}! It's time for your vehicle's scheduled maintenance. Contact EBAM Motors to schedule service and keep your vehicle running smoothly.`;
  }
}

// Export singleton instance
export const emailService = new ResendEmailService();
