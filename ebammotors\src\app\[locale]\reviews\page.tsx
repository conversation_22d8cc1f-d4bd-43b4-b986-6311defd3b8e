import { getMessages } from '@/lib/messages';
import Navigation from '@/components/Navigation';
import CustomerReviews from '@/components/CustomerReviews';
import Link from 'next/link';

export default async function ReviewsPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;
  const messages = await getMessages(locale);

  return (
    <div className="min-h-screen bg-neutral-50">
      <Navigation />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center space-y-6 mb-12">
          <h1 className="text-4xl lg:text-5xl font-heading font-bold text-neutral-800">
            {messages.reviews.title}
          </h1>
          <p className="text-xl text-neutral-600 max-w-2xl mx-auto">
            {messages.reviews.subtitle}
          </p>
        </div>

        {/* Reviews Section */}
        <CustomerReviews locale={locale} />

        {/* Call to Action */}
        <div className="text-center mt-16">
          <div className="bg-white rounded-2xl shadow-lg p-8 max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-neutral-800 mb-4">
              {messages.reviews.shareExperience}
            </h3>
            <p className="text-neutral-600 mb-6">
              {messages.reviews.shareDescription}
            </p>
            <Link
              href={`/${locale}/leave-review`}
              className="inline-flex items-center space-x-2 bg-primary-600 hover:bg-primary-700 text-white px-8 py-4 rounded-xl font-bold transition-all duration-300 hover:scale-105 hover:shadow-lg"
            >
              <span>{messages.reviews.leaveReview}</span>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
