'use client';

import Image from 'next/image';
import { useState, useEffect } from 'react';
import ImageGalleryModal from './ImageGalleryModal';

interface InventoryItem {
  id: number;
  category: string;
  title: string;
  price: string;
  location: string;
  status: string;
  image: string;
  images: string[]; // All images for this item
  specs: string[];
  carId: string; // Unique identifier for each car folder
}

interface InventoryGridProps {
  items: InventoryItem[];
  locale: string;
  filteredCategory: string;
}

export default function InventoryGrid({ items, locale, filteredCategory }: InventoryGridProps) {
  // State for modals and animations
  const [selectedItem, setSelectedItem] = useState<InventoryItem | null>(null);
  const [showImageGallery, setShowImageGallery] = useState(false);
  const [showInquireModal, setShowInquireModal] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState<Record<number, number>>({});

  // Animation trigger
  useEffect(() => {
    setIsVisible(true);
  }, []);

  // Image animation effect
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImageIndex(prev => {
        const newIndex = { ...prev };
        items.forEach(item => {
          if (item.images && item.images.length > 1) {
            newIndex[item.id] = ((prev[item.id] || 0) + 1) % item.images.length;
          }
        });
        return newIndex;
      });
    }, 3000); // Change image every 3 seconds

    return () => clearInterval(interval);
  }, [items]);

  // Function to get car color based on image filename
  const getCarColor = (item: InventoryItem) => {
    const imagePath = item.image;

    // Color mapping based on actual car images
    const colorMap: { [key: string]: { en: string; ja: string } } = {
      // Toyota Voxy 2010
      'toyota_voxy_2010_001.jpg': { en: 'Silver Metallic', ja: 'シルバーメタリック' },
      'toyota_voxy_2010_002.jpg': { en: 'Dark Blue Metallic', ja: 'ダークブルーメタリック' },
      'toyota_voxy_2010_003.jpg': { en: 'White Pearl', ja: 'ホワイトパール' },
      'toyota_voxy_2010_004.jpg': { en: 'Black Metallic', ja: 'ブラックメタリック' },
      'toyota_voxy_2010_005.jpg': { en: 'Gray Metallic', ja: 'グレーメタリック' },
      'toyota_voxy_2010_006.jpg': { en: 'Red Metallic', ja: 'レッドメタリック' },
      'toyota_voxy_2010_007.jpg': { en: 'Blue Metallic', ja: 'ブルーメタリック' },

      // Toyota Voxy 2012
      'toyota_voxy_2012_001.jpg': { en: 'White Pearl Crystal Shine', ja: 'ホワイトパールクリスタルシャイン' },
      'toyota_voxy_2012_002.jpg': { en: 'Silver Metallic', ja: 'シルバーメタリック' },
      'toyota_voxy_2012_003.jpg': { en: 'Black Mica', ja: 'ブラックマイカ' },

      // Toyota Noah 2010
      'toyota_noah_2010_001.jpg': { en: 'Silver Metallic', ja: 'シルバーメタリック' },
      'toyota_noah_2010_002.JPG': { en: 'White', ja: 'ホワイト' },
      'toyota_noah_2010_003.jpg': { en: 'Dark Gray Metallic', ja: 'ダークグレーメタリック' },
      'toyota_noah_2010_004.jpg': { en: 'Blue Metallic', ja: 'ブルーメタリック' },

      // Toyota Sienta 2014
      'toyota_sienta_2014_001.jpg': { en: 'White', ja: 'ホワイト' },
      'toyota_sienta_2014_002.jpg': { en: 'Silver Metallic', ja: 'シルバーメタリック' },
      'toyota_sienta_2014_003.jpg': { en: 'Red', ja: 'レッド' },
      'toyota_sienta_2014_004.jpg': { en: 'Dark Blue Metallic', ja: 'ダークブルーメタリック' },

      // Toyota Vitz 2014
      'toyota_vitz_2014_001.jpg': { en: 'Red', ja: 'レッド' },
      'toyota_vitz_2014_002.jpg': { en: 'White', ja: 'ホワイト' },
      'toyota_vitz_2014_003.jpg': { en: 'Silver Metallic', ja: 'シルバーメタリック' },

      // Toyota Yaris 2015
      'toyota_yaris_2015_001.jpg': { en: 'Blue Metallic', ja: 'ブルーメタリック' },
      'toyota_yaris_2015_002.jpg': { en: 'White Pearl', ja: 'ホワイトパール' },
      'toyota_yaris_2015_003.jpg': { en: 'Silver Metallic', ja: 'シルバーメタリック' },
    };

    // Extract filename from path
    const filename = imagePath.split('/').pop() || '';
    const colorInfo = colorMap[filename];

    if (colorInfo) {
      return locale === 'en' ? colorInfo.en : colorInfo.ja;
    }

    // Default fallback colors
    return locale === 'en' ? 'Silver Metallic' : 'シルバーメタリック';
  };
  
  // Modal handlers
  const handleDetailsClick = (item: InventoryItem) => {
    setSelectedItem(item);
    setShowImageGallery(true);
  };

  const handleInquireClick = (item: InventoryItem) => {
    setSelectedItem(item);
    setShowInquireModal(true);
  };

  const closeModals = () => {
    setShowImageGallery(false);
    setShowInquireModal(false);
    setSelectedItem(null);
  };

  // Filter items based on category
  const filteredItems = filteredCategory === 'all' 
    ? items 
    : items.filter(item => item.category === filteredCategory);

  return (
    <>
      {/* Inventory Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {filteredItems.map((item, index) => (
          <div
            key={item.id}
            className={`bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-500 cursor-pointer group transform ${
              isVisible
                ? 'translate-y-0 opacity-100'
                : 'translate-y-8 opacity-0'
            }`}
            style={{
              transitionDelay: `${index * 100}ms`
            }}
          >
            <div className="relative overflow-hidden" onClick={() => handleDetailsClick(item)}>
              <div className="h-48 relative overflow-hidden">
                <Image
                  src={item.images && item.images.length > 0
                    ? item.images[currentImageIndex[item.id] || 0]
                    : item.image}
                  alt={item.title}
                  fill
                  className="object-cover group-hover:scale-110 transition-all duration-500 ease-out"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                />
                {/* Overlay effect on hover */}
                <div className="absolute inset-0 bg-black opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>

                {/* Image counter indicator */}
                {item.images && item.images.length > 1 && (
                  <div className="absolute bottom-2 right-2 bg-black/70 text-white px-2 py-1 rounded-full text-xs">
                    {(currentImageIndex[item.id] || 0) + 1}/{item.images.length}
                  </div>
                )}
              </div>
              {/* Special badge for Toyota Voxy */}
              {item.title.includes('Toyota Voxy') && (
                <div className="absolute top-4 left-4 px-2 py-1 bg-primary-600 text-white text-xs font-semibold rounded-full">
                  8-Seater
                </div>
              )}
              <div className={`absolute top-4 right-4 px-3 py-1 rounded-full text-xs font-semibold ${
                item.status === 'Available' ? 'bg-green-100 text-green-800' :
                item.status === 'Reserved' ? 'bg-yellow-100 text-yellow-800' :
                'bg-red-100 text-red-800'
              }`}>
                {item.status}
              </div>
            </div>

            <div className="p-6 transform transition-all duration-300" onClick={() => handleDetailsClick(item)}>
              <div className="flex justify-between items-start mb-2">
                <h3 className="text-xl font-semibold text-neutral-800 group-hover:text-primary-600 transition-all duration-300 group-hover:translate-x-1">{item.title}</h3>
                <div className="text-neutral-400 group-hover:text-primary-500 transition-all duration-300 text-sm opacity-0 group-hover:opacity-100 transform translate-x-2 group-hover:translate-x-0">
                  👁️ {locale === 'en' ? 'View Details' : '詳細を見る'}
                </div>
              </div>
              <p className="text-2xl font-bold text-primary-600 mb-2 transform transition-all duration-300 group-hover:scale-105">{item.price}</p>
              <p className="text-neutral-600 mb-4 transition-colors duration-300 group-hover:text-neutral-800">📍 {item.location}</p>

              <div className="mb-4">
                {item.specs.map((spec, specIndex) => (
                  <span
                    key={specIndex}
                    className="inline-block bg-neutral-100 text-neutral-700 px-2 py-1 rounded text-xs mr-2 mb-1 transition-all duration-300 hover:bg-neutral-200 transform hover:scale-105"
                    style={{
                      transitionDelay: `${specIndex * 50}ms`
                    }}
                  >
                    {spec}
                  </span>
                ))}
                {/* Add color indicator for cars */}
                {item.category === 'cars' && (
                  <span className="inline-block bg-primary-100 text-primary-700 px-2 py-1 rounded text-xs mr-2 mb-1 transition-all duration-300 hover:bg-primary-200 transform hover:scale-105 animate-pulse">
                    🎨 {getCarColor(item)}
                  </span>
                )}
              </div>

              <div className="flex flex-col sm:flex-row gap-2 transform transition-all duration-300 group-hover:translate-y-1" onClick={(e) => e.stopPropagation()}>
                <button
                  className={`flex-1 px-4 py-3 sm:py-2 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-lg text-sm sm:text-base ${
                    item.status === 'Available'
                      ? 'bg-primary-600 text-white hover:bg-primary-700 hover:-translate-y-1'
                      : 'bg-neutral-300 text-neutral-500 cursor-not-allowed'
                  }`}
                  disabled={item.status !== 'Available'}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleInquireClick(item);
                  }}
                >
                  {locale === 'en' ? 'Inquire' : 'お問い合わせ'}
                </button>
                <button
                  className="flex-1 sm:flex-none px-4 py-3 sm:py-2 border border-neutral-300 text-neutral-700 rounded-lg hover:border-primary-500 hover:text-primary-600 transition-all duration-300 transform hover:scale-105 hover:shadow-lg hover:-translate-y-1 text-sm sm:text-base"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDetailsClick(item);
                  }}
                >
                  {locale === 'en' ? 'Details' : '詳細'}
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Load More */}
      <div className="text-center mt-12">
        <button className="px-8 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-200 font-semibold">
          {locale === 'en' ? 'Load More Items' : 'さらに表示'}
        </button>
      </div>

      {/* Image Gallery Modal */}
      {showImageGallery && selectedItem && (
        <ImageGalleryModal
          item={selectedItem}
          locale={locale}
          onClose={closeModals}
        />
      )}

      {/* Old Details Modal - keeping for reference but not used */}
      {false && showImageGallery && selectedItem && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-2 sm:p-4 animate-fadeIn">
          <div className="bg-white rounded-xl max-w-4xl w-full max-h-[95vh] sm:max-h-[90vh] overflow-y-auto transform transition-all duration-500 animate-slideInUp">
            <div className="p-4 sm:p-6">
              <div className="flex justify-between items-start mb-4 animate-slideInDown">
                <h2 className="text-xl sm:text-2xl font-bold text-neutral-800 transition-colors duration-300 hover:text-primary-600">
                  {locale === 'en' ? 'Item Details' : '商品詳細'}
                </h2>
                <button
                  onClick={closeModals}
                  className="text-neutral-500 hover:text-neutral-700 text-2xl sm:text-3xl transition-all duration-300 hover:scale-110 hover:rotate-90 p-1"
                >
                  ×
                </button>
              </div>
              
              <div className="space-y-6">
                {/* Image and Basic Info */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                  <div className="relative h-48 sm:h-64 rounded-lg overflow-hidden animate-slideInLeft group">
                    <Image
                      src={selectedItem.image}
                      alt={selectedItem.title}
                      fill
                      className="object-cover transition-transform duration-500 group-hover:scale-110"
                      sizes="(max-width: 768px) 100vw, 50vw"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  </div>

                  <div className="space-y-3 sm:space-y-4 animate-slideInRight">
                  <div className="transform transition-all duration-500 hover:scale-105">
                    <h3 className="text-lg sm:text-xl font-semibold text-neutral-800 mb-2 animate-fadeInUp">
                      {selectedItem.title}
                    </h3>
                    <p className="text-xl sm:text-2xl font-bold text-primary-600 mb-2 animate-fadeInUp animate-pulse">
                      {selectedItem.price}
                    </p>
                    <p className="text-sm sm:text-base text-neutral-600 animate-fadeInUp">
                      📍 {selectedItem.location}
                    </p>
                  </div>
                  
                  <div className="animate-fadeInUp">
                    <h4 className="text-sm sm:text-base font-semibold text-neutral-800 mb-2 transition-colors duration-300 hover:text-primary-600">
                      {locale === 'en' ? 'Basic Specifications' : '基本仕様'}
                    </h4>
                    <div className="flex flex-wrap gap-1 sm:gap-2">
                      {selectedItem.specs.map((spec: string, specIndex: number) => (
                        <span
                          key={specIndex}
                          className="bg-neutral-100 text-neutral-700 px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm transition-all duration-300 hover:bg-primary-100 hover:text-primary-700 transform hover:scale-110 animate-fadeInUp"
                          style={{
                            animationDelay: `${specIndex * 100}ms`
                          }}
                        >
                          {spec}
                        </span>
                      ))}
                    </div>
                  </div>
                  
                  </div>
                </div>

                {/* Detailed Specifications for Cars */}
                {selectedItem.category === 'cars' && (
                    <div className="animate-slideInUp">
                      <h4 className="text-base sm:text-lg font-semibold text-neutral-800 mb-3 border-b border-orange-400 pb-1 transition-all duration-300 hover:text-primary-600 hover:border-primary-400">
                        {locale === 'en' ? 'Detailed Specifications' : '詳細仕様'}
                      </h4>
                      <div className="bg-neutral-50 rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-x-auto">
                        <table className="w-full text-xs sm:text-sm">
                          <tbody>
                            <tr className="border-b border-neutral-200">
                              <td className="bg-neutral-100 px-2 sm:px-3 py-2 font-medium text-neutral-700 w-1/3">
                                {locale === 'en' ? 'Ref No' : '参照番号'}
                              </td>
                              <td className="px-2 sm:px-3 py-2 text-neutral-800">EBM{selectedItem.id.toString().padStart(6, '0')}</td>
                            </tr>
                            <tr className="border-b border-neutral-200">
                              <td className="bg-neutral-100 px-2 sm:px-3 py-2 font-medium text-neutral-700 w-1/3">
                                {locale === 'en' ? 'Chassis #' : 'シャーシ番号'}
                              </td>
                              <td className="px-2 sm:px-3 py-2 text-neutral-800 break-all">
                                {selectedItem.title.includes('Voxy') ? 'ZRR70W-' :
                                 selectedItem.title.includes('Noah') ? 'ZRR75W-' :
                                 selectedItem.title.includes('Sienta') ? 'NCP81G-' :
                                 selectedItem.title.includes('Vitz') ? 'NCP131-' :
                                 selectedItem.title.includes('Yaris') ? 'NCP130-' :
                                 'VZJ95-'}{(selectedItem.id * 1000 + 76053).toString()}
                              </td>
                            </tr>
                            <tr className="border-b border-neutral-200">
                              <td className="bg-neutral-100 px-3 py-2 font-medium text-neutral-700">
                                {locale === 'en' ? 'Model Code' : 'モデルコード'}
                              </td>
                              <td className="px-3 py-2 text-neutral-800">
                                {selectedItem.title.includes('Voxy 2010') ? 'DBA-ZRR70W' :
                                 selectedItem.title.includes('Voxy 2012') ? 'DBA-ZRR70W' :
                                 selectedItem.title.includes('Noah 2010') ? 'DBA-ZRR75W' :
                                 selectedItem.title.includes('Sienta 2014') ? 'DBA-NCP81G' :
                                 selectedItem.title.includes('Vitz 2014') ? 'DBA-NCP131' :
                                 selectedItem.title.includes('Yaris 2015') ? 'DBA-NCP130' :
                                 'DBA-ZRR70W'}
                                <br />
                                <a href="#" className="text-blue-600 hover:text-blue-800 text-xs underline">
                                  {locale === 'en' ? 'Find parts for this model code' : 'このモデルコードの部品を検索'}
                                </a>
                              </td>
                            </tr>
                            <tr className="border-b border-neutral-200">
                              <td className="bg-neutral-100 px-3 py-2 font-medium text-neutral-700">
                                {locale === 'en' ? 'Version / Class' : 'バージョン/クラス'}
                              </td>
                              <td className="px-3 py-2 text-neutral-800">-</td>
                            </tr>
                            <tr className="border-b border-neutral-200">
                              <td className="bg-neutral-100 px-3 py-2 font-medium text-neutral-700">
                                {locale === 'en' ? 'Engine Code' : 'エンジンコード'}
                              </td>
                              <td className="px-3 py-2 text-neutral-800">-</td>
                            </tr>
                            <tr className="border-b border-neutral-200">
                              <td className="bg-neutral-100 px-3 py-2 font-medium text-neutral-700">
                                {locale === 'en' ? 'Sub Ref No' : 'サブ参照番号'}
                              </td>
                              <td className="px-3 py-2 text-neutral-800">PRC211010507</td>
                            </tr>
                            <tr className="border-b border-neutral-200">
                              <td className="bg-neutral-100 px-3 py-2 font-medium text-neutral-700">
                                {locale === 'en' ? 'Mileage' : '走行距離'}
                              </td>
                              <td className="px-3 py-2 text-neutral-800">
                                {selectedItem.specs.find(spec => spec.includes('km'))?.replace(',', ',') || 'N/A'}
                              </td>
                            </tr>
                            <tr className="border-b border-neutral-200">
                              <td className="bg-neutral-100 px-3 py-2 font-medium text-neutral-700">
                                {locale === 'en' ? 'Engine Size' : 'エンジンサイズ'}
                              </td>
                              <td className="px-3 py-2 text-neutral-800">
                                {selectedItem.title.includes('Voxy') || selectedItem.title.includes('Noah') ? '2,000cc' :
                                 selectedItem.title.includes('Sienta') ? '1,500cc' :
                                 selectedItem.title.includes('Vitz') ? '1,300cc' :
                                 selectedItem.title.includes('Yaris') ? '1,500cc' :
                                 '2,000cc'}
                              </td>
                            </tr>
                            <tr className="border-b border-neutral-200">
                              <td className="bg-neutral-100 px-3 py-2 font-medium text-neutral-700">
                                {locale === 'en' ? 'Registration Year' : '登録年'}
                              </td>
                              <td className="px-3 py-2 text-neutral-800">
                                {selectedItem.title.includes('2010') ? '2010 / 3' :
                                 selectedItem.title.includes('2012') ? '2012 / 5' :
                                 selectedItem.title.includes('2013') ? '2013 / 8' :
                                 selectedItem.title.includes('2014') ? '2014 / 4' :
                                 selectedItem.title.includes('2015') ? '2015 / 7' :
                                 '2010 / 3'}
                              </td>
                            </tr>
                            <tr className="border-b border-neutral-200">
                              <td className="bg-neutral-100 px-3 py-2 font-medium text-neutral-700">
                                {locale === 'en' ? 'Manufacture Year' : '製造年'}
                              </td>
                              <td className="px-3 py-2 text-neutral-800">N/A</td>
                            </tr>
                            <tr className="border-b border-neutral-200">
                              <td className="bg-neutral-100 px-3 py-2 font-medium text-neutral-700">
                                {locale === 'en' ? 'Ext. Color' : '外装色'}
                              </td>
                              <td className="px-3 py-2 text-neutral-800">
                                {getCarColor(selectedItem)}
                              </td>
                            </tr>
                            <tr className="border-b border-neutral-200">
                              <td className="bg-neutral-100 px-3 py-2 font-medium text-neutral-700">
                                {locale === 'en' ? 'Wheel Drive' : '駆動方式'}
                              </td>
                              <td className="px-3 py-2 text-neutral-800">
                                {locale === 'en' ? '4wheel drive' : '4輪駆動'}
                              </td>
                            </tr>
                            <tr className="border-b border-neutral-200">
                              <td className="bg-neutral-100 px-3 py-2 font-medium text-neutral-700">
                                {locale === 'en' ? 'Transmission' : 'トランスミッション'}
                              </td>
                              <td className="px-3 py-2 text-neutral-800">
                                {selectedItem.specs.find(spec => spec.includes('Automatic') || spec.includes('CVT') || spec.includes('Manual')) ||
                                 (locale === 'en' ? 'Automatic' : 'オートマチック')}
                              </td>
                            </tr>
                            <tr className="border-b border-neutral-200">
                              <td className="bg-neutral-100 px-3 py-2 font-medium text-neutral-700">
                                {locale === 'en' ? 'Location' : '所在地'}
                              </td>
                              <td className="px-3 py-2 text-neutral-800">YOKOHAMA</td>
                            </tr>
                            <tr className="border-b border-neutral-200">
                              <td className="bg-neutral-100 px-3 py-2 font-medium text-neutral-700">
                                {locale === 'en' ? 'Steering' : 'ステアリング'}
                              </td>
                              <td className="px-3 py-2 text-neutral-800">
                                {locale === 'en' ? 'Right' : '右ハンドル'}
                              </td>
                            </tr>
                            <tr className="border-b border-neutral-200">
                              <td className="bg-neutral-100 px-3 py-2 font-medium text-neutral-700">
                                {locale === 'en' ? 'Fuel' : '燃料'}
                              </td>
                              <td className="px-3 py-2 text-neutral-800">
                                {locale === 'en' ? 'Petrol' : 'ガソリン'}
                              </td>
                            </tr>
                            <tr>
                              <td className="bg-neutral-100 px-3 py-2 font-medium text-neutral-700">
                                {locale === 'en' ? 'Seats' : '座席数'}
                              </td>
                              <td className="px-3 py-2 text-neutral-800">
                                {selectedItem.specs.find(spec => spec.includes('seater'))?.replace('-seater', '') ||
                                 (selectedItem.title.includes('Voxy') || selectedItem.title.includes('Noah') ? '8' :
                                  selectedItem.title.includes('Sienta') ? '7' : '5')}
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>
                  )}

                {/* Status and Action Section */}
                <div className="grid grid-cols-1 gap-4 sm:gap-6 pt-4 border-t border-neutral-200 animate-fadeInUp">
                  <div className="animate-slideInLeft">
                    <h4 className="text-sm sm:text-base font-semibold text-neutral-800 mb-2 transition-colors duration-300 hover:text-primary-600">
                      {locale === 'en' ? 'Status' : 'ステータス'}
                    </h4>
                    <span className={`px-3 py-1 rounded-full text-xs sm:text-sm font-semibold transition-all duration-300 hover:scale-105 ${
                      selectedItem.status === 'Available' ? 'bg-green-100 text-green-800 animate-pulse' :
                      selectedItem.status === 'Reserved' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {selectedItem.status}
                    </span>
                  </div>

                  <div className="animate-slideInRight">
                    <button
                      className={`w-full px-4 py-3 sm:py-4 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-lg hover:-translate-y-1 text-sm sm:text-base ${
                        selectedItem.status === 'Available'
                          ? 'bg-primary-600 text-white hover:bg-primary-700 animate-bounceIn'
                          : 'bg-neutral-300 text-neutral-500 cursor-not-allowed'
                      }`}
                      disabled={selectedItem.status !== 'Available'}
                      onClick={() => {
                        closeModals();
                        handleInquireClick(selectedItem);
                      }}
                    >
                      {locale === 'en' ? 'Inquire About This Item' : 'この商品について問い合わせる'}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Inquire Modal */}
      {showInquireModal && selectedItem && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 animate-fadeIn">
          <div className="bg-white rounded-xl max-w-md w-full animate-bounceIn">
            <div className="p-6">
              <div className="flex justify-between items-start mb-4">
                <h2 className="text-2xl font-bold text-neutral-800">
                  {locale === 'en' ? 'Inquire About Item' : '商品についてお問い合わせ'}
                </h2>
                <button 
                  onClick={closeModals}
                  className="text-neutral-500 hover:text-neutral-700 text-2xl"
                >
                  ×
                </button>
              </div>
              
              <div className="mb-4 p-4 bg-neutral-50 rounded-lg">
                <h3 className="font-semibold text-neutral-800 mb-1">
                  {selectedItem.title}
                </h3>
                <p className="text-primary-600 font-bold">
                  {selectedItem.price}
                </p>
              </div>
              
              <form className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-2">
                    {locale === 'en' ? 'Your Name' : 'お名前'}
                  </label>
                  <input 
                    type="text" 
                    className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    required
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-2">
                    {locale === 'en' ? 'Email' : 'メールアドレス'}
                  </label>
                  <input 
                    type="email" 
                    className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    required
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-2">
                    {locale === 'en' ? 'Phone Number' : '電話番号'}
                  </label>
                  <input 
                    type="tel" 
                    className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-2">
                    {locale === 'en' ? 'Message' : 'メッセージ'}
                  </label>
                  <textarea 
                    rows={4}
                    className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    placeholder={locale === 'en' ? 'Please let us know your questions or requirements...' : 'ご質問やご要望をお聞かせください...'}
                  />
                </div>
                
                <div className="flex gap-3 pt-4">
                  <button 
                    type="button"
                    onClick={closeModals}
                    className="flex-1 px-4 py-2 border border-neutral-300 text-neutral-700 rounded-lg hover:border-neutral-400 transition-colors duration-200"
                  >
                    {locale === 'en' ? 'Cancel' : 'キャンセル'}
                  </button>
                  <button 
                    type="submit"
                    className="flex-1 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-200"
                  >
                    {locale === 'en' ? 'Send Inquiry' : '問い合わせを送信'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
