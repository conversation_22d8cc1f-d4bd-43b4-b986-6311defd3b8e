import { NextRequest, NextResponse } from 'next/server';
import { getOrderById, addShippingUpdate, updateOrder } from '@/lib/orderStorage';
import { sendDeliveryUpdateFollowUpResend } from '@/lib/followupScheduler';

interface TimelineItem {
  status: string;
  label: string;
  date?: string;
  location?: string;
  completed: boolean;
}
import { getShippingTimeline } from '@/lib/shippingCalculator';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const orderId = searchParams.get('orderId');
    const trackingNumber = searchParams.get('trackingNumber');

    if (!orderId && !trackingNumber) {
      return NextResponse.json(
        { success: false, message: 'Order ID or tracking number is required' },
        { status: 400 }
      );
    }

    let order;
    if (orderId) {
      order = await getOrderById(orderId);
    } else {
      // In a real application, you would search by tracking number
      // For now, we'll return an error
      return NextResponse.json(
        { success: false, message: 'Search by tracking number not implemented' },
        { status: 400 }
      );
    }

    if (!order) {
      return NextResponse.json(
        { success: false, message: 'Order not found' },
        { status: 404 }
      );
    }

    // Get shipping timeline
    const timeline = getShippingTimeline(order.shipping.method);

    // Calculate progress percentage
    const totalStages = timeline.length;
    const currentStageIndex = getCurrentStageIndex(order.shipping.status, timeline);
    const progressPercentage = Math.round((currentStageIndex / (totalStages - 1)) * 100);

    return NextResponse.json({
      success: true,
      data: {
        order: {
          id: order.id,
          orderNumber: order.orderNumber,
          status: order.status,
          createdAt: order.createdAt,
        },
        vehicle: {
          title: order.vehicle.title,
          image: order.vehicle.images[0],
        },
        shipping: {
          status: order.shipping.status,
          trackingNumber: order.shipping.trackingNumber,
          estimatedDelivery: order.shipping.estimatedDelivery,
          method: order.shipping.method.name,
          address: order.shipping.address,
          updates: order.shipping.updates.sort((a, b) => 
            new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
          ),
        },
        timeline: timeline.map((stage, index) => ({
          ...stage,
          completed: index <= currentStageIndex,
          current: index === currentStageIndex,
        })),
        progress: {
          percentage: progressPercentage,
          currentStage: timeline[currentStageIndex]?.stage || 'Unknown',
          nextStage: timeline[currentStageIndex + 1]?.stage || 'Delivered',
        },
      },
    });

  } catch (error) {
    console.error('Error getting tracking information:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to get tracking information' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { orderId, update, adminKey } = await request.json();

    // Admin authentication
    const validAdminKey = process.env.ADMIN_PASSWORD || 'admin123';
    if (adminKey !== validAdminKey) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (!orderId || !update) {
      return NextResponse.json(
        { success: false, message: 'Order ID and update information are required' },
        { status: 400 }
      );
    }

    // Validate update data
    if (!update.status || !update.location || !update.description) {
      return NextResponse.json(
        { success: false, message: 'Status, location, and description are required' },
        { status: 400 }
      );
    }

    // Add shipping update
    const success = await addShippingUpdate(orderId, {
      timestamp: update.timestamp || new Date().toISOString(),
      status: update.status,
      location: update.location,
      description: update.description,
      estimatedDelivery: update.estimatedDelivery,
    });

    if (!success) {
      return NextResponse.json(
        { success: false, message: 'Order not found' },
        { status: 404 }
      );
    }

    // Send delivery update follow-up email to customer
    try {
      const order = await getOrderById(orderId);
      if (order && order.customer.email) {
        await sendDeliveryUpdateFollowUpResend({
          customerName: order.customer.name,
          customerEmail: order.customer.email,
          orderId: orderId,
          status: update.status,
          location: update.location,
          estimatedArrival: update.estimatedDelivery
        });
        console.log('Delivery update follow-up email sent successfully');
      }
    } catch (emailError) {
      console.error('Failed to send delivery update follow-up email:', emailError);
      // Don't fail the shipping update if email fails
    }

    // Update shipping status if provided
    if (update.shippingStatus) {
      await updateOrder(orderId, {
        shipping: {
          ...await getOrderById(orderId).then(o => o?.shipping),
          status: update.shippingStatus,
          trackingNumber: update.trackingNumber || undefined,
        },
      });
    }

    return NextResponse.json({
      success: true,
      message: 'Tracking update added successfully',
    });

  } catch (error) {
    console.error('Error adding tracking update:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to add tracking update' },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const { orderId, trackingNumber, shippingStatus, adminKey } = await request.json();

    // Admin authentication
    const validAdminKey = process.env.ADMIN_PASSWORD || 'admin123';
    if (adminKey !== validAdminKey) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (!orderId) {
      return NextResponse.json(
        { success: false, message: 'Order ID is required' },
        { status: 400 }
      );
    }

    const order = await getOrderById(orderId);
    if (!order) {
      return NextResponse.json(
        { success: false, message: 'Order not found' },
        { status: 404 }
      );
    }

    // Update shipping information
    const updatedShipping = {
      ...order.shipping,
      ...(trackingNumber && { trackingNumber }),
      ...(shippingStatus && { status: shippingStatus }),
    };

    const success = await updateOrder(orderId, { shipping: updatedShipping });

    if (!success) {
      return NextResponse.json(
        { success: false, message: 'Failed to update order' },
        { status: 500 }
      );
    }

    // Add automatic update if status changed
    if (shippingStatus && shippingStatus !== order.shipping.status) {
      await addShippingUpdate(orderId, {
        timestamp: new Date().toISOString(),
        status: getStatusDisplayName(shippingStatus),
        location: 'System Update',
        description: `Shipping status updated to ${getStatusDisplayName(shippingStatus)}`,
      });
    }

    return NextResponse.json({
      success: true,
      message: 'Shipping information updated successfully',
    });

  } catch (error) {
    console.error('Error updating shipping information:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to update shipping information' },
      { status: 500 }
    );
  }
}

function getCurrentStageIndex(shippingStatus: string, timeline: TimelineItem[]): number {
  const statusMap: Record<string, number> = {
    'pending': 0,
    'preparing': 1,
    'shipped': 2,
    'in_transit': 3,
    'delivered': timeline.length - 1,
  };

  return statusMap[shippingStatus] || 0;
}

function getStatusDisplayName(status: string): string {
  const statusNames: Record<string, string> = {
    'pending': 'Order Pending',
    'preparing': 'Preparing for Shipment',
    'shipped': 'Shipped',
    'in_transit': 'In Transit',
    'delivered': 'Delivered',
    'returned': 'Returned',
  };

  return statusNames[status] || status;
}
