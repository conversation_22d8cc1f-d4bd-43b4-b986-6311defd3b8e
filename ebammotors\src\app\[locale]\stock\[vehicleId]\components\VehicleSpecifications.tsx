'use client';

import { Car, Fuel, Settings, Wrench, Shield, Users, Palette } from 'lucide-react';

interface StockItem {
  id: number;
  category: string;
  title: string;
  price: string;
  location: string;
  status: string;
  image: string;
  images: string[];
  specs: string[];
  carId: string;
  year?: number;
  mileage?: number;
  fuelType?: string;
  transmission?: string;
  bodyCondition?: string;
}

interface VehicleSpecificationsProps {
  vehicle: StockItem;
  locale: string;
}

export default function VehicleSpecifications({ vehicle, locale }: VehicleSpecificationsProps) {
  // Generate additional specifications based on vehicle data
  const generateSpecs = () => {
    const baseSpecs = {
      // Basic Information
      make: vehicle.title.split(' ')[0] || 'Toyota',
      model: vehicle.title.split(' ').slice(1, -1).join(' ') || 'Unknown',
      year: vehicle.year || 2010,
      mileage: vehicle.mileage || 50000,
      
      // Engine & Performance
      engine: vehicle.fuelType === 'Hybrid' ? '1.8L Hybrid' : vehicle.fuelType === 'Diesel' ? '2.0L Diesel' : '1.5L Gasoline',
      fuelType: vehicle.fuelType || 'Gasoline',
      transmission: vehicle.transmission || 'Automatic',
      drivetrain: 'Front-Wheel Drive',
      
      // Dimensions & Capacity
      seating: vehicle.specs.find(spec => spec.includes('seater'))?.replace('-seater', ' seats') || '5 seats',
      doors: '4 doors',
      bodyType: 'Sedan',
      
      // Condition & Features
      bodyCondition: vehicle.bodyCondition || 'Good',
      interiorCondition: vehicle.bodyCondition || 'Good',
      airConditioning: 'Yes',
      powerSteering: 'Yes',
      
      // Documentation
      registrationStatus: 'Valid',
      inspectionStatus: 'Passed',
      exportReady: 'Yes',
      
      // Additional Features
      color: ['White', 'Silver', 'Black', 'Blue', 'Red'][vehicle.id % 5],
      fuelTankCapacity: '50L',
      groundClearance: '150mm',
    };

    return baseSpecs;
  };

  const specs = generateSpecs();

  const specSections = [
    {
      title: locale === 'en' ? 'Basic Information' : '基本情報',
      icon: Car,
      items: [
        { label: locale === 'en' ? 'Make' : 'メーカー', value: specs.make },
        { label: locale === 'en' ? 'Model' : 'モデル', value: specs.model },
        { label: locale === 'en' ? 'Year' : '年式', value: specs.year.toString() },
        { label: locale === 'en' ? 'Body Type' : 'ボディタイプ', value: specs.bodyType },
        { label: locale === 'en' ? 'Color' : '色', value: specs.color },
        { label: locale === 'en' ? 'Doors' : 'ドア数', value: specs.doors },
      ]
    },
    {
      title: locale === 'en' ? 'Engine & Performance' : 'エンジン・性能',
      icon: Settings,
      items: [
        { label: locale === 'en' ? 'Engine' : 'エンジン', value: specs.engine },
        { label: locale === 'en' ? 'Fuel Type' : '燃料タイプ', value: specs.fuelType },
        { label: locale === 'en' ? 'Transmission' : 'トランスミッション', value: specs.transmission },
        { label: locale === 'en' ? 'Drivetrain' : '駆動方式', value: specs.drivetrain },
        { label: locale === 'en' ? 'Fuel Tank' : '燃料タンク', value: specs.fuelTankCapacity },
      ]
    },
    {
      title: locale === 'en' ? 'Dimensions & Capacity' : '寸法・容量',
      icon: Users,
      items: [
        { label: locale === 'en' ? 'Seating Capacity' : '乗車定員', value: specs.seating },
        { label: locale === 'en' ? 'Mileage' : '走行距離', value: `${Math.floor(specs.mileage / 1000)}k km` },
        { label: locale === 'en' ? 'Ground Clearance' : '最低地上高', value: specs.groundClearance },
      ]
    },
    {
      title: locale === 'en' ? 'Condition & Features' : '状態・装備',
      icon: Shield,
      items: [
        { label: locale === 'en' ? 'Body Condition' : 'ボディ状態', value: specs.bodyCondition },
        { label: locale === 'en' ? 'Interior Condition' : '内装状態', value: specs.interiorCondition },
        { label: locale === 'en' ? 'Air Conditioning' : 'エアコン', value: specs.airConditioning },
        { label: locale === 'en' ? 'Power Steering' : 'パワーステアリング', value: specs.powerSteering },
      ]
    },
    {
      title: locale === 'en' ? 'Documentation & Export' : '書類・輸出',
      icon: Wrench,
      items: [
        { label: locale === 'en' ? 'Registration' : '登録', value: specs.registrationStatus },
        { label: locale === 'en' ? 'Inspection' : '車検', value: specs.inspectionStatus },
        { label: locale === 'en' ? 'Export Ready' : '輸出準備', value: specs.exportReady },
        { label: locale === 'en' ? 'Location' : '所在地', value: vehicle.location },
      ]
    }
  ];

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-white rounded-lg p-6 border">
        <h2 className="text-2xl font-bold text-neutral-800 mb-2">
          {locale === 'en' ? 'Detailed Specifications' : '詳細仕様'}
        </h2>
        <p className="text-neutral-600">
          {locale === 'en' 
            ? 'Complete technical specifications and features for this vehicle.'
            : 'この車両の完全な技術仕様と機能。'
          }
        </p>
      </div>

      {/* Specifications Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {specSections.map((section, sectionIndex) => {
          const Icon = section.icon;
          return (
            <div key={sectionIndex} className="bg-white rounded-lg border overflow-hidden">
              <div className="bg-neutral-50 px-6 py-4 border-b">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-primary-100 rounded-lg">
                    <Icon className="w-5 h-5 text-primary-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-neutral-800">{section.title}</h3>
                </div>
              </div>
              
              <div className="p-6">
                <div className="space-y-4">
                  {section.items.map((item, itemIndex) => (
                    <div key={itemIndex} className="flex justify-between items-center py-2 border-b border-neutral-100 last:border-b-0">
                      <span className="text-neutral-600 font-medium">{item.label}</span>
                      <span className="text-neutral-800 font-semibold">{item.value}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Additional Features */}
      <div className="bg-white rounded-lg p-6 border">
        <h3 className="text-lg font-semibold text-neutral-800 mb-4 flex items-center space-x-2">
          <Palette className="w-5 h-5 text-primary-600" />
          <span>{locale === 'en' ? 'Standard Features' : '標準装備'}</span>
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[
            locale === 'en' ? 'Power Windows' : 'パワーウィンドウ',
            locale === 'en' ? 'Central Locking' : 'セントラルロック',
            locale === 'en' ? 'ABS Brakes' : 'ABSブレーキ',
            locale === 'en' ? 'Airbags' : 'エアバッグ',
            locale === 'en' ? 'Radio/CD Player' : 'ラジオ/CDプレーヤー',
            locale === 'en' ? 'Electric Mirrors' : '電動ミラー',
          ].map((feature, index) => (
            <div key={index} className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-neutral-700">{feature}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Fuel Economy */}
      <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-6 border">
        <h3 className="text-lg font-semibold text-neutral-800 mb-4 flex items-center space-x-2">
          <Fuel className="w-5 h-5 text-green-600" />
          <span>{locale === 'en' ? 'Fuel Economy' : '燃費'}</span>
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {specs.fuelType === 'Hybrid' ? '25' : specs.fuelType === 'Diesel' ? '18' : '15'}
            </div>
            <div className="text-sm text-neutral-600">
              {locale === 'en' ? 'km/L City' : 'km/L 市街地'}
            </div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {specs.fuelType === 'Hybrid' ? '28' : specs.fuelType === 'Diesel' ? '22' : '18'}
            </div>
            <div className="text-sm text-neutral-600">
              {locale === 'en' ? 'km/L Highway' : 'km/L 高速道路'}
            </div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {specs.fuelType === 'Hybrid' ? '26' : specs.fuelType === 'Diesel' ? '20' : '16'}
            </div>
            <div className="text-sm text-neutral-600">
              {locale === 'en' ? 'km/L Combined' : 'km/L 総合'}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
