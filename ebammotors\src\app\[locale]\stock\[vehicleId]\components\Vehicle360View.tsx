'use client';

import { useState, useRef, useEffect } from 'react';
import Image from 'next/image';
import { RotateCcw, Play, Pause, RotateCw, Maximize, Info, MousePointer } from 'lucide-react';

interface StockItem {
  id: number;
  category: string;
  title: string;
  price: string;
  location: string;
  status: string;
  image: string;
  images: string[];
  specs: string[];
  carId: string;
  year?: number;
  mileage?: number;
  fuelType?: string;
  transmission?: string;
  bodyCondition?: string;
}

interface Vehicle360ViewProps {
  vehicle: StockItem;
  locale: string;
}

export default function Vehicle360View({ vehicle, locale }: Vehicle360ViewProps) {
  const [currentFrame, setCurrentFrame] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showInstructions, setShowInstructions] = useState(true);
  
  const containerRef = useRef<HTMLDivElement>(null);
  const startXRef = useRef<number>(0);
  const startFrameRef = useRef<number>(0);
  
  // Use vehicle images for 360 view simulation
  const frames = vehicle.images || [vehicle.image];
  const totalFrames = frames.length;

  // Auto-rotation effect
  useEffect(() => {
    if (isPlaying && !isDragging) {
      const interval = setInterval(() => {
        setCurrentFrame((prev) => (prev + 1) % totalFrames);
      }, 150);
      return () => clearInterval(interval);
    }
  }, [isPlaying, isDragging, totalFrames]);

  // Hide instructions after 5 seconds
  useEffect(() => {
    const timer = setTimeout(() => {
      setShowInstructions(false);
    }, 5000);
    return () => clearTimeout(timer);
  }, []);

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    setIsPlaying(false);
    startXRef.current = e.clientX;
    startFrameRef.current = currentFrame;
    setShowInstructions(false);
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging) return;
    
    const deltaX = e.clientX - startXRef.current;
    const sensitivity = 3; // Adjust sensitivity
    const frameChange = Math.floor(deltaX / sensitivity);
    const newFrame = (startFrameRef.current + frameChange + totalFrames) % totalFrames;
    setCurrentFrame(newFrame);
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleTouchStart = (e: React.TouchEvent) => {
    setIsDragging(true);
    setIsPlaying(false);
    startXRef.current = e.touches[0].clientX;
    startFrameRef.current = currentFrame;
    setShowInstructions(false);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!isDragging) return;
    
    const deltaX = e.touches[0].clientX - startXRef.current;
    const sensitivity = 3;
    const frameChange = Math.floor(deltaX / sensitivity);
    const newFrame = (startFrameRef.current + frameChange + totalFrames) % totalFrames;
    setCurrentFrame(newFrame);
  };

  const handleTouchEnd = () => {
    setIsDragging(false);
  };

  const togglePlayPause = () => {
    setIsPlaying(!isPlaying);
    setShowInstructions(false);
  };

  const rotateLeft = () => {
    setCurrentFrame((prev) => (prev - 1 + totalFrames) % totalFrames);
    setIsPlaying(false);
    setShowInstructions(false);
  };

  const rotateRight = () => {
    setCurrentFrame((prev) => (prev + 1) % totalFrames);
    setIsPlaying(false);
    setShowInstructions(false);
  };

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      containerRef.current?.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  // Listen for fullscreen changes
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, []);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg p-6 border">
        <h2 className="text-2xl font-bold text-neutral-800 mb-2">
          {locale === 'en' ? '360° Vehicle View' : '360°車両ビュー'}
        </h2>
        <p className="text-neutral-600">
          {locale === 'en' 
            ? 'Drag to rotate, click play for auto-rotation, or use the controls below.'
            : 'ドラッグして回転、再生をクリックして自動回転、または下のコントロールを使用してください。'
          }
        </p>
      </div>

      {/* 360 View Container */}
      <div 
        ref={containerRef}
        className={`relative bg-white rounded-lg border overflow-hidden ${
          isFullscreen ? 'fixed inset-0 z-50 rounded-none' : ''
        }`}
      >
        {/* Instructions Overlay */}
        {showInstructions && (
          <div className="absolute top-4 left-4 right-4 bg-black/75 text-white p-4 rounded-lg z-20 animate-fadeIn">
            <div className="flex items-center space-x-2 mb-2">
              <MousePointer className="w-4 h-4" />
              <span className="font-semibold">
                {locale === 'en' ? 'How to use 360° View' : '360°ビューの使い方'}
              </span>
            </div>
            <p className="text-sm">
              {locale === 'en' 
                ? 'Drag left or right to rotate the vehicle. Use controls below for auto-rotation.'
                : '左右にドラッグして車両を回転させます。自動回転には下のコントロールを使用してください。'
              }
            </p>
          </div>
        )}

        {/* Main 360 View */}
        <div 
          className={`relative ${isFullscreen ? 'h-screen' : 'aspect-[16/10]'} cursor-grab active:cursor-grabbing select-none`}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
        >
          <Image
            src={frames[currentFrame]}
            alt={`${vehicle.title} - 360° View Frame ${currentFrame + 1}`}
            fill
            className="object-contain bg-neutral-100"
            priority
            draggable={false}
          />

          {/* Frame Counter */}
          <div className="absolute top-4 right-4 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
            {currentFrame + 1} / {totalFrames}
          </div>

          {/* Loading Indicator */}
          {isDragging && (
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
              {locale === 'en' ? 'Rotating...' : '回転中...'}
            </div>
          )}
        </div>

        {/* Controls */}
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black/75 backdrop-blur-sm rounded-full p-2 flex items-center space-x-2">
          <button
            onClick={rotateLeft}
            className="p-2 text-white hover:text-primary-400 transition-colors"
            title={locale === 'en' ? 'Rotate Left' : '左回転'}
          >
            <RotateCcw className="w-5 h-5" />
          </button>
          
          <button
            onClick={togglePlayPause}
            className="p-2 text-white hover:text-primary-400 transition-colors"
            title={isPlaying ? (locale === 'en' ? 'Pause' : '一時停止') : (locale === 'en' ? 'Play' : '再生')}
          >
            {isPlaying ? <Pause className="w-5 h-5" /> : <Play className="w-5 h-5" />}
          </button>
          
          <button
            onClick={rotateRight}
            className="p-2 text-white hover:text-primary-400 transition-colors"
            title={locale === 'en' ? 'Rotate Right' : '右回転'}
          >
            <RotateCw className="w-5 h-5" />
          </button>
          
          <div className="w-px h-6 bg-white/30 mx-1"></div>
          
          <button
            onClick={() => setShowInstructions(!showInstructions)}
            className="p-2 text-white hover:text-primary-400 transition-colors"
            title={locale === 'en' ? 'Show Instructions' : '使い方を表示'}
          >
            <Info className="w-5 h-5" />
          </button>
          
          <button
            onClick={toggleFullscreen}
            className="p-2 text-white hover:text-primary-400 transition-colors"
            title={locale === 'en' ? 'Fullscreen' : 'フルスクリーン'}
          >
            <Maximize className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Features Info */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg p-6 border">
          <h3 className="text-lg font-semibold text-neutral-800 mb-4">
            {locale === 'en' ? 'Exterior Highlights' : '外装のハイライト'}
          </h3>
          <div className="space-y-3">
            {[
              locale === 'en' ? 'Body condition: ' + (vehicle.bodyCondition || 'Good') : 'ボディ状態: ' + (vehicle.bodyCondition || '良好'),
              locale === 'en' ? 'Paint quality: Excellent' : '塗装品質: 優秀',
              locale === 'en' ? 'Rust inspection: Passed' : '錆検査: 合格',
              locale === 'en' ? 'Panel alignment: Good' : 'パネル整列: 良好',
            ].map((item, index) => (
              <div key={index} className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-neutral-700">{item}</span>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 border">
          <h3 className="text-lg font-semibold text-neutral-800 mb-4">
            {locale === 'en' ? 'View Options' : '表示オプション'}
          </h3>
          <div className="space-y-3">
            <button className="w-full text-left p-3 border rounded-lg hover:bg-neutral-50 transition-colors">
              <div className="font-medium text-neutral-800">
                {locale === 'en' ? 'Interior 360° View' : '内装360°ビュー'}
              </div>
              <div className="text-sm text-neutral-600">
                {locale === 'en' ? 'Coming soon' : '近日公開'}
              </div>
            </button>
            <button className="w-full text-left p-3 border rounded-lg hover:bg-neutral-50 transition-colors">
              <div className="font-medium text-neutral-800">
                {locale === 'en' ? 'Engine Bay View' : 'エンジンルームビュー'}
              </div>
              <div className="text-sm text-neutral-600">
                {locale === 'en' ? 'Coming soon' : '近日公開'}
              </div>
            </button>
            <button className="w-full text-left p-3 border rounded-lg hover:bg-neutral-50 transition-colors">
              <div className="font-medium text-neutral-800">
                {locale === 'en' ? 'Undercarriage View' : '車体下部ビュー'}
              </div>
              <div className="text-sm text-neutral-600">
                {locale === 'en' ? 'Coming soon' : '近日公開'}
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
