'use client';

import { usePathname } from 'next/navigation';
import { getMessagesSync } from '@/lib/messages';

interface ChatbotButtonProps {
  className?: string;
  children: React.ReactNode;
}

export default function ChatbotButton({ className, children }: ChatbotButtonProps) {
  const pathname = usePathname();
  const locale = pathname.split('/')[1] || 'en';
  const messages = getMessagesSync(locale);

  const handleChatbotClick = () => {
    if (typeof window !== 'undefined' && (window as any).openChatbot) {
      (window as any).openChatbot();
    } else {
      // Fallback - show message about chatbot location
      alert(messages.chatbot?.lookForChatIcon || 'Look for the chat icon in the bottom-right corner above the WhatsApp button to start chatting!');
    }
  };

  return (
    <button
      onClick={handleChatbotClick}
      className={className}
    >
      {children}
    </button>
  );
}
