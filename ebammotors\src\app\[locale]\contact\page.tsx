import Link from 'next/link';
import Image from 'next/image';
import { getMessages } from '@/lib/messages';
import Navigation from '@/components/Navigation';
import ContactForm from '@/components/ContactForm';

export default async function ContactPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;
  const messages = await getMessages(locale);

  const contactMethods = [
    {
      title: locale === 'en' ? 'Phone (Japan)' : '電話（日本）',
      value: messages.about?.phone || '080-6985-2864',
      icon: '',
      description: locale === 'en' ? 'Call us directly for immediate assistance' : '即座のサポートのため直接お電話ください',
      action: `tel:${(messages.about?.phone || '080-6985-2864').replace(/[^0-9+]/g, '')}`
    },
    {
      title: locale === 'en' ? 'Phone (Ghana)' : '電話（ガーナ）',
      value: messages.about?.ghanaPhone || '+233245375692',
      icon: '',
      description: locale === 'en' ? 'Contact our Ghana office for local support' : 'ガーナオフィスへの現地サポートのお問い合わせ',
      action: `tel:${(messages.about?.ghanaPhone || '+233245375692').replace(/[^0-9+]/g, '')}`
    },
    {
      title: locale === 'en' ? 'Email' : 'メール',
      value: messages.about?.email || '<EMAIL>',
      icon: '',
      description: locale === 'en' ? 'Send us detailed inquiries via email' : '詳細なお問い合わせをメールでお送りください',
      action: `mailto:${messages.about?.email || '<EMAIL>'}`
    },
    {
      title: 'WhatsApp Channel',
      value: locale === 'en' ? 'Join our channel for updates' : 'アップデートのためにチャンネルに参加',
      icon: '',
      description: locale === 'en' ? 'Follow our WhatsApp channel for latest inventory and updates' : '最新の在庫とアップデートのためにWhatsAppチャンネルをフォロー',
      action: 'https://whatsapp.com/channel/0029Vb5sDlFDTkK191T6Ut1G'
    },
    {
      title: locale === 'en' ? 'Japan Office' : '日本オフィス',
      value: messages.about?.location || 'Japan, Saitama, Hanno City, Nagata',
      icon: '',
      description: locale === 'en' ? 'Visit our office in Japan' : '日本のオフィスにお越しください',
      action: '#map'
    },
    {
      title: locale === 'en' ? 'Ghana Office' : 'ガーナオフィス',
      value: messages.about?.ghanaLocation || 'Kumasi, Ghana',
      icon: '',
      description: locale === 'en' ? 'Our local presence in Ghana and Africa' : 'ガーナ・アフリカでの現地拠点',
      action: '#map'
    }
  ];

  const officeHours = [
    {
      day: locale === 'en' ? 'Monday - Friday' : '月曜日 - 金曜日',
      hours: '9:00 AM - 6:00 PM JST'
    },
    {
      day: locale === 'en' ? 'Saturday' : '土曜日',
      hours: '10:00 AM - 4:00 PM JST'
    },
    {
      day: locale === 'en' ? 'Sunday' : '日曜日',
      hours: locale === 'en' ? 'Closed' : '休業'
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Navigation */}
      <Navigation />

      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 text-white py-20">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl lg:text-5xl font-heading font-bold mb-6">
            {messages.navigation.contact}
          </h1>
          <p className="text-xl lg:text-2xl text-primary-100 max-w-4xl mx-auto">
            {locale === 'en'
              ? 'Get in touch with us for all your trading needs. We\'re here to help you every step of the way.'
              : 'すべての貿易ニーズについてお気軽にお問い合わせください。あらゆる段階でサポートいたします。'
            }
          </p>
        </div>
      </section>

      {/* Contact Methods */}
      <section className="py-20 bg-neutral-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-heading font-bold text-neutral-800 mb-6">
              {locale === 'en' ? 'How to Reach Us' : 'お問い合わせ方法'}
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              {locale === 'en'
                ? 'Choose the most convenient way to contact us'
                : '最も便利な方法でお問い合わせください'
              }
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {contactMethods.map((method, index) => (
              <div key={index} className="bg-white rounded-xl shadow-lg p-8 text-center hover:shadow-xl transition-shadow duration-300">
                <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <span className="text-3xl">{method.icon}</span>
                </div>
                <h3 className="text-xl font-heading font-bold text-neutral-800 mb-3">
                  {method.title}
                </h3>
                <p className="text-primary-600 font-semibold mb-3">
                  {method.value}
                </p>
                <p className="text-neutral-600 text-sm mb-6">
                  {method.description}
                </p>
                <a
                  href={method.action}
                  className="inline-block bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-lg font-semibold transition-colors duration-200"
                >
                  {locale === 'en' ? 'Contact' : '連絡する'}
                </a>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Form */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <ContactForm locale={locale} />
        </div>
      </section>

      {/* Office Hours & Location */}
      <section className="py-20 bg-neutral-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Office Hours */}
            <div>
              <h2 className="text-3xl font-heading font-bold text-neutral-800 mb-8">
                {messages.contact.officeHours}
              </h2>
              <div className="bg-white rounded-xl shadow-lg p-8">
                <div className="space-y-4">
                  {officeHours.map((schedule, index) => (
                    <div key={index} className="flex justify-between items-center py-3 border-b border-neutral-200 last:border-b-0">
                      <span className="font-semibold text-neutral-800">{schedule.day}</span>
                      <span className="text-neutral-600">{schedule.hours}</span>
                    </div>
                  ))}
                </div>
                <div className="mt-6 p-4 bg-primary-50 rounded-lg">
                  <p className="text-primary-800 font-semibold mb-2">
                    {messages.contact.emergencyContact}
                  </p>
                  <p className="text-primary-700 text-sm">
                    {locale === 'en'
                      ? 'For urgent matters outside office hours, please use WhatsApp or email.'
                      : '営業時間外の緊急事項については、WhatsAppまたはメールをご利用ください。'
                    }
                  </p>
                </div>
              </div>
            </div>

            {/* Location Map Placeholder */}
            <div>
              <h2 className="text-3xl font-heading font-bold text-neutral-800 mb-8">
                {messages.contact.map}
              </h2>
              <div className="bg-white rounded-xl shadow-lg p-8">
                <div className="bg-neutral-200 rounded-lg h-64 flex items-center justify-center mb-6">
                  <div className="text-center text-neutral-500">
                    <span className="text-4xl mb-2 block">🗺️</span>
                    <p>{messages.contact.interactiveMapComingSoon}</p>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex items-start">
                    <span className="text-primary-600 mr-3 mt-1">📍</span>
                    <div>
                      <p className="font-semibold text-neutral-800">
                        {messages.contact.address}
                      </p>
                      <p className="text-neutral-600">{messages.about.location}</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <span className="text-primary-600 mr-3 mt-1">🚗</span>
                    <div>
                      <p className="font-semibold text-neutral-800">
                        {messages.contact.access}
                      </p>
                      <p className="text-neutral-600">
                        {messages.contact.accessDescription}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* WhatsApp Channel QR Code Section */}
      <section className="py-20 bg-gradient-to-br from-green-50 to-green-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-heading font-bold text-neutral-800 mb-4">
              {locale === 'en' ? 'Join Our WhatsApp Channel' : 'WhatsAppチャンネルに参加'}
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              {locale === 'en'
                ? 'Stay updated with our latest stock, special offers, and important announcements by joining our WhatsApp channel.'
                : '最新の在庫、特別オファー、重要なお知らせを受け取るために、WhatsAppチャンネルにご参加ください。'
              }
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* QR Code - Hidden on mobile phones, visible on tablets and larger */}
            <div className="hidden sm:block text-center">
              <div className="bg-white rounded-2xl shadow-lg p-8 inline-block">
                <Image
                  src="/whatsapp qrcode.jpg"
                  alt="WhatsApp Channel QR Code"
                  width={300}
                  height={300}
                  className="rounded-lg w-auto h-auto"
                />
                <p className="text-sm text-neutral-600 mt-4">
                  {locale === 'en' ? 'Scan with your phone camera' : 'スマートフォンのカメラでスキャン'}
                </p>
              </div>
            </div>

            {/* Channel Info */}
            <div className="space-y-6">
              <div className="bg-white rounded-xl shadow-lg p-8">
                <h3 className="text-2xl font-heading font-bold text-neutral-800 mb-4">
                  {locale === 'en' ? 'What You\'ll Get:' : '受け取れる内容：'}
                </h3>
                <ul className="space-y-3">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-3 mt-2 flex-shrink-0"></div>
                    <span className="text-neutral-700">
                      {locale === 'en' ? 'Latest car arrivals and inventory updates' : '最新の車両入荷と在庫アップデート'}
                    </span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-3 mt-2 flex-shrink-0"></div>
                    <span className="text-neutral-700">
                      {locale === 'en' ? 'Special offers and price alerts' : '特別オファーと価格アラート'}
                    </span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-3 mt-2 flex-shrink-0"></div>
                    <span className="text-neutral-700">
                      {locale === 'en' ? 'Important announcements and updates' : '重要なお知らせとアップデート'}
                    </span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-3 mt-2 flex-shrink-0"></div>
                    <span className="text-neutral-700">
                      {locale === 'en' ? 'Exclusive deals for channel members' : 'チャンネルメンバー限定のお得な情報'}
                    </span>
                  </li>
                </ul>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <a
                  href="https://whatsapp.com/channel/0029Vb5sDlFDTkK191T6Ut1G"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex-1 bg-green-500 hover:bg-green-600 text-white px-6 py-4 rounded-lg font-semibold transition-all duration-200 flex items-center justify-center hover:scale-105 hover:shadow-lg"
                >
                  <span className="mr-2">💬</span>
                  {locale === 'en' ? 'Join Channel Now' : '今すぐチャンネルに参加'}
                </a>
                <a
                  href="https://whatsapp.com/channel/0029Vb5sDlFDTkK191T6Ut1G"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex-1 bg-neutral-100 hover:bg-neutral-200 text-neutral-700 px-6 py-4 rounded-lg font-semibold transition-all duration-200 flex items-center justify-center hover:scale-105"
                >
                  <span className="mr-2">🔗</span>
                  {locale === 'en' ? 'Open Link' : 'リンクを開く'}
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Quick Actions */}
      <section className="py-20 bg-primary-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl lg:text-4xl font-heading font-bold mb-6">
            {locale === 'en' ? 'Quick Actions' : 'クイックアクション'}
          </h2>
          <p className="text-xl text-primary-100 mb-8 max-w-3xl mx-auto">
            {locale === 'en'
              ? 'Need immediate assistance? Use these quick links to get started right away.'
              : '即座のサポートが必要ですか？これらのクイックリンクを使用してすぐに始めましょう。'
            }
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <a
              href="https://whatsapp.com/channel/0029Vb5sDlFDTkK191T6Ut1G"
              target="_blank"
              rel="noopener noreferrer"
              className="bg-green-500 hover:bg-green-600 text-white px-6 py-4 rounded-lg font-semibold transition-colors duration-200 flex items-center justify-center"
            >
              {locale === 'en' ? 'Join WhatsApp Channel' : 'WhatsAppチャンネルに参加'}
            </a>
            <a
              href={`tel:${(messages.about?.phone || '080-6985-2864').replace(/[^0-9+]/g, '')}`}
              className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-4 rounded-lg font-semibold transition-colors duration-200 flex items-center justify-center"
            >
              {locale === 'en' ? 'Call Now' : '今すぐ電話'}
            </a>
            <a
              href={`mailto:${messages.about?.email || '<EMAIL>'}`}
              className="bg-secondary-500 hover:bg-secondary-600 text-neutral-900 px-6 py-4 rounded-lg font-semibold transition-colors duration-200 flex items-center justify-center"
            >
              <span className="mr-2">✉️</span>
              {locale === 'en' ? 'Send Email' : 'メール送信'}
            </a>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-neutral-800 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-xl">EM</span>
                </div>
                <span className="font-heading font-bold text-xl">EBAM MOTORS</span>
              </div>
              <p className="text-neutral-300">
                {messages.about?.mission || 'Promote sustainable trade by giving used goods a second life where they are needed most.'}
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-lg mb-4">{messages.contact.contactInfo}</h3>
              <div className="space-y-2 text-neutral-300">
                <div>
                  <p className="font-medium text-neutral-200">{messages.contact.japanOffice}：</p>
                  <p>{messages.about?.phone || '080-6985-2864'}</p>
                  <p>{messages.about?.location || 'Japan, Saitama, Hanno City, Nagata'}</p>
                </div>
                <div className="pt-2">
                  <p className="font-medium text-neutral-200">{messages.contact.ghanaOffice}：</p>
                  <p>{messages.about?.ghanaPhone || '+233245375692'}</p>
                  <p>{messages.about?.ghanaLocation || 'Kumasi, Ghana'}</p>
                </div>
                <div className="pt-2">
                  <p>{messages.about?.email || '<EMAIL>'}</p>
                  <a
                    href="https://whatsapp.com/channel/0029Vb5sDlFDTkK191T6Ut1G"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="block text-green-400 hover:text-green-300 transition-colors"
                  >
                    {locale === 'en' ? 'WhatsApp Channel' : 'WhatsAppチャンネル'}
                  </a>
                </div>
              </div>
            </div>
            <div>
              <h3 className="font-semibold text-lg mb-4">{messages.contact.quickLinks}</h3>
              <div className="space-y-2">
                <Link href={`/${locale}/services`} className="block text-neutral-300 hover:text-white transition-colors">
                  {messages.navigation.services}
                </Link>
                <Link href={`/${locale}/stock`} className="block text-neutral-300 hover:text-white transition-colors">
                  {messages.navigation.inventory}
                </Link>
                <Link href={`/${locale}/how-it-works`} className="block text-neutral-300 hover:text-white transition-colors">
                  {messages.navigation.howItWorks}
                </Link>
              </div>
            </div>
          </div>
          <div className="border-t border-neutral-700 mt-8 pt-8 text-center text-neutral-400">
            <p>&copy; 2025 EBAM MOTORS. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
