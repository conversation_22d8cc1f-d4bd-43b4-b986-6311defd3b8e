// Database connection and utility functions
import { sql } from '@vercel/postgres';
import { Car, CreateCarInput, UpdateCarInput, CarSearchFilters, CarSearchResult, CSVImport } from '@/types/car';

// Environment check for database availability
const isDatabaseAvailable = () => {
  return !!(process.env.POSTGRES_URL || process.env.DATABASE_URL);
};

// Generate URL-friendly slug from car details
export function generateCarSlug(make: string, model: string, year: number, carId: string): string {
  const baseSlug = `${make}-${model}-${year}`.toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/^-+|-+$/g, '');
  
  // Add unique identifier to prevent duplicates
  const uniquePart = carId.split('_').pop() || Math.random().toString(36).substr(2, 5);
  return `${baseSlug}-${uniquePart}`;
}

// Convert database row to Car object
function mapRowToCar(row: any): Car {
  return {
    id: row.id,
    car_id: row.car_id,
    make: row.make,
    model: row.model,
    year: row.year,
    title: row.title,
    price: row.price,
    original_price: row.original_price,
    currency: row.currency || 'JPY',
    mileage: row.mileage,
    fuel_type: row.fuel_type,
    transmission: row.transmission,
    engine_size: row.engine_size,
    drive_type: row.drive_type,
    seats: row.seats,
    doors: row.doors,
    body_type: row.body_type,
    body_condition: row.body_condition,
    interior_condition: row.interior_condition,
    exterior_color: row.exterior_color,
    interior_color: row.interior_color,
    main_image: row.main_image,
    images: row.images || [],
    image_folder: row.image_folder,
    specs: row.specs || [],
    features: row.features || [],
    status: row.status,
    stock_quantity: row.stock_quantity,
    location: row.location,
    slug: row.slug,
    description: row.description,
    meta_title: row.meta_title,
    meta_description: row.meta_description,
    view_count: row.view_count,
    popularity_score: row.popularity_score,
    is_featured: row.is_featured,
    is_recently_added: row.is_recently_added,
    is_price_reduced: row.is_price_reduced,
    import_batch_id: row.import_batch_id,
    import_source: row.import_source,
    import_notes: row.import_notes,
    added_date: row.added_date,
    updated_date: row.updated_date,
    sold_date: row.sold_date,
    created_at: row.created_at,
    updated_at: row.updated_at,
  };
}

// CRUD Operations for Cars

/**
 * Get all cars with optional filtering and pagination
 */
export async function getAllCars(
  filters: CarSearchFilters = {},
  page: number = 1,
  perPage: number = 50
): Promise<CarSearchResult> {
  if (!isDatabaseAvailable()) {
    throw new Error('Database not available');
  }

  try {
    // Build WHERE clause based on filters
    const conditions: string[] = [];
    const values: any[] = [];
    let paramIndex = 1;

    if (filters.make) {
      conditions.push(`make ILIKE $${paramIndex}`);
      values.push(`%${filters.make}%`);
      paramIndex++;
    }

    if (filters.model) {
      conditions.push(`model ILIKE $${paramIndex}`);
      values.push(`%${filters.model}%`);
      paramIndex++;
    }

    if (filters.year_min) {
      conditions.push(`year >= $${paramIndex}`);
      values.push(filters.year_min);
      paramIndex++;
    }

    if (filters.year_max) {
      conditions.push(`year <= $${paramIndex}`);
      values.push(filters.year_max);
      paramIndex++;
    }

    if (filters.price_min) {
      conditions.push(`price >= $${paramIndex}`);
      values.push(filters.price_min);
      paramIndex++;
    }

    if (filters.price_max) {
      conditions.push(`price <= $${paramIndex}`);
      values.push(filters.price_max);
      paramIndex++;
    }

    if (filters.mileage_max) {
      conditions.push(`mileage <= $${paramIndex}`);
      values.push(filters.mileage_max);
      paramIndex++;
    }

    if (filters.fuel_type) {
      conditions.push(`fuel_type = $${paramIndex}`);
      values.push(filters.fuel_type);
      paramIndex++;
    }

    if (filters.transmission) {
      conditions.push(`transmission = $${paramIndex}`);
      values.push(filters.transmission);
      paramIndex++;
    }

    if (filters.body_condition) {
      conditions.push(`body_condition = $${paramIndex}`);
      values.push(filters.body_condition);
      paramIndex++;
    }

    if (filters.status) {
      conditions.push(`status = $${paramIndex}`);
      values.push(filters.status);
      paramIndex++;
    }

    if (filters.is_featured !== undefined) {
      conditions.push(`is_featured = $${paramIndex}`);
      values.push(filters.is_featured);
      paramIndex++;
    }

    if (filters.is_recently_added !== undefined) {
      conditions.push(`is_recently_added = $${paramIndex}`);
      values.push(filters.is_recently_added);
      paramIndex++;
    }

    if (filters.search_query) {
      conditions.push(`(
        title ILIKE $${paramIndex} OR 
        make ILIKE $${paramIndex} OR 
        model ILIKE $${paramIndex} OR 
        description ILIKE $${paramIndex}
      )`);
      values.push(`%${filters.search_query}%`);
      paramIndex++;
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
    
    // Calculate offset for pagination
    const offset = (page - 1) * perPage;

    // Get total count
    const countQuery = `SELECT COUNT(*) as total FROM cars ${whereClause}`;
    const countResult = await sql.query(countQuery, values);
    const totalCount = parseInt(countResult.rows[0].total);

    // Get paginated results
    const dataQuery = `
      SELECT * FROM cars 
      ${whereClause}
      ORDER BY added_date DESC, created_at DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;
    values.push(perPage, offset);

    const { rows } = await sql.query(dataQuery, values);
    const cars = rows.map(mapRowToCar);

    return {
      cars,
      total_count: totalCount,
      page,
      per_page: perPage,
      total_pages: Math.ceil(totalCount / perPage),
      filters_applied: filters,
    };
  } catch (error) {
    console.error('Error fetching cars:', error);
    throw new Error('Failed to fetch cars from database');
  }
}

/**
 * Get car by ID
 */
export async function getCarById(id: string): Promise<Car | null> {
  if (!isDatabaseAvailable()) {
    throw new Error('Database not available');
  }

  try {
    const { rows } = await sql`
      SELECT * FROM cars WHERE id = ${id}
    `;

    if (rows.length === 0) {
      return null;
    }

    return mapRowToCar(rows[0]);
  } catch (error) {
    console.error('Error fetching car by ID:', error);
    throw new Error('Failed to fetch car from database');
  }
}

/**
 * Get car by car_id (unique identifier)
 */
export async function getCarByCarId(carId: string): Promise<Car | null> {
  if (!isDatabaseAvailable()) {
    throw new Error('Database not available');
  }

  try {
    const { rows } = await sql`
      SELECT * FROM cars WHERE car_id = ${carId}
    `;

    if (rows.length === 0) {
      return null;
    }

    return mapRowToCar(rows[0]);
  } catch (error) {
    console.error('Error fetching car by car_id:', error);
    throw new Error('Failed to fetch car from database');
  }
}

/**
 * Get car by slug
 */
export async function getCarBySlug(slug: string): Promise<Car | null> {
  if (!isDatabaseAvailable()) {
    throw new Error('Database not available');
  }

  try {
    const { rows } = await sql`
      SELECT * FROM cars WHERE slug = ${slug}
    `;

    if (rows.length === 0) {
      return null;
    }

    // Increment view count
    await sql`
      UPDATE cars
      SET view_count = view_count + 1, updated_at = NOW()
      WHERE slug = ${slug}
    `;

    return mapRowToCar(rows[0]);
  } catch (error) {
    console.error('Error fetching car by slug:', error);
    throw new Error('Failed to fetch car from database');
  }
}

/**
 * Create a new car
 */
export async function createCar(carData: CreateCarInput): Promise<Car> {
  if (!isDatabaseAvailable()) {
    throw new Error('Database not available');
  }

  try {
    // Generate slug if not provided
    const slug = carData.slug || generateCarSlug(carData.make, carData.model, carData.year, carData.car_id);

    const { rows } = await sql`
      INSERT INTO cars (
        car_id, make, model, year, title, price, original_price, currency,
        mileage, fuel_type, transmission, engine_size, drive_type, seats, doors, body_type,
        body_condition, interior_condition, exterior_color, interior_color,
        main_image, images, image_folder, specs, features,
        status, stock_quantity, location, slug, description, meta_title, meta_description,
        is_featured, import_batch_id, import_source, import_notes
      ) VALUES (
        ${carData.car_id}, ${carData.make}, ${carData.model}, ${carData.year}, ${carData.title},
        ${carData.price}, ${carData.original_price || null}, ${carData.currency || 'JPY'},
        ${carData.mileage || null}, ${carData.fuel_type || null}, ${carData.transmission || null},
        ${carData.engine_size || null}, ${carData.drive_type || null}, ${carData.seats || null},
        ${carData.doors || null}, ${carData.body_type || null},
        ${carData.body_condition || 'Good'}, ${carData.interior_condition || 'Good'},
        ${carData.exterior_color || null}, ${carData.interior_color || null},
        ${carData.main_image || null}, ${JSON.stringify(carData.images || [])}, ${carData.image_folder || null},
        ${JSON.stringify(carData.specs || [])}, ${JSON.stringify(carData.features || [])},
        ${carData.status || 'Available'}, ${carData.stock_quantity || 1}, ${carData.location || 'Japan'},
        ${slug}, ${carData.description || null}, ${carData.meta_title || null}, ${carData.meta_description || null},
        ${carData.is_featured || false}, ${carData.import_batch_id || null}, ${carData.import_source || 'Manual'},
        ${carData.import_notes || null}
      )
      RETURNING *
    `;

    return mapRowToCar(rows[0]);
  } catch (error) {
    console.error('Error creating car:', error);
    if (error instanceof Error && error.message.includes('duplicate key')) {
      throw new Error(`Car with ID '${carData.car_id}' already exists`);
    }
    throw new Error('Failed to create car in database');
  }
}

/**
 * Update an existing car
 */
export async function updateCar(id: string, updates: UpdateCarInput): Promise<Car | null> {
  if (!isDatabaseAvailable()) {
    throw new Error('Database not available');
  }

  try {
    // Build dynamic update query
    const updateFields: string[] = [];
    const values: any[] = [];
    let paramIndex = 1;

    // Add each field that has a value
    Object.entries(updates).forEach(([key, value]) => {
      if (value !== undefined) {
        if (key === 'images' || key === 'specs' || key === 'features') {
          updateFields.push(`${key} = $${paramIndex}`);
          values.push(JSON.stringify(value));
        } else {
          updateFields.push(`${key} = $${paramIndex}`);
          values.push(value);
        }
        paramIndex++;
      }
    });

    if (updateFields.length === 0) {
      throw new Error('No fields to update');
    }

    // Always update the updated_at timestamp
    updateFields.push(`updated_at = NOW()`);
    updateFields.push(`updated_date = NOW()`);

    values.push(id); // Add ID as the last parameter

    const query = `
      UPDATE cars
      SET ${updateFields.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING *
    `;

    const { rows } = await sql.query(query, values);

    if (rows.length === 0) {
      return null;
    }

    return mapRowToCar(rows[0]);
  } catch (error) {
    console.error('Error updating car:', error);
    throw new Error('Failed to update car in database');
  }
}

/**
 * Delete a car
 */
export async function deleteCar(id: string): Promise<boolean> {
  if (!isDatabaseAvailable()) {
    throw new Error('Database not available');
  }

  try {
    const result = await sql`
      DELETE FROM cars WHERE id = ${id}
    `;

    return result.rowCount > 0;
  } catch (error) {
    console.error('Error deleting car:', error);
    throw new Error('Failed to delete car from database');
  }
}

/**
 * Bulk delete cars
 */
export async function bulkDeleteCars(carIds: string[]): Promise<number> {
  if (!isDatabaseAvailable()) {
    throw new Error('Database not available');
  }

  if (carIds.length === 0) {
    return 0;
  }

  try {
    const placeholders = carIds.map((_, index) => `$${index + 1}`).join(',');
    const query = `DELETE FROM cars WHERE id IN (${placeholders})`;

    const result = await sql.query(query, carIds);
    return result.rowCount || 0;
  } catch (error) {
    console.error('Error bulk deleting cars:', error);
    throw new Error('Failed to bulk delete cars from database');
  }
}
