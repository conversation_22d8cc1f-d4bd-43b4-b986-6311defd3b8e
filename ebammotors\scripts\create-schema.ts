/**
 * Database Schema Creation Script
 *
 * This script creates the required database tables and indexes
 * for the EBAM Motors car inventory system.
 */

const { sql } = require('@vercel/postgres');

async function createSchema() {
  console.log('🚀 Creating database schema...');
  
  try {
    // Create cars table
    console.log('📋 Creating cars table...');
    await sql`
      CREATE TABLE IF NOT EXISTS cars (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        car_id VARCHAR(255) UNIQUE NOT NULL,
        
        -- Basic Information
        make VARCHAR(100) NOT NULL,
        model VARCHAR(100) NOT NULL,
        year INTEGER NOT NULL CHECK (year >= 1990 AND year <= 2030),
        title VARCHAR(255) NOT NULL,
        
        -- Pricing
        price INTEGER NOT NULL CHECK (price > 0),
        original_price INTEGER,
        currency VARCHAR(3) DEFAULT 'JPY',
        
        -- Technical Specifications
        mileage INTEGER CHECK (mileage >= 0),
        fuel_type VARCHAR(50),
        transmission VARCHAR(50),
        engine_size VARCHAR(50),
        drive_type VARCHAR(50),
        seats INTEGER CHECK (seats > 0 AND seats <= 50),
        doors INTEGER CHECK (doors > 0 AND doors <= 10),
        body_type VARCHAR(50),
        
        -- Condition & Features
        body_condition VARCHAR(50) DEFAULT 'Good',
        interior_condition VARCHAR(50) DEFAULT 'Good',
        exterior_color VARCHAR(50),
        interior_color VARCHAR(50),
        
        -- Images & Media
        main_image VARCHAR(500),
        images TEXT[],
        image_folder VARCHAR(255),
        
        -- Specifications Array
        specs TEXT[],
        features TEXT[],
        
        -- Status & Availability
        status VARCHAR(50) DEFAULT 'Available',
        stock_quantity INTEGER DEFAULT 1 CHECK (stock_quantity >= 0),
        location VARCHAR(100) DEFAULT 'Japan',
        
        -- SEO & Display
        slug VARCHAR(255) UNIQUE,
        description TEXT,
        meta_title VARCHAR(255),
        meta_description TEXT,
        
        -- Tracking & Analytics
        view_count INTEGER DEFAULT 0,
        popularity_score INTEGER DEFAULT 0,
        is_featured BOOLEAN DEFAULT FALSE,
        is_recently_added BOOLEAN DEFAULT TRUE,
        is_price_reduced BOOLEAN DEFAULT FALSE,
        
        -- Import & Management
        import_batch_id VARCHAR(255),
        import_source VARCHAR(100),
        import_notes TEXT,
        
        -- Timestamps
        added_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        sold_date TIMESTAMP WITH TIME ZONE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `;
    console.log('✅ Cars table created successfully');

    // Create csv_imports table
    console.log('📋 Creating csv_imports table...');
    await sql`
      CREATE TABLE IF NOT EXISTS csv_imports (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        batch_id VARCHAR(255) UNIQUE NOT NULL,
        filename VARCHAR(255) NOT NULL,
        total_rows INTEGER NOT NULL,
        successful_imports INTEGER DEFAULT 0,
        failed_imports INTEGER DEFAULT 0,
        status VARCHAR(50) DEFAULT 'processing',
        error_log TEXT[],
        import_summary JSONB,
        imported_by VARCHAR(255),
        started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        completed_at TIMESTAMP WITH TIME ZONE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `;
    console.log('✅ CSV imports table created successfully');

    console.log('🔍 Creating indexes for better performance...');
    
    // Create indexes
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_cars_make_model ON cars(make, model)',
      'CREATE INDEX IF NOT EXISTS idx_cars_year ON cars(year)',
      'CREATE INDEX IF NOT EXISTS idx_cars_price ON cars(price)',
      'CREATE INDEX IF NOT EXISTS idx_cars_status ON cars(status)',
      'CREATE INDEX IF NOT EXISTS idx_cars_added_date ON cars(added_date DESC)',
      'CREATE INDEX IF NOT EXISTS idx_cars_mileage ON cars(mileage)',
      'CREATE INDEX IF NOT EXISTS idx_cars_fuel_type ON cars(fuel_type)',
      'CREATE INDEX IF NOT EXISTS idx_cars_transmission ON cars(transmission)',
      'CREATE INDEX IF NOT EXISTS idx_cars_body_condition ON cars(body_condition)',
      'CREATE INDEX IF NOT EXISTS idx_cars_is_featured ON cars(is_featured)',
      'CREATE INDEX IF NOT EXISTS idx_cars_is_recently_added ON cars(is_recently_added)',
      'CREATE INDEX IF NOT EXISTS idx_cars_slug ON cars(slug)',
      'CREATE INDEX IF NOT EXISTS idx_cars_car_id ON cars(car_id)',
      'CREATE INDEX IF NOT EXISTS idx_csv_imports_batch_id ON csv_imports(batch_id)',
      'CREATE INDEX IF NOT EXISTS idx_csv_imports_status ON csv_imports(status)',
      'CREATE INDEX IF NOT EXISTS idx_csv_imports_started_at ON csv_imports(started_at DESC)'
    ];

    for (const indexQuery of indexes) {
      await sql.query(indexQuery);
    }

    // Create full-text search index
    await sql`
      CREATE INDEX IF NOT EXISTS idx_cars_search ON cars USING gin(
        to_tsvector('english', 
          COALESCE(title, '') || ' ' || 
          COALESCE(make, '') || ' ' || 
          COALESCE(model, '') || ' ' || 
          COALESCE(description, '')
        )
      )
    `;

    console.log('✅ All indexes created successfully');
    console.log('🎉 Database schema created successfully!');
    console.log('');
    console.log('Next steps:');
    console.log('1. Start your dev server: npm run dev');
    console.log('2. Go to: http://localhost:3000/admin/cars');
    console.log('3. Enter your admin password');
    console.log('4. Start importing cars via CSV!');
    
  } catch (error) {
    console.error('❌ Error creating schema:', error);
    throw error;
  }
}

// Run the schema creation
createSchema()
  .then(() => {
    console.log('✅ Schema creation completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Schema creation failed:', error);
    process.exit(1);
  });
