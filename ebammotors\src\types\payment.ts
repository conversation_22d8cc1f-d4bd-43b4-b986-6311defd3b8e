// Payment and Order Management Types

export interface PaymentMethod {
  id: string;
  type: 'bank_transfer' | 'mobile_money' | 'stripe' | 'cash_agent';
  name: string;
  description: string;
  icon: string;
  enabled: boolean;
  config?: {
    // Bank transfer config
    bankName?: string;
    accountNumber?: string;
    accountName?: string;
    swiftCode?: string;
    // Mobile money config
    provider?: 'mtn' | 'vodafone' | 'airtel';
    number?: string;
    // Stripe config
    publishableKey?: string;
    // Cash agent config
    agentLocations?: string[];
  };
}

export interface ShippingMethod {
  id: string;
  name: string;
  description: string;
  estimatedDays: number;
  basePrice: number;
  pricePerKg?: number;
  maxWeight?: number;
  trackingIncluded: boolean;
  insuranceIncluded: boolean;
}

export interface ShippingCalculation {
  method: ShippingMethod;
  cost: number;
  estimatedDelivery: string;
  weight: number;
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
}

export interface Order {
  id: string;
  orderNumber: string;
  customerId: string;
  customerInfo: {
    name: string;
    email: string;
    phone: string;
    address: {
      street: string;
      city: string;
      state: string;
      country: string;
      postalCode: string;
    };
  };
  vehicle: {
    id: string;
    title: string;
    price: number;
    currency: string;
    images: string[];
    specs: string[];
  };
  payment: {
    method: PaymentMethod;
    amount: number;
    currency: string;
    status: 'pending' | 'processing' | 'completed' | 'failed' | 'refunded';
    transactionId?: string;
    paidAt?: string;
    dueDate?: string;
  };
  shipping: {
    method: ShippingMethod;
    cost: number;
    address: {
      street: string;
      city: string;
      state: string;
      country: string;
      postalCode: string;
    };
    estimatedDelivery: string;
    trackingNumber?: string;
    status: 'pending' | 'preparing' | 'shipped' | 'in_transit' | 'delivered' | 'returned';
    updates: ShippingUpdate[];
  };
  status: 'draft' | 'pending_payment' | 'paid' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded';
  totalAmount: number;
  currency: string;
  createdAt: string;
  updatedAt: string;
  notes?: string;
  invoiceGenerated: boolean;
  invoiceUrl?: string;
}

export interface ShippingUpdate {
  id: string;
  timestamp: string;
  status: string;
  location: string;
  description: string;
  estimatedDelivery?: string;
}

export interface Invoice {
  id: string;
  orderId: string;
  invoiceNumber: string;
  issuedDate: string;
  dueDate: string;
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
  items: InvoiceItem[];
  subtotal: number;
  tax: number;
  shipping: number;
  total: number;
  currency: string;
  paymentTerms: string;
  notes?: string;
  pdfUrl?: string;
}

export interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  total: number;
}

export interface PaymentStatus {
  orderId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'refunded';
  amount: number;
  currency: string;
  method: string;
  transactionId?: string;
  timestamp: string;
  message?: string;
  nextAction?: {
    type: 'redirect' | 'wait' | 'contact_support';
    url?: string;
    message: string;
  };
}

export interface PaymentWebhook {
  id: string;
  type: string;
  data: any;
  timestamp: string;
  processed: boolean;
  orderId?: string;
}

// CRM Types for Enhanced Customer Management

export interface Lead {
  id: string;
  source: 'chatbot' | 'contact_form' | 'phone' | 'email' | 'whatsapp' | 'manual';
  status: 'new' | 'contacted' | 'qualified' | 'proposal_sent' | 'negotiating' | 'won' | 'lost' | 'nurturing';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  customerInfo: {
    name: string;
    email?: string;
    phone?: string;
    location?: string;
    preferredContact?: 'email' | 'phone' | 'whatsapp';
  };
  inquiry: {
    subject?: string;
    message: string;
    productInterest?: string;
    budgetRange?: string;
    timeline?: string;
  };
  assignedTo?: string; // Admin/sales person ID
  tags: string[];
  estimatedValue?: number;
  currency?: string;
  followUpDate?: string;
  lastContactDate?: string;
  createdAt: string;
  updatedAt: string;
  notes?: string;
  customFields?: Record<string, any>;
}

export interface Customer {
  id: string;
  personalInfo: {
    name: string;
    email: string;
    phone?: string;
    dateOfBirth?: string;
    gender?: 'male' | 'female' | 'other';
    preferredLanguage?: 'en' | 'ja';
  };
  address: {
    street?: string;
    city?: string;
    state?: string;
    country?: string;
    postalCode?: string;
  };
  businessInfo?: {
    companyName?: string;
    industry?: string;
    position?: string;
  };
  preferences: {
    notifications: {
      email: boolean;
      sms: boolean;
      push: boolean;
      marketing: boolean;
    };
    currency: string;
    communicationPreference: 'email' | 'phone' | 'whatsapp' | 'sms';
  };
  status: 'active' | 'inactive' | 'blocked' | 'vip';
  segment: 'new' | 'regular' | 'premium' | 'enterprise';
  loyaltyPoints: number;
  membershipTier: 'Bronze' | 'Silver' | 'Gold' | 'Platinum';
  totalSpent: number;
  totalOrders: number;
  averageOrderValue: number;
  lastOrderDate?: string;
  lastLoginDate?: string;
  acquisitionSource: string;
  referredBy?: string;
  tags: string[];
  createdAt: string;
  updatedAt: string;
  notes?: string;
}

export interface Interaction {
  id: string;
  customerId?: string;
  leadId?: string;
  type: 'email' | 'phone' | 'chat' | 'whatsapp' | 'meeting' | 'order' | 'support' | 'review' | 'website_visit';
  direction: 'inbound' | 'outbound';
  channel: 'website' | 'email' | 'phone' | 'whatsapp' | 'social_media' | 'in_person';
  subject?: string;
  content: string;
  attachments?: string[];
  duration?: number; // in minutes for calls/meetings
  outcome?: 'successful' | 'no_response' | 'follow_up_needed' | 'resolved' | 'escalated';
  sentiment?: 'positive' | 'neutral' | 'negative';
  tags: string[];
  relatedOrderId?: string;
  relatedProductId?: string;
  assignedTo?: string;
  createdAt: string;
  createdBy: string; // User ID who logged the interaction
  metadata?: Record<string, any>;
}

export interface FollowUp {
  id: string;
  type: 'email' | 'phone' | 'whatsapp' | 'task' | 'reminder';
  status: 'pending' | 'sent' | 'completed' | 'cancelled' | 'failed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  customerId?: string;
  leadId?: string;
  orderId?: string;
  title: string;
  description: string;
  scheduledDate: string;
  completedDate?: string;
  assignedTo?: string;
  template?: string; // Email template ID
  emailConfig?: {
    subject: string;
    body: string;
    attachments?: string[];
  };
  automationRule?: {
    trigger: 'abandoned_cart' | 'order_delivered' | 'no_activity' | 'birthday' | 'custom';
    delay: number; // in hours
    conditions?: Record<string, any>;
  };
  result?: {
    success: boolean;
    message?: string;
    responseReceived?: boolean;
    nextAction?: string;
  };
  createdAt: string;
  updatedAt: string;
  createdBy: string;
}

export interface CustomerActivity {
  id: string;
  customerId: string;
  type: 'login' | 'page_view' | 'product_view' | 'cart_add' | 'cart_remove' | 'search' | 'download' | 'share';
  details: {
    page?: string;
    productId?: string;
    searchQuery?: string;
    duration?: number;
    referrer?: string;
    userAgent?: string;
    ipAddress?: string;
  };
  timestamp: string;
  sessionId?: string;
}

export interface CampaignResponse {
  id: string;
  campaignId: string;
  customerId: string;
  type: 'email_open' | 'email_click' | 'sms_click' | 'conversion' | 'unsubscribe';
  timestamp: string;
  details?: Record<string, any>;
}

export interface CustomerSegment {
  id: string;
  name: string;
  description: string;
  criteria: {
    totalSpent?: { min?: number; max?: number };
    orderCount?: { min?: number; max?: number };
    lastOrderDays?: number; // days since last order
    loyaltyPoints?: { min?: number; max?: number };
    membershipTier?: string[];
    location?: string[];
    tags?: string[];
    customFields?: Record<string, any>;
  };
  customerCount: number;
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
}
