'use client';

import { useState, useEffect } from 'react';
import { <PERSON>ert<PERSON>riangle, CheckCircle, XCircle, Eye, Settings } from 'lucide-react';

interface HallucinationIncident {
  id: string;
  timestamp: string;
  userMessage: string;
  aiResponse: string;
  riskLevel: 'low' | 'medium' | 'high';
  issues: string[];
  cleanedResponse: string;
}

export default function HallucinationMonitor() {
  const [incidents, setIncidents] = useState<HallucinationIncident[]>([]);
  const [stats, setStats] = useState({
    totalResponses: 0,
    lowRisk: 0,
    mediumRisk: 0,
    highRisk: 0,
    hallucinationRate: 0
  });
  const [showSettings, setShowSettings] = useState(false);

  // Mock data for demonstration - in real implementation, this would come from your logging system
  useEffect(() => {
    const mockIncidents: HallucinationIncident[] = [
      {
        id: '1',
        timestamp: new Date().toISOString(),
        userMessage: 'What is the cheapest car?',
        aiResponse: 'The cheapest car is Toyota Voxy at ¥300,000',
        riskLevel: 'low',
        issues: [],
        cleanedResponse: 'The cheapest car is Toyota Voxy at ¥300,000'
      },
      {
        id: '2',
        timestamp: new Date(Date.now() - 3600000).toISOString(),
        userMessage: 'Do you have Honda Civic?',
        aiResponse: 'Yes, we have Honda Civic for ¥450,000 with 2 year warranty',
        riskLevel: 'high',
        issues: ['Unknown Honda model', 'Invalid price format', 'Warranty claim'],
        cleanedResponse: 'I can only provide information about our current inventory: Toyota Voxy, Noah, Sienta, Yaris, and Vitz. For other models, please contact our team.'
      }
    ];

    setIncidents(mockIncidents);
    setStats({
      totalResponses: 50,
      lowRisk: 45,
      mediumRisk: 3,
      highRisk: 2,
      hallucinationRate: 10 // percentage
    });
  }, []);

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'text-green-600 bg-green-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'high': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getRiskIcon = (risk: string) => {
    switch (risk) {
      case 'low': return <CheckCircle className="w-4 h-4" />;
      case 'medium': return <AlertTriangle className="w-4 h-4" />;
      case 'high': return <XCircle className="w-4 h-4" />;
      default: return <Eye className="w-4 h-4" />;
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">AI Hallucination Monitor</h3>
        <button
          onClick={() => setShowSettings(!showSettings)}
          className="flex items-center space-x-2 px-3 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
        >
          <Settings className="w-4 h-4" />
          <span>Settings</span>
        </button>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div className="p-3 bg-blue-50 rounded-lg">
          <p className="text-sm text-blue-600">Total Responses</p>
          <p className="text-xl font-bold text-blue-900">{stats.totalResponses}</p>
        </div>
        <div className="p-3 bg-green-50 rounded-lg">
          <p className="text-sm text-green-600">Low Risk</p>
          <p className="text-xl font-bold text-green-900">{stats.lowRisk}</p>
        </div>
        <div className="p-3 bg-yellow-50 rounded-lg">
          <p className="text-sm text-yellow-600">Medium Risk</p>
          <p className="text-xl font-bold text-yellow-900">{stats.mediumRisk}</p>
        </div>
        <div className="p-3 bg-red-50 rounded-lg">
          <p className="text-sm text-red-600">High Risk</p>
          <p className="text-xl font-bold text-red-900">{stats.highRisk}</p>
        </div>
      </div>

      {/* Hallucination Rate */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700">Hallucination Rate</span>
          <span className={`text-sm font-bold ${stats.hallucinationRate > 15 ? 'text-red-600' : stats.hallucinationRate > 5 ? 'text-yellow-600' : 'text-green-600'}`}>
            {stats.hallucinationRate}%
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className={`h-2 rounded-full ${stats.hallucinationRate > 15 ? 'bg-red-500' : stats.hallucinationRate > 5 ? 'bg-yellow-500' : 'bg-green-500'}`}
            style={{ width: `${Math.min(stats.hallucinationRate, 100)}%` }}
          ></div>
        </div>
        <p className="text-xs text-gray-500 mt-1">
          Target: &lt;5% | Warning: &gt;10% | Critical: &gt;15%
        </p>
      </div>

      {/* Settings Panel */}
      {showSettings && (
        <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="font-medium text-blue-900 mb-3">Hallucination Control Settings</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <p><strong>Temperature:</strong> 0.3 (Lower = less creative)</p>
              <p><strong>Top P:</strong> 0.8 (Nucleus sampling)</p>
              <p><strong>Max Tokens:</strong> 200</p>
            </div>
            <div>
              <p><strong>Frequency Penalty:</strong> 0.5</p>
              <p><strong>Presence Penalty:</strong> 0.3</p>
              <p><strong>Validation:</strong> Enabled</p>
            </div>
          </div>
          <div className="mt-3 p-2 bg-blue-100 rounded text-xs text-blue-800">
            💡 <strong>Tip:</strong> Lower temperature reduces creativity but increases accuracy. 
            Adjust in <code>src/config/chatbotConfig.ts</code>
          </div>
        </div>
      )}

      {/* Recent Incidents */}
      <div>
        <h4 className="font-medium text-gray-900 mb-3">Recent AI Responses</h4>
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {incidents.map((incident) => (
            <div key={incident.id} className="border border-gray-200 rounded-lg p-3">
              <div className="flex items-center justify-between mb-2">
                <div className={`flex items-center space-x-2 px-2 py-1 rounded-full text-xs font-medium ${getRiskColor(incident.riskLevel)}`}>
                  {getRiskIcon(incident.riskLevel)}
                  <span>{incident.riskLevel.toUpperCase()} RISK</span>
                </div>
                <span className="text-xs text-gray-500">
                  {new Date(incident.timestamp).toLocaleString()}
                </span>
              </div>
              
              <div className="space-y-2 text-sm">
                <div>
                  <p className="font-medium text-gray-700">User:</p>
                  <p className="text-gray-600 bg-gray-50 p-2 rounded">{incident.userMessage}</p>
                </div>
                
                {incident.riskLevel !== 'low' && (
                  <div>
                    <p className="font-medium text-red-700">Original AI Response:</p>
                    <p className="text-red-600 bg-red-50 p-2 rounded">{incident.aiResponse}</p>
                  </div>
                )}
                
                <div>
                  <p className="font-medium text-green-700">
                    {incident.riskLevel === 'low' ? 'AI Response:' : 'Cleaned Response:'}
                  </p>
                  <p className="text-green-600 bg-green-50 p-2 rounded">{incident.cleanedResponse}</p>
                </div>
                
                {incident.issues.length > 0 && (
                  <div>
                    <p className="font-medium text-orange-700">Issues Detected:</p>
                    <ul className="text-orange-600 text-xs list-disc list-inside">
                      {incident.issues.map((issue, index) => (
                        <li key={index}>{issue}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <h4 className="font-medium text-gray-900 mb-3">Quick Actions</h4>
        <div className="flex flex-wrap gap-2">
          <button className="px-3 py-1 bg-blue-100 text-blue-700 rounded text-sm hover:bg-blue-200">
            Export Log
          </button>
          <button className="px-3 py-1 bg-green-100 text-green-700 rounded text-sm hover:bg-green-200">
            Clear Low Risk
          </button>
          <button className="px-3 py-1 bg-yellow-100 text-yellow-700 rounded text-sm hover:bg-yellow-200">
            Review Medium Risk
          </button>
          <button className="px-3 py-1 bg-red-100 text-red-700 rounded text-sm hover:bg-red-200">
            Flag High Risk
          </button>
        </div>
      </div>
    </div>
  );
}
