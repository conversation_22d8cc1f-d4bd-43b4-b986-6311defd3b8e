'use client';

// React hooks imported for future functionality
import Image from 'next/image';
import Link from 'next/link';
import { Eye, Heart, Calendar, Gauge, Fuel, Settings, MapPin, TrendingDown, Clock, Package, ShoppingCart } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

interface StockItem {
  id: number;
  category: string;
  title: string;
  price: string;
  location: string;
  status: string;
  image: string;
  images: string[];
  specs: string[];
  carId: string;
  year?: number;
  mileage?: number;
  fuelType?: string;
  transmission?: string;
  bodyCondition?: string;
  addedDate?: string;
  originalPrice?: string;
  stockQuantity?: number;
  popularity?: number;
}

interface ListViewProps {
  items: StockItem[];
  locale: string;
  onItemClick: (item: StockItem) => void;
}

export default function ListView({ items, locale, onItemClick }: ListViewProps) {
  const { user, isFavorite, addToFavorites, removeFromFavorites } = useAuth();

  const handleFavoriteClick = (e: React.MouseEvent, item: StockItem) => {
    e.stopPropagation();
    if (!user) {
      alert(locale === 'en' ? 'Please sign in to add favorites' : 'お気に入りに追加するにはサインインしてください');
      return;
    }
    
    if (isFavorite(item.carId)) {
      removeFromFavorites(item.carId);
    } else {
      addToFavorites(item.carId);
    }
  };

  const isRecentlyAdded = (addedDate: string) => {
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    return new Date(addedDate) > sevenDaysAgo;
  };

  const getStockIndicator = (quantity: number) => {
    if (quantity <= 1) return { color: 'text-red-600', label: locale === 'en' ? 'Last one!' : '最後の1台！' };
    if (quantity <= 3) return { color: 'text-yellow-600', label: locale === 'en' ? 'Limited stock' : '在庫僅少' };
    return { color: 'text-green-600', label: locale === 'en' ? 'In stock' : '在庫あり' };
  };

  if (items.length === 0) {
    return (
      <div className="text-center py-12">
        <Package className="w-16 h-16 text-neutral-300 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-neutral-600 mb-2">
          {locale === 'en' ? 'No vehicles found' : '車両が見つかりません'}
        </h3>
        <p className="text-neutral-500">
          {locale === 'en' ? 'Try adjusting your search criteria' : '検索条件を調整してみてください'}
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {items.map((item) => {
        const stockInfo = getStockIndicator(item.stockQuantity || 1);
        
        return (
          <div
            key={item.id}
            className="bg-white rounded-lg border hover:shadow-lg transition-all duration-300 cursor-pointer overflow-hidden"
            onClick={() => onItemClick(item)}
          >
            <div className="flex flex-col md:flex-row">
              {/* Image */}
              <div className="relative w-full md:w-80 h-48 md:h-40 flex-shrink-0">
                <Image
                  src={item.image}
                  alt={item.title}
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, 320px"
                />
                
                {/* Badges */}
                <div className="absolute top-3 left-3 flex flex-col space-y-2">
                  {isRecentlyAdded(item.addedDate || '') && (
                    <span className="bg-blue-600 text-white text-xs px-2 py-1 rounded-full flex items-center space-x-1">
                      <Clock className="w-3 h-3" />
                      <span>{locale === 'en' ? 'New' : '新着'}</span>
                    </span>
                  )}
                  {item.originalPrice && (
                    <span className="bg-red-600 text-white text-xs px-2 py-1 rounded-full flex items-center space-x-1">
                      <TrendingDown className="w-3 h-3" />
                      <span>{locale === 'en' ? 'Price Cut' : '値下げ'}</span>
                    </span>
                  )}
                </div>

                {/* Status and Favorite */}
                <div className="absolute top-3 right-3 flex items-center space-x-2">
                  <button
                    onClick={(e) => handleFavoriteClick(e, item)}
                    className={`p-2 rounded-full transition-colors ${
                      user && isFavorite(item.carId)
                        ? 'bg-red-100 text-red-600 hover:bg-red-200'
                        : 'bg-white/80 text-neutral-600 hover:bg-white hover:text-red-600'
                    }`}
                  >
                    <Heart className={`w-4 h-4 ${user && isFavorite(item.carId) ? 'fill-current' : ''}`} />
                  </button>
                  <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                    item.status === 'Available' ? 'bg-green-100 text-green-800' :
                    item.status === 'Reserved' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {item.status}
                  </span>
                </div>
              </div>

              {/* Content */}
              <div className="flex-1 p-6">
                <div className="flex flex-col h-full">
                  {/* Header */}
                  <div className="flex-1">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h3 className="text-xl font-semibold text-neutral-800 mb-1 hover:text-primary-600 transition-colors">
                          {item.title}
                        </h3>
                        <div className="flex items-center space-x-2 text-sm text-neutral-600">
                          <MapPin className="w-4 h-4" />
                          <span>{item.location}</span>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="flex items-center space-x-2">
                          {item.originalPrice && (
                            <span className="text-neutral-500 line-through text-sm">
                              {item.originalPrice}
                            </span>
                          )}
                          <div className="text-2xl font-bold text-primary-600">{item.price}</div>
                        </div>
                        <div className={`text-xs font-medium ${stockInfo.color}`}>
                          {stockInfo.label}
                        </div>
                      </div>
                    </div>

                    {/* Specifications */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                      {item.year && (
                        <div className="flex items-center space-x-2 text-sm text-neutral-600">
                          <Calendar className="w-4 h-4" />
                          <span>{item.year}</span>
                        </div>
                      )}
                      {item.mileage && (
                        <div className="flex items-center space-x-2 text-sm text-neutral-600">
                          <Gauge className="w-4 h-4" />
                          <span>{Math.floor(item.mileage / 1000)}k km</span>
                        </div>
                      )}
                      {item.fuelType && (
                        <div className="flex items-center space-x-2 text-sm text-neutral-600">
                          <Fuel className="w-4 h-4" />
                          <span>{item.fuelType}</span>
                        </div>
                      )}
                      {item.transmission && (
                        <div className="flex items-center space-x-2 text-sm text-neutral-600">
                          <Settings className="w-4 h-4" />
                          <span>{item.transmission}</span>
                        </div>
                      )}
                    </div>

                    {/* Additional Specs */}
                    <div className="flex flex-wrap gap-2 mb-4">
                      {item.specs.slice(0, 4).map((spec, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-neutral-100 text-neutral-600 text-xs rounded-full"
                        >
                          {spec}
                        </span>
                      ))}
                      {item.specs.length > 4 && (
                        <span className="px-2 py-1 bg-neutral-100 text-neutral-600 text-xs rounded-full">
                          +{item.specs.length - 4} {locale === 'en' ? 'more' : 'その他'}
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center justify-between pt-4 border-t">
                    <div className="flex items-center space-x-4 text-xs text-neutral-500">
                      {item.addedDate && (
                        <span>
                          {locale === 'en' ? 'Added' : '追加日'}: {new Date(item.addedDate).toLocaleDateString()}
                        </span>
                      )}
                      {item.stockQuantity && (
                        <span className="flex items-center space-x-1">
                          <Package className="w-3 h-3" />
                          <span>{item.stockQuantity} {locale === 'en' ? 'available' : '台在庫'}</span>
                        </span>
                      )}
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          onItemClick(item);
                        }}
                        className="flex items-center space-x-2 text-neutral-600 hover:text-primary-600 transition-colors border border-neutral-300 px-3 py-2 rounded-lg hover:bg-neutral-50"
                      >
                        <Eye className="w-4 h-4" />
                        <span className="text-sm">{locale === 'en' ? 'View' : '表示'}</span>
                      </button>
                      <Link
                        href={`/${locale}/stock/${item.carId}?quickbuy=true`}
                        onClick={(e) => e.stopPropagation()}
                        className="flex items-center space-x-2 bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors text-sm font-medium"
                      >
                        <ShoppingCart className="w-4 h-4" />
                        <span>{locale === 'en' ? 'Quick Buy' : 'クイック購入'}</span>
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
}
