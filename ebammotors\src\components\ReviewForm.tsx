'use client';

import { useState } from 'react';
import { Star } from 'lucide-react';
import { getMessagesSync } from '@/lib/messages';

interface ReviewFormProps {
  locale: string;
}

export default function ReviewForm({ locale }: ReviewFormProps) {
  const messages = getMessagesSync(locale);

  const [formData, setFormData] = useState({
    name: '',
    location: '',
    email: '',
    rating: 0,
    review: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);

  // Word limit for review text
  const MAX_WORDS = 150;

  // Helper function to count words
  const countWords = (text: string): number => {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    // Apply word limit to review field
    if (name === 'review') {
      const wordCount = countWords(value);
      if (wordCount > MAX_WORDS) {
        return; // Don't update if word limit exceeded
      }
    }

    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleRatingClick = (rating: number) => {
    setFormData(prev => ({ ...prev, rating }));
  };



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Validate required fields
      if (!formData.name.trim()) {
        alert(messages.reviews.enterFullName);
        return;
      }
      if (!formData.location.trim()) {
        alert(messages.reviews.enterLocation);
        return;
      }
      if (!formData.email.trim()) {
        alert(messages.reviews.enterEmail);
        return;
      }
      if (formData.rating === 0) {
        alert(messages.reviews.selectRating);
        return;
      }
      if (!formData.review.trim()) {
        alert(messages.reviews.writeReview);
        return;
      }

      // Validate word count
      const wordCount = countWords(formData.review);
      if (wordCount < 10) {
        alert(messages.reviews.minimumWords);
        return;
      }
      if (wordCount > MAX_WORDS) {
        alert(messages.reviews.maximumWords);
        return;
      }

      const formDataToSend = new FormData();

      // Add form fields
      Object.entries(formData).forEach(([key, value]) => {
        formDataToSend.append(key, value.toString());
      });

      // Add metadata
      formDataToSend.append('locale', locale);
      formDataToSend.append('submittedAt', new Date().toISOString());
      formDataToSend.append('status', 'pending'); // For moderation

      const response = await fetch('/api/reviews', {
        method: 'POST',
        body: formDataToSend,
      });

      if (response.ok) {
        const result = await response.json();
        console.log('Review submitted successfully:', result);
        setSubmitted(true);
      } else {
        const errorData = await response.json().catch(() => ({}));
        console.error('Server error:', response.status, errorData);

        // Provide specific error messages for common issues
        let errorMessage = errorData.message || `Server error: ${response.status}`;

        if (response.status === 400) {
          errorMessage = errorData.message || 'Please check your form data and try again.';
        } else if (response.status >= 500) {
          errorMessage = 'Server error. Please try again later.';
        }

        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error('Error submitting review:', error);
      alert(`Failed to submit review: ${error.message}. Please try again.`);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (submitted) {
    return (
      <div className="text-center py-12">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </div>
        <h3 className="text-2xl font-bold text-neutral-800 mb-4">{messages.reviews.thankYou}</h3>
        <p className="text-neutral-600 mb-6">
          {messages.reviews.reviewSubmittedSuccess}
        </p>
        <button
          onClick={() => {
            setSubmitted(false);
            setFormData({
              name: '', location: '', email: '', rating: 0, review: ''
            });
          }}
          className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-200"
        >
          {messages.reviews.submitAnotherReview}
        </button>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Personal Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label htmlFor="name" className="block text-sm font-semibold text-neutral-700 mb-2">
            {messages.reviews.fullName} *
          </label>
          <input
            type="text"
            id="name"
            name="name"
            required
            value={formData.name}
            onChange={handleInputChange}
            className="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            placeholder={messages.reviews.enterYourFullName}
          />
        </div>
        <div>
          <label htmlFor="location" className="block text-sm font-semibold text-neutral-700 mb-2">
            {messages.reviews.location} *
          </label>
          <input
            type="text"
            id="location"
            name="location"
            required
            value={formData.location}
            onChange={handleInputChange}
            className="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            placeholder={messages.reviews.cityCountry}
          />
        </div>
      </div>

      <div>
        <label htmlFor="email" className="block text-sm font-semibold text-neutral-700 mb-2">
          {messages.reviews.emailAddress} *
        </label>
        <input
          type="email"
          id="email"
          name="email"
          required
          value={formData.email}
          onChange={handleInputChange}
          className="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          placeholder={messages.reviews.yourEmailExample}
        />
      </div>

      {/* Rating */}
      <div>
        <label className="block text-sm font-semibold text-neutral-700 mb-2">
          {messages.reviews.overallRating} *
        </label>
        <div className="flex items-center space-x-2">
          {[1, 2, 3, 4, 5].map((star) => (
            <button
              key={star}
              type="button"
              onClick={() => handleRatingClick(star)}
              className="focus:outline-none"
            >
              <Star
                className={`w-8 h-8 transition-colors duration-200 ${
                  star <= formData.rating
                    ? 'text-yellow-400 fill-current'
                    : 'text-neutral-300 hover:text-yellow-300'
                }`}
              />
            </button>
          ))}
          <span className="ml-4 text-sm text-neutral-600">
            {formData.rating > 0 ? `${formData.rating} ${messages.reviews.outOfStars}` : messages.reviews.clickToRate}
          </span>
        </div>
      </div>

      {/* Review Content */}

      <div>
        <div className="flex justify-between items-center mb-2">
          <label htmlFor="review" className="block text-sm font-semibold text-neutral-700">
            {messages.reviews.yourReview} *
          </label>
          <span className={`text-sm ${
            countWords(formData.review) > MAX_WORDS ? 'text-red-600' :
            countWords(formData.review) > MAX_WORDS * 0.8 ? 'text-yellow-600' : 'text-neutral-500'
          }`}>
            {countWords(formData.review)}/{MAX_WORDS} {messages.reviews.words}
          </span>
        </div>
        <textarea
          id="review"
          name="review"
          required
          rows={6}
          value={formData.review}
          onChange={handleInputChange}
          className="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-vertical"
          placeholder={messages.reviews.shareDetailedExperience}
        />
        <p className="text-xs text-neutral-500 mt-1">
          {messages.reviews.minimumWordsNote}
        </p>
      </div>



      {/* Submit Button */}
      <div className="pt-6">
        {formData.rating === 0 && (
          <p className="text-sm text-red-600 mb-3 text-center">
            {messages.reviews.pleaseSelectRating}
          </p>
        )}
        <button
          type="submit"
          disabled={isSubmitting || formData.rating === 0}
          className="w-full bg-primary-600 hover:bg-primary-700 disabled:bg-neutral-400 text-white font-bold py-4 px-6 rounded-lg transition-colors duration-200"
        >
          {isSubmitting ? messages.reviews.submittingReview : messages.reviews.submitReview}
        </button>
        <p className="text-xs text-neutral-500 mt-2 text-center">
          {messages.reviews.requiredFields}
        </p>
      </div>
    </form>
  );
}
