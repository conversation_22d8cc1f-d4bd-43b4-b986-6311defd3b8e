// Car inventory types for database operations

export interface Car {
  id: string;
  car_id: string; // Unique identifier (e.g., toyota_voxy_2012_TVXYA)
  
  // Basic Information
  make: string; // Toyota, Honda, etc.
  model: string; // <PERSON><PERSON><PERSON>, <PERSON>, Sienta, etc.
  year: number;
  title: string; // Display title (e.g., "Toyota Voxy 2012")
  
  // Pricing
  price: number; // Price in Yen
  original_price?: number; // For price reduction tracking
  currency: string; // JPY, USD, etc.
  
  // Technical Specifications
  mileage?: number; // In kilometers
  fuel_type?: string; // Gasoline, Hybrid, Electric, Diesel
  transmission?: string; // Automatic, Manual, CVT
  engine_size?: string; // 1.8L, 2.0L, etc.
  drive_type?: string; // FWD, AWD, RWD
  seats?: number; // Number of seats
  doors?: number; // Number of doors
  body_type?: string; // Sedan, SUV, Hatchback, Van, etc.
  
  // Condition & Features
  body_condition?: string; // Excellent, Good, Fair, Needs Work
  interior_condition?: string;
  exterior_color?: string;
  interior_color?: string;
  
  // Images & Media
  main_image?: string; // Primary display image
  images?: string[]; // Array of all image URLs
  image_folder?: string; // Folder path for images
  
  // Specifications Array (for flexible specs)
  specs?: string[]; // Array of specification strings
  features?: string[]; // Array of feature strings
  
  // Status & Availability
  status: string; // Available, Reserved, Sold, Pending
  stock_quantity: number;
  location: string;
  
  // SEO & Display
  slug?: string; // URL-friendly identifier
  description?: string;
  meta_title?: string;
  meta_description?: string;
  
  // Tracking & Analytics
  view_count: number;
  popularity_score: number;
  is_featured: boolean;
  is_recently_added: boolean;
  is_price_reduced: boolean;
  
  // Import & Management
  import_batch_id?: string; // For tracking bulk imports
  import_source?: string; // CSV, API, Manual, etc.
  import_notes?: string;
  
  // Timestamps
  added_date: string;
  updated_date: string;
  sold_date?: string;
  created_at: string;
  updated_at: string;
}

// For creating new cars (without auto-generated fields)
export interface CreateCarInput {
  car_id: string;
  make: string;
  model: string;
  year: number;
  title: string;
  price: number;
  original_price?: number;
  currency?: string;
  mileage?: number;
  fuel_type?: string;
  transmission?: string;
  engine_size?: string;
  drive_type?: string;
  seats?: number;
  doors?: number;
  body_type?: string;
  body_condition?: string;
  interior_condition?: string;
  exterior_color?: string;
  interior_color?: string;
  main_image?: string;
  images?: string[];
  image_folder?: string;
  specs?: string[];
  features?: string[];
  status?: string;
  stock_quantity?: number;
  location?: string;
  slug?: string;
  description?: string;
  meta_title?: string;
  meta_description?: string;
  is_featured?: boolean;
  import_batch_id?: string;
  import_source?: string;
  import_notes?: string;
}

// For updating existing cars
export interface UpdateCarInput {
  make?: string;
  model?: string;
  year?: number;
  title?: string;
  price?: number;
  original_price?: number;
  currency?: string;
  mileage?: number;
  fuel_type?: string;
  transmission?: string;
  engine_size?: string;
  drive_type?: string;
  seats?: number;
  doors?: number;
  body_type?: string;
  body_condition?: string;
  interior_condition?: string;
  exterior_color?: string;
  interior_color?: string;
  main_image?: string;
  images?: string[];
  image_folder?: string;
  specs?: string[];
  features?: string[];
  status?: string;
  stock_quantity?: number;
  location?: string;
  slug?: string;
  description?: string;
  meta_title?: string;
  meta_description?: string;
  is_featured?: boolean;
  is_recently_added?: boolean;
  is_price_reduced?: boolean;
  import_notes?: string;
}

// CSV Import types
export interface CSVImportRow {
  car_id: string;
  make: string;
  model: string;
  year: string | number;
  price: string | number;
  original_price?: string | number;
  mileage?: string | number;
  fuel_type?: string;
  transmission?: string;
  engine_size?: string;
  drive_type?: string;
  seats?: string | number;
  doors?: string | number;
  body_type?: string;
  body_condition?: string;
  interior_condition?: string;
  exterior_color?: string;
  interior_color?: string;
  main_image?: string;
  images?: string; // Comma-separated image URLs
  image_folder?: string;
  specs?: string; // Comma-separated specs
  features?: string; // Comma-separated features
  status?: string;
  stock_quantity?: string | number;
  location?: string;
  description?: string;
  is_featured?: string | boolean;
}

export interface CSVImport {
  id: string;
  batch_id: string;
  filename: string;
  total_rows: number;
  successful_imports: number;
  failed_imports: number;
  status: 'processing' | 'completed' | 'failed';
  error_log: string[];
  import_summary: Record<string, any>;
  imported_by: string;
  started_at: string;
  completed_at?: string;
  created_at: string;
}

export interface CSVImportResult {
  success: boolean;
  batch_id: string;
  total_rows: number;
  successful_imports: number;
  failed_imports: number;
  errors: string[];
  imported_cars: Car[];
}

// Search and filter types
export interface CarSearchFilters {
  make?: string;
  model?: string;
  year_min?: number;
  year_max?: number;
  price_min?: number;
  price_max?: number;
  mileage_max?: number;
  fuel_type?: string;
  transmission?: string;
  body_condition?: string;
  status?: string;
  is_featured?: boolean;
  is_recently_added?: boolean;
  search_query?: string;
}

export interface CarSearchResult {
  cars: Car[];
  total_count: number;
  page: number;
  per_page: number;
  total_pages: number;
  filters_applied: CarSearchFilters;
}

// Bulk operations
export interface BulkUpdateInput {
  car_ids: string[];
  updates: UpdateCarInput;
}

export interface BulkDeleteInput {
  car_ids: string[];
  reason?: string;
}

export interface BulkOperationResult {
  success: boolean;
  affected_count: number;
  errors: string[];
}
