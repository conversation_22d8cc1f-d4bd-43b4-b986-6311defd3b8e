'use client';

import { useState } from 'react';
import {
  Users,
  UserPlus,
  MessageSquare,
  Calendar,
  TrendingUp,
  DollarSign
} from 'lucide-react';
import { formatDateTime, formatCurrency } from '@/lib/dateUtils';
import AIStatusPanel from '../../../components/admin/AIStatusPanel';
import HallucinationMonitor from '../../../components/admin/HallucinationMonitor';

interface Customer {
  id: string;
  personalInfo: {
    name: string;
    email: string;
    phone?: string;
  };
  status: string;
  segment: string;
  loyaltyPoints: number;
  membershipTier: string;
  totalSpent: number;
  totalOrders: number;
  lastOrderDate?: string;
  createdAt: string;
}

interface Lead {
  id: string;
  source: string;
  status: string;
  priority: string;
  customerInfo: {
    name: string;
    email?: string;
    phone?: string;
  };
  inquiry: {
    message: string;
    productInterest?: string;
  };
  createdAt: string;
}

interface Interaction {
  id: string;
  type: string;
  direction: string;
  content: string;
  createdAt: string;
}

interface FollowUp {
  id: string;
  type: string;
  status: string;
  priority: string;
  title: string;
  scheduledDate: string;
}

export default function CRMDashboard() {
  const [authenticated, setAuthenticated] = useState(false);
  const [adminKey, setAdminKey] = useState('');
  const [activeTab, setActiveTab] = useState<'overview' | 'customers' | 'leads' | 'interactions' | 'followups'>('overview');
  const [loading, setLoading] = useState(false);

  // Data states
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [leads, setLeads] = useState<Lead[]>([]);
  const [interactions, setInteractions] = useState<Interaction[]>([]);
  const [followups, setFollowUps] = useState<FollowUp[]>([]);

  // Filter states (currently unused but kept for future functionality)

  const handleAuth = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await fetch('/api/admin/auth', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ password: adminKey }),
      });

      const data = await response.json();
      if (data.success) {
        setAuthenticated(true);
        await loadDashboardData(data.adminKey);
      } else {
        alert('Invalid admin password');
      }
    } catch (error) {
      console.error('Authentication error:', error);
      alert('Authentication failed');
    } finally {
      setLoading(false);
    }
  };

  const loadDashboardData = async (key: string) => {
    setLoading(true);
    try {
      // Load all CRM data
      const [customersRes, leadsRes, interactionsRes, followupsRes] = await Promise.all([
        fetch(`/api/customers?adminKey=${encodeURIComponent(key)}`),
        fetch(`/api/leads?adminKey=${encodeURIComponent(key)}`),
        fetch(`/api/interactions?adminKey=${encodeURIComponent(key)}`),
        fetch(`/api/followups?adminKey=${encodeURIComponent(key)}`)
      ]);

      const [customersData, leadsData, interactionsData, followupsData] = await Promise.all([
        customersRes.json(),
        leadsRes.json(),
        interactionsRes.json(),
        followupsRes.json()
      ]);

      if (customersData.success) setCustomers(customersData.customers || []);
      if (leadsData.success) setLeads(leadsData.leads || []);
      if (interactionsData.success) setInteractions(interactionsData.interactions || []);
      if (followupsData.success) setFollowUps(followupsData.followups || []);

    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStats = () => {
    const newLeads = leads.filter(l => l.status === 'new').length;
    const activeCustomers = customers.filter(c => c.status === 'active').length;
    const pendingFollowUps = followups.filter(f => f.status === 'pending').length;
    const totalRevenue = customers.reduce((sum, c) => sum + c.totalSpent, 0);

    return { newLeads, activeCustomers, pendingFollowUps, totalRevenue };
  };

  if (!authenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
          <h1 className="text-2xl font-bold text-center mb-6">CRM Admin Access</h1>
          <form onSubmit={handleAuth}>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Admin Password
              </label>
              <input
                type="password"
                value={adminKey}
                onChange={(e) => setAdminKey(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>
            <button
              type="submit"
              disabled={loading}
              className="w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:opacity-50"
            >
              {loading ? 'Authenticating...' : 'Access CRM'}
            </button>
          </form>
        </div>
      </div>
    );
  }

  const stats = getStats();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <h1 className="text-2xl font-bold text-gray-900">EBAM Motors CRM</h1>
            <button
              onClick={() => setAuthenticated(false)}
              className="text-gray-500 hover:text-gray-700"
            >
              Logout
            </button>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex space-x-8">
            {[
              { id: 'overview', label: 'Overview', icon: TrendingUp },
              { id: 'customers', label: 'Customers', icon: Users },
              { id: 'leads', label: 'Leads', icon: UserPlus },
              { id: 'interactions', label: 'Interactions', icon: MessageSquare },
              { id: 'followups', label: 'Follow-ups', icon: Calendar },
            ].map(({ id, label, icon: Icon }) => (
              <button
                key={id}
                onClick={() => setActiveTab(id as any)}
                className={`flex items-center px-3 py-4 text-sm font-medium border-b-2 ${
                  activeTab === id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                <Icon className="w-4 h-4 mr-2" />
                {label}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-white p-6 rounded-lg shadow">
                <div className="flex items-center">
                  <UserPlus className="w-8 h-8 text-blue-500" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">New Leads</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.newLeads}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg shadow">
                <div className="flex items-center">
                  <Users className="w-8 h-8 text-green-500" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Active Customers</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.activeCustomers}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg shadow">
                <div className="flex items-center">
                  <Calendar className="w-8 h-8 text-orange-500" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Pending Follow-ups</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.pendingFollowUps}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg shadow">
                <div className="flex items-center">
                  <DollarSign className="w-8 h-8 text-purple-500" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                    <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.totalRevenue)}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* AI Chatbot Status */}
            <AIStatusPanel />

            {/* AI Hallucination Monitor */}
            <HallucinationMonitor />

            {/* Recent Activity */}
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b">
                <h3 className="text-lg font-medium text-gray-900">Recent Activity</h3>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {interactions.slice(0, 5).map((interaction) => (
                    <div key={interaction.id} className="flex items-start space-x-3">
                      <MessageSquare className="w-5 h-5 text-gray-400 mt-0.5" />
                      <div className="flex-1">
                        <p className="text-sm text-gray-900">{interaction.content}</p>
                        <p className="text-xs text-gray-500">
                          {formatDateTime(interaction.createdAt)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Other tabs show placeholder for now */}
        {activeTab !== 'overview' && (
          <div className="bg-white rounded-lg shadow p-6">
            <p className="text-gray-500">
              {activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} management interface - Full implementation available in locale-based route: /en/admin/crm
            </p>
            <div className="mt-4">
              <a
                href="/en/admin/crm"
                className="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 inline-block"
              >
                Go to Full CRM Dashboard
              </a>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
