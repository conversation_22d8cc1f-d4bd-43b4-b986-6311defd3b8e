'use client';

import { usePathname } from 'next/navigation';
import Chatbot from './Chatbot';
import ErrorBoundary from '../ErrorBoundary';

interface ChatbotWrapperProps {
  locale: string;
}

export default function ChatbotWrapper({ locale }: ChatbotWrapperProps) {
  const pathname = usePathname();
  
  // Extract the current page from the pathname
  const getCurrentPage = (path: string): string => {
    // Remove locale prefix and leading/trailing slashes
    const cleanPath = path.replace(/^\/[a-z]{2}\//, '').replace(/^\/|\/$/g, '');
    
    // Map paths to page identifiers
    const pageMap: { [key: string]: string } = {
      '': 'home',
      'about': 'about',
      'services': 'services',
      'stock': 'stock',
      'inventory': 'stock', // Legacy support
      'how-it-works': 'how-it-works',
      'suppliers': 'suppliers',
      'buyers': 'buyers',
      'contact': 'contact'
    };
    
    return pageMap[cleanPath] || 'global';
  };

  const currentPage = getCurrentPage(pathname);

  return (
    <ErrorBoundary fallback={null}>
      <Chatbot locale={locale} currentPage={currentPage} />
    </ErrorBoundary>
  );
}
