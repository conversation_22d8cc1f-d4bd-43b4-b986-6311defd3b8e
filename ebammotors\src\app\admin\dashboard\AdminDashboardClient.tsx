'use client';

import { useState, useEffect } from 'react';
import { Shield, Activity, Database, Memory, AlertTriangle, CheckCircle, Clock } from 'lucide-react';

interface HealthData {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  uptime: number;
  issues: string[];
  stats: {
    requests: number;
    averageResponseTime: number;
    errorRate: number;
    slowRequests: number;
  };
  performance?: any;
  database?: {
    healthy: boolean;
    latency?: number;
    error?: string;
  };
  memory?: {
    rss: number;
    heapTotal: number;
    heapUsed: number;
    external: number;
  };
  errors?: any[];
}

function AdminDashboardClient() {
  const [healthData, setHealthData] = useState<HealthData | null>(null);
  const [loading, setLoading] = useState(true);
  const [authenticated, setAuthenticated] = useState(false);
  const [password, setPassword] = useState('');
  const [mounted, setMounted] = useState(false);

  const handleAuth = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const response = await fetch('/api/admin/auth', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ password }),
      });

      const data = await response.json();
      if (data.success) {
        // Ensure we're on the client side before accessing localStorage
        if (typeof window !== 'undefined') {
          localStorage.setItem('admin_token', data.token);
        }
        setAuthenticated(true);
        fetchHealthData();
      } else {
        alert('Invalid password');
      }
    } catch (error) {
      console.error('Auth error:', error);
      alert('Authentication failed');
    }
  };

  const fetchHealthData = async () => {
    try {
      // Ensure we're on the client side before accessing localStorage
      if (typeof window === 'undefined') {
        setLoading(false);
        return;
      }

      const token = localStorage.getItem('admin_token');
      const response = await fetch('/api/admin/health?detailed=true', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.status === 401) {
        setAuthenticated(false);
        localStorage.removeItem('admin_token');
        return;
      }

      const data = await response.json();
      if (data.success) {
        setHealthData(data.health);
      }
    } catch (error) {
      console.error('Error fetching health data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!mounted) return;
    
    // Ensure we're on the client side before accessing localStorage
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('admin_token');
      if (token) {
        setAuthenticated(true);
        fetchHealthData();
      } else {
        setLoading(false);
      }
    } else {
      setLoading(false);
    }
  }, [mounted]);

  useEffect(() => {
    if (authenticated && mounted) {
      const interval = setInterval(fetchHealthData, 30000); // Refresh every 30 seconds
      return () => clearInterval(interval);
    }
  }, [authenticated, mounted]);

  // Don't render anything until mounted
  if (!mounted) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  if (!authenticated) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
          <div className="flex items-center mb-6">
            <Shield className="w-8 h-8 text-blue-600 mr-3" />
            <h1 className="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
          </div>
          
          <form onSubmit={handleAuth}>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Admin Password
              </label>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>
            
            <button
              type="submit"
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              Login
            </button>
          </form>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600 bg-green-100';
      case 'degraded': return 'text-yellow-600 bg-yellow-100';
      case 'unhealthy': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return <CheckCircle className="w-5 h-5" />;
      case 'degraded': return <Clock className="w-5 h-5" />;
      case 'unhealthy': return <AlertTriangle className="w-5 h-5" />;
      default: return <Activity className="w-5 h-5" />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 p-6">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">EBAM Motors - Admin Dashboard</h1>
          <p className="text-gray-600">System health and performance monitoring</p>
        </div>

        {healthData && (
          <>
            {/* Status Overview */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className={`p-2 rounded-lg ${getStatusColor(healthData.status)}`}>
                    {getStatusIcon(healthData.status)}
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">System Status</p>
                    <p className="text-2xl font-bold text-gray-900 capitalize">{healthData.status}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="p-2 rounded-lg bg-blue-100 text-blue-600">
                    <Activity className="w-5 h-5" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Requests (5m)</p>
                    <p className="text-2xl font-bold text-gray-900">{healthData.stats.requests}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="p-2 rounded-lg bg-purple-100 text-purple-600">
                    <Clock className="w-5 h-5" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Avg Response</p>
                    <p className="text-2xl font-bold text-gray-900">{healthData.stats.averageResponseTime}ms</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className={`p-2 rounded-lg ${healthData.stats.errorRate > 5 ? 'bg-red-100 text-red-600' : 'bg-green-100 text-green-600'}`}>
                    <AlertTriangle className="w-5 h-5" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Error Rate</p>
                    <p className="text-2xl font-bold text-gray-900">{healthData.stats.errorRate}%</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Database & Memory */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center mb-4">
                  <Database className="w-5 h-5 text-blue-600 mr-2" />
                  <h3 className="text-lg font-semibold text-gray-900">Database Health</h3>
                </div>
                {healthData.database ? (
                  <div>
                    <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                      healthData.database.healthy ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {healthData.database.healthy ? 'Connected' : 'Disconnected'}
                    </div>
                    {healthData.database.latency && (
                      <p className="text-sm text-gray-600 mt-2">Latency: {healthData.database.latency}ms</p>
                    )}
                    {healthData.database.error && (
                      <p className="text-sm text-red-600 mt-2">{healthData.database.error}</p>
                    )}
                  </div>
                ) : (
                  <p className="text-gray-500">Database status unavailable</p>
                )}
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center mb-4">
                  <Memory className="w-5 h-5 text-purple-600 mr-2" />
                  <h3 className="text-lg font-semibold text-gray-900">Memory Usage</h3>
                </div>
                {healthData.memory ? (
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">RSS:</span>
                      <span className="text-sm font-medium">{healthData.memory.rss}MB</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Heap Used:</span>
                      <span className="text-sm font-medium">{healthData.memory.heapUsed}MB</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Heap Total:</span>
                      <span className="text-sm font-medium">{healthData.memory.heapTotal}MB</span>
                    </div>
                  </div>
                ) : (
                  <p className="text-gray-500">Memory data unavailable</p>
                )}
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}

export default AdminDashboardClient;
