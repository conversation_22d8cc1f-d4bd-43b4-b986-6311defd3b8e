'use client';

import { useState } from 'react';
import Link from 'next/link';
import { ArrowLeft, Heart, Share2, Eye, Calendar, Gauge, Fuel, Settings, MapPin, Shield, FileText, RotateCcw, GitCompare } from 'lucide-react';
import Navigation from '@/components/Navigation';
import VehicleImageGallery from './components/VehicleImageGallery';
import VehicleSpecifications from './components/VehicleSpecifications';
import VehicleHistory from './components/VehicleHistory';
import Vehicle360View from './components/Vehicle360View';
import ComparisonPanel from './components/ComparisonPanel';
import PurchaseModal from '@/components/Purchase/PurchaseModal';
import LeadCaptureForm from '@/components/Chatbot/LeadCaptureForm';

interface StockItem {
  id: number;
  category: string;
  title: string;
  price: string;
  location: string;
  status: string;
  image: string;
  images: string[];
  specs: string[];
  carId: string;
  year?: number;
  mileage?: number;
  fuelType?: string;
  transmission?: string;
  bodyCondition?: string;
}

interface VehicleDetailPageProps {
  vehicle: StockItem;
  locale: string;
}

export default function VehicleDetailPage({ vehicle, locale }: VehicleDetailPageProps) {
  const [activeTab, setActiveTab] = useState<'overview' | 'specs' | 'history' | '360view' | 'compare'>('overview');
  const [isFavorite, setIsFavorite] = useState(false);
  const [showComparisonPanel, setShowComparisonPanel] = useState(false);
  const [showPurchaseModal, setShowPurchaseModal] = useState(false);
  const [showQuoteModal, setShowQuoteModal] = useState(false);

  const tabs = [
    { id: 'overview', label: locale === 'en' ? 'Overview' : '概要', icon: Eye },
    { id: 'specs', label: locale === 'en' ? 'Specifications' : '仕様', icon: Settings },
    { id: 'history', label: locale === 'en' ? 'History & Reports' : '履歴・レポート', icon: FileText },
    { id: '360view', label: locale === 'en' ? '360° View' : '360°ビュー', icon: RotateCcw },
    { id: 'compare', label: locale === 'en' ? 'Compare' : '比較', icon: GitCompare },
  ];

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: vehicle.title,
          text: `Check out this ${vehicle.title} at EBAM Motors`,
          url: window.location.href,
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
      alert(locale === 'en' ? 'Link copied to clipboard!' : 'リンクがクリップボードにコピーされました！');
    }
  };

  return (
    <div className="min-h-screen bg-neutral-50">
      <Navigation />
      
      {/* Header */}
      <div className="bg-white border-b sticky top-16 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link
                href={`/${locale}/stock`}
                className="flex items-center space-x-2 text-neutral-600 hover:text-primary-600 transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
                <span>{locale === 'en' ? 'Back to Stock' : '在庫に戻る'}</span>
              </Link>
              <div className="hidden sm:block w-px h-6 bg-neutral-300"></div>
              <h1 className="text-xl font-bold text-neutral-800">{vehicle.title}</h1>
            </div>
            
            <div className="flex items-center space-x-3">
              <button
                onClick={() => setIsFavorite(!isFavorite)}
                className={`p-2 rounded-lg border transition-colors ${
                  isFavorite 
                    ? 'bg-red-50 border-red-200 text-red-600' 
                    : 'bg-white border-neutral-300 text-neutral-600 hover:text-red-600'
                }`}
              >
                <Heart className={`w-5 h-5 ${isFavorite ? 'fill-current' : ''}`} />
              </button>
              <button
                onClick={handleShare}
                className="p-2 rounded-lg border border-neutral-300 text-neutral-600 hover:text-primary-600 transition-colors"
              >
                <Share2 className="w-5 h-5" />
              </button>
              <button
                onClick={() => setShowComparisonPanel(true)}
                className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors flex items-center space-x-2"
              >
                <GitCompare className="w-4 h-4" />
                <span className="hidden sm:inline">{locale === 'en' ? 'Compare' : '比較'}</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          
          {/* Left Column - Images and Basic Info */}
          <div className="lg:col-span-2 space-y-6">
            <VehicleImageGallery vehicle={vehicle} locale={locale} />
            
            {/* Quick Info Cards */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-white rounded-lg p-4 border">
                <div className="flex items-center space-x-2 text-neutral-600 mb-1">
                  <Calendar className="w-4 h-4" />
                  <span className="text-sm">{locale === 'en' ? 'Year' : '年式'}</span>
                </div>
                <div className="text-lg font-semibold text-neutral-800">{vehicle.year || 'N/A'}</div>
              </div>
              
              <div className="bg-white rounded-lg p-4 border">
                <div className="flex items-center space-x-2 text-neutral-600 mb-1">
                  <Gauge className="w-4 h-4" />
                  <span className="text-sm">{locale === 'en' ? 'Mileage' : '走行距離'}</span>
                </div>
                <div className="text-lg font-semibold text-neutral-800">
                  {vehicle.mileage ? `${Math.floor(vehicle.mileage / 1000)}k km` : 'N/A'}
                </div>
              </div>
              
              <div className="bg-white rounded-lg p-4 border">
                <div className="flex items-center space-x-2 text-neutral-600 mb-1">
                  <Fuel className="w-4 h-4" />
                  <span className="text-sm">{locale === 'en' ? 'Fuel' : '燃料'}</span>
                </div>
                <div className="text-lg font-semibold text-neutral-800">{vehicle.fuelType || 'N/A'}</div>
              </div>
              
              <div className="bg-white rounded-lg p-4 border">
                <div className="flex items-center space-x-2 text-neutral-600 mb-1">
                  <Settings className="w-4 h-4" />
                  <span className="text-sm">{locale === 'en' ? 'Transmission' : 'トランスミッション'}</span>
                </div>
                <div className="text-lg font-semibold text-neutral-800">{vehicle.transmission || 'N/A'}</div>
              </div>
            </div>
          </div>

          {/* Right Column - Price and Actions */}
          <div className="space-y-6">
            {/* Price Card */}
            <div className="bg-white rounded-lg p-6 border shadow-sm">
              <div className="text-center space-y-4">
                <div>
                  <div className="text-3xl font-bold text-primary-600">{vehicle.price}</div>
                  <div className="text-sm text-neutral-600">{locale === 'en' ? 'FOB Price' : 'FOB価格'}</div>
                </div>
                
                <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                  vehicle.status === 'Available' 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-yellow-100 text-yellow-800'
                }`}>
                  <div className={`w-2 h-2 rounded-full mr-2 ${
                    vehicle.status === 'Available' ? 'bg-green-500' : 'bg-yellow-500'
                  }`}></div>
                  {vehicle.status}
                </div>

                <div className="flex items-center justify-center space-x-2 text-neutral-600">
                  <MapPin className="w-4 h-4" />
                  <span className="text-sm">{vehicle.location}</span>
                </div>

                <div className="space-y-3 pt-4">
                  <button
                    onClick={() => setShowPurchaseModal(true)}
                    className="w-full bg-primary-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-primary-700 transition-colors"
                  >
                    {locale === 'en' ? 'Purchase Now' : '今すぐ購入'}
                  </button>
                  <button
                    onClick={() => setShowQuoteModal(true)}
                    className="w-full border border-primary-600 text-primary-600 py-3 px-4 rounded-lg font-semibold hover:bg-primary-50 transition-colors"
                  >
                    {locale === 'en' ? 'Request Quote' : '見積もり依頼'}
                  </button>
                </div>
              </div>
            </div>

            {/* Trust Indicators */}
            <div className="bg-white rounded-lg p-6 border">
              <h3 className="font-semibold text-neutral-800 mb-4 flex items-center space-x-2">
                <Shield className="w-5 h-5 text-primary-600" />
                <span>{locale === 'en' ? 'Trust & Safety' : '信頼・安全'}</span>
              </h3>
              <div className="space-y-3 text-sm">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>{locale === 'en' ? 'Verified Seller' : '認証済み販売者'}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>{locale === 'en' ? 'Inspection Available' : '検査利用可能'}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>{locale === 'en' ? 'Secure Payment' : '安全な支払い'}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs Navigation */}
        <div className="mt-12">
          <div className="border-b border-neutral-200">
            <nav className="flex space-x-8 overflow-x-auto">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors ${
                      activeTab === tab.id
                        ? 'border-primary-600 text-primary-600'
                        : 'border-transparent text-neutral-500 hover:text-neutral-700 hover:border-neutral-300'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span>{tab.label}</span>
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="mt-8">
            {activeTab === 'overview' && (
              <div className="bg-white rounded-lg p-6 border">
                <h3 className="text-lg font-semibold text-neutral-800 mb-4">
                  {locale === 'en' ? 'Vehicle Overview' : '車両概要'}
                </h3>
                <div className="prose max-w-none">
                  <p className="text-neutral-600">
                    {locale === 'en' 
                      ? `This ${vehicle.title} is in ${vehicle.bodyCondition?.toLowerCase()} condition and ready for export. The vehicle has been thoroughly inspected and comes with all necessary documentation for international shipping.`
                      : `この${vehicle.title}は${vehicle.bodyCondition}の状態で、輸出準備が整っています。車両は徹底的に検査され、国際輸送に必要なすべての書類が揃っています。`
                    }
                  </p>
                </div>
              </div>
            )}
            
            {activeTab === 'specs' && <VehicleSpecifications vehicle={vehicle} locale={locale} />}
            {activeTab === 'history' && <VehicleHistory vehicle={vehicle} locale={locale} />}
            {activeTab === '360view' && <Vehicle360View vehicle={vehicle} locale={locale} />}
            {activeTab === 'compare' && <ComparisonPanel vehicle={vehicle} locale={locale} />}
          </div>
        </div>
      </div>

      {/* Comparison Panel Modal */}
      {showComparisonPanel && (
        <ComparisonPanel
          vehicle={vehicle}
          locale={locale}
          onClose={() => setShowComparisonPanel(false)}
          isModal={true}
        />
      )}

      {/* Purchase Modal */}
      {showPurchaseModal && (
        <PurchaseModal
          isOpen={showPurchaseModal}
          onClose={() => setShowPurchaseModal(false)}
          vehicle={vehicle}
          locale={locale}
        />
      )}

      {/* Quote Request Modal */}
      {showQuoteModal && (
        <LeadCaptureForm
          locale={locale}
          isOpen={showQuoteModal}
          onClose={() => setShowQuoteModal(false)}
          productInterest={`${vehicle.title} - ${vehicle.price}`}
        />
      )}


    </div>
  );
}
