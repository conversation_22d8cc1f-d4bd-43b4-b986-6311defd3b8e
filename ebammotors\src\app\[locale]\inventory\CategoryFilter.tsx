'use client';

import { useState } from 'react';

interface CategoryFilterProps {
  locale: string;
  onCategoryChange: (category: string) => void;
}

export default function CategoryFilter({ locale, onCategoryChange }: CategoryFilterProps) {
  const [activeCategory, setActiveCategory] = useState('all');

  const categories = [
    { id: 'all', label: locale === 'en' ? 'All Items' : '全ての商品' },
    { id: 'cars', label: locale === 'en' ? 'Cars' : '車' },
    { id: 'electronics', label: locale === 'en' ? 'Electronics' : '電子機器' },
    { id: 'furniture', label: locale === 'en' ? 'Furniture' : '家具' }
  ];

  const handleCategoryClick = (categoryId: string) => {
    setActiveCategory(categoryId);
    onCategoryChange(categoryId);
  };

  return (
    <div className="flex flex-wrap gap-4 justify-center mb-8">
      {categories.map((category) => (
        <button
          key={category.id}
          onClick={() => handleCategoryClick(category.id)}
          className={`px-6 py-3 rounded-lg font-semibold transition-colors duration-200 ${
            activeCategory === category.id
              ? 'bg-primary-600 text-white'
              : 'bg-white text-neutral-700 border border-neutral-300 hover:border-primary-500 hover:text-primary-600'
          }`}
        >
          {category.label}
        </button>
      ))}
    </div>
  );
}
