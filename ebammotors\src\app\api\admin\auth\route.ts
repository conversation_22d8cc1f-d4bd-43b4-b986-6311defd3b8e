import { NextRequest, NextResponse } from 'next/server';
import { authenticateAdmin, checkAuthRateLimit, resetAuthRateLimit } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    const { password } = await request.json();

    // Get client IP for rate limiting
    const clientIP = request.headers.get('x-forwarded-for') ||
                    request.headers.get('x-real-ip') ||
                    'unknown';

    // Check rate limiting
    const rateLimit = checkAuthRateLimit(clientIP);
    if (!rateLimit.allowed) {
      return NextResponse.json(
        {
          success: false,
          message: `Too many authentication attempts. Please try again in ${Math.ceil((rateLimit.lockoutTime || 0) / 60000)} minutes.`,
          lockoutTime: rateLimit.lockoutTime
        },
        { status: 429 }
      );
    }

    // Validate input
    if (!password || typeof password !== 'string') {
      return NextResponse.json(
        { success: false, message: 'Password is required' },
        { status: 400 }
      );
    }

    // Authenticate admin
    const authResult = await authenticateAdmin(password);

    if (authResult.success) {
      // Reset rate limit on successful authentication
      resetAuthRateLimit(clientIP);

      // Set secure HTTP-only cookie for session
      const response = NextResponse.json({
        success: true,
        message: authResult.message,
        token: authResult.token,
        expiresIn: '24h'
      });

      // Set secure session cookie
      response.cookies.set('admin_session', authResult.sessionId!, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 24 * 60 * 60, // 24 hours
        path: '/'
      });

      return response;
    } else {
      return NextResponse.json(
        {
          success: false,
          message: authResult.message,
          remainingAttempts: rateLimit.remainingAttempts - 1
        },
        { status: 401 }
      );
    }
  } catch (error) {
    console.error('Error in admin authentication:', error);
    return NextResponse.json(
      { success: false, message: 'Authentication failed' },
      { status: 500 }
    );
  }
}
