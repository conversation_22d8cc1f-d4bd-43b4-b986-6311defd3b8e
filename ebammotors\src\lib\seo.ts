import { Metadata } from 'next';

// Base SEO configuration
const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || 'https://ebammotors.com';
const SITE_NAME = 'EBAM Motors';
const DEFAULT_DESCRIPTION = 'Premium Japanese used cars for Ghana and Africa. Quality vehicles, reliable shipping, competitive prices. Toyota, Honda, Nissan and more.';

// Default metadata
export const defaultMetadata: Metadata = {
  title: {
    default: `${SITE_NAME} - Premium Japanese Used Cars for Ghana & Africa`,
    template: `%s | ${SITE_NAME}`
  },
  description: DEFAULT_DESCRIPTION,
  keywords: [
    'Japanese used cars',
    'Ghana car import',
    'Africa car shipping',
    'Toyota Ghana',
    'Honda Ghana',
    'Nissan Ghana',
    'used cars Japan',
    'car export Japan',
    'reliable cars Ghana',
    'affordable cars Africa'
  ],
  authors: [{ name: SITE_NAME }],
  creator: SITE_NAME,
  publisher: SITE_NAME,
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(BASE_URL),
  alternates: {
    canonical: BASE_URL,
    languages: {
      'en': '/en',
      'ja': '/ja',
    },
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: BASE_URL,
    siteName: SITE_NAME,
    title: `${SITE_NAME} - Premium Japanese Used Cars for Ghana & Africa`,
    description: DEFAULT_DESCRIPTION,
    images: [
      {
        url: '/images/og-image.jpg',
        width: 1200,
        height: 630,
        alt: `${SITE_NAME} - Premium Japanese Used Cars`,
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: `${SITE_NAME} - Premium Japanese Used Cars for Ghana & Africa`,
    description: DEFAULT_DESCRIPTION,
    images: ['/images/og-image.jpg'],
    creator: '@ebammotors',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
    yandex: process.env.YANDEX_VERIFICATION,
    yahoo: process.env.YAHOO_VERIFICATION,
  },
};

// Generate page-specific metadata
export function generatePageMetadata({
  title,
  description,
  keywords = [],
  image,
  url,
  locale = 'en',
  type = 'website',
  noIndex = false,
}: {
  title: string;
  description?: string;
  keywords?: string[];
  image?: string;
  url?: string;
  locale?: string;
  type?: 'website' | 'article' | 'product';
  noIndex?: boolean;
}): Metadata {
  const fullTitle = `${title} | ${SITE_NAME}`;
  const pageDescription = description || DEFAULT_DESCRIPTION;
  const pageUrl = url ? `${BASE_URL}${url}` : BASE_URL;
  const pageImage = image || '/images/og-image.jpg';

  return {
    title: fullTitle,
    description: pageDescription,
    keywords: [...defaultMetadata.keywords as string[], ...keywords],
    alternates: {
      canonical: pageUrl,
      languages: {
        'en': url ? `/en${url}` : '/en',
        'ja': url ? `/ja${url}` : '/ja',
      },
    },
    openGraph: {
      type,
      locale: locale === 'ja' ? 'ja_JP' : 'en_US',
      url: pageUrl,
      siteName: SITE_NAME,
      title: fullTitle,
      description: pageDescription,
      images: [
        {
          url: pageImage,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: fullTitle,
      description: pageDescription,
      images: [pageImage],
    },
    robots: noIndex ? {
      index: false,
      follow: false,
    } : {
      index: true,
      follow: true,
    },
  };
}

// Generate car listing metadata
export function generateCarMetadata({
  make,
  model,
  year,
  price,
  carId,
  locale = 'en',
}: {
  make: string;
  model: string;
  year: number;
  price: string;
  carId: string;
  locale?: string;
}): Metadata {
  const title = `${year} ${make} ${model} - ${price}`;
  const description = `Premium ${year} ${make} ${model} available for export to Ghana and Africa. Price: ${price}. Quality Japanese used car with reliable shipping and competitive pricing.`;
  const keywords = [
    `${make} ${model}`,
    `${year} ${make}`,
    `${make} Ghana`,
    `${model} Ghana`,
    `${year} car`,
    'Japanese used car',
    'car export Ghana',
    'Africa car import',
  ];

  return generatePageMetadata({
    title,
    description,
    keywords,
    url: `/stock/${carId}`,
    locale,
    type: 'product',
  });
}

// Generate structured data for cars
export function generateCarStructuredData({
  make,
  model,
  year,
  price,
  carId,
  mileage,
  fuelType,
  transmission,
  images = [],
}: {
  make: string;
  model: string;
  year: number;
  price: string;
  carId: string;
  mileage?: number;
  fuelType?: string;
  transmission?: string;
  images?: string[];
}) {
  const priceValue = parseInt(price.replace(/[¥,]/g, '')) || 0;
  
  return {
    '@context': 'https://schema.org',
    '@type': 'Car',
    name: `${year} ${make} ${model}`,
    brand: {
      '@type': 'Brand',
      name: make,
    },
    model: model,
    vehicleModelDate: year.toString(),
    mileageFromOdometer: mileage ? {
      '@type': 'QuantitativeValue',
      value: mileage,
      unitCode: 'KMT',
    } : undefined,
    fuelType: fuelType,
    vehicleTransmission: transmission,
    offers: {
      '@type': 'Offer',
      price: priceValue,
      priceCurrency: 'JPY',
      availability: 'https://schema.org/InStock',
      seller: {
        '@type': 'Organization',
        name: SITE_NAME,
        url: BASE_URL,
      },
    },
    image: images.map(img => `${BASE_URL}${img}`),
    url: `${BASE_URL}/stock/${carId}`,
    identifier: carId,
  };
}

// Generate organization structured data
export function generateOrganizationStructuredData() {
  return {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: SITE_NAME,
    url: BASE_URL,
    logo: `${BASE_URL}/images/logo.png`,
    description: DEFAULT_DESCRIPTION,
    address: [
      {
        '@type': 'PostalAddress',
        addressCountry: 'JP',
        addressLocality: 'Tokyo',
        addressRegion: 'Tokyo',
        streetAddress: 'Japan Office',
      },
      {
        '@type': 'PostalAddress',
        addressCountry: 'GH',
        addressLocality: 'Kumasi',
        addressRegion: 'Ashanti',
        streetAddress: 'Ghana Office',
      },
    ],
    contactPoint: [
      {
        '@type': 'ContactPoint',
        telephone: '+81-80-6985-2864',
        contactType: 'customer service',
        areaServed: 'JP',
        availableLanguage: ['Japanese', 'English'],
      },
      {
        '@type': 'ContactPoint',
        telephone: '+233-24-537-5692',
        contactType: 'customer service',
        areaServed: 'GH',
        availableLanguage: ['English'],
      },
    ],
    sameAs: [
      'https://whatsapp.com/channel/0029Vb5sDlFDTkK191T6Ut1G',
    ],
  };
}

// Generate breadcrumb structured data
export function generateBreadcrumbStructuredData(items: { name: string; url: string }[]) {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: items.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: `${BASE_URL}${item.url}`,
    })),
  };
}

// SEO-friendly URL generation
export function generateSEOUrl(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim()
    .substring(0, 100); // Limit length
}

// Generate meta tags for head
export function generateMetaTags(metadata: Metadata): string {
  const tags: string[] = [];
  
  if (metadata.title) {
    tags.push(`<title>${typeof metadata.title === 'string' ? metadata.title : metadata.title.default}</title>`);
  }
  
  if (metadata.description) {
    tags.push(`<meta name="description" content="${metadata.description}" />`);
  }
  
  if (metadata.keywords && Array.isArray(metadata.keywords)) {
    tags.push(`<meta name="keywords" content="${metadata.keywords.join(', ')}" />`);
  }
  
  return tags.join('\n');
}
