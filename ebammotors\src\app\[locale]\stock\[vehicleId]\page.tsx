import { notFound } from 'next/navigation';
import { generateStockFromFileSystem } from '../utils/stockGenerator';
import VehicleDetailPage from './VehicleDetailPage';

interface VehiclePageProps {
  params: Promise<{
    locale: string;
    vehicleId: string;
  }>;
}

export default async function VehiclePage({ params }: VehiclePageProps) {
  const { locale, vehicleId } = await params;
  
  // Get all stock items
  const stockItems = await generateStockFromFileSystem();
  
  // Find the specific vehicle
  const vehicle = stockItems.find(item => item.carId === vehicleId);
  
  if (!vehicle) {
    notFound();
  }

  return <VehicleDetailPage vehicle={vehicle} locale={locale} />;
}

// Generate static params for all vehicles
export async function generateStaticParams() {
  const stockItems = await generateStockFromFileSystem();
  
  return stockItems
    .filter(item => item.category === 'cars')
    .flatMap(item => [
      { locale: 'en', vehicleId: item.carId },
      { locale: 'ja', vehicleId: item.carId }
    ]);
}
