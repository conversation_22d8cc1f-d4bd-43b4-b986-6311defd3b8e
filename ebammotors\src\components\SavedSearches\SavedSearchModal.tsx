'use client';

import { useState } from 'react';
import { X, Save, Search, Trash2, Clock, Filter } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { FilterOptions } from '@/app/[locale]/stock/FilterAndSearch';

interface SavedSearchModalProps {
  isOpen: boolean;
  onClose: () => void;
  locale: string;
  currentFilters?: FilterOptions;
  onApplySearch?: (filters: FilterOptions) => void;
}

export default function SavedSearchModal({ 
  isOpen, 
  onClose, 
  locale, 
  currentFilters,
  onApplySearch 
}: SavedSearchModalProps) {
  const { user, savedSearches, saveSearch, removeSavedSearch } = useAuth();
  const [searchName, setSearchName] = useState('');
  const [showSaveForm, setShowSaveForm] = useState(false);

  if (!isOpen || !user) return null;

  const handleSaveCurrentSearch = () => {
    if (!currentFilters || !searchName.trim()) return;
    
    saveSearch(searchName.trim(), currentFilters);
    setSearchName('');
    setShowSaveForm(false);
    
    // Show success message
    alert(locale === 'en' ? 'Search saved successfully!' : '検索が正常に保存されました！');
  };

  const handleApplySearch = (search: any) => {
    if (onApplySearch) {
      onApplySearch(search.filters);
      onClose();
    }
  };

  const handleDeleteSearch = (searchId: string) => {
    if (confirm(locale === 'en' ? 'Are you sure you want to delete this search?' : 'この検索を削除してもよろしいですか？')) {
      removeSavedSearch(searchId);
    }
  };

  const getFilterSummary = (filters: FilterOptions) => {
    const summary = [];
    
    if (filters.category !== 'all') {
      summary.push(`${locale === 'en' ? 'Category' : 'カテゴリー'}: ${filters.category}`);
    }
    
    if (filters.searchTerm) {
      summary.push(`${locale === 'en' ? 'Search' : '検索'}: "${filters.searchTerm}"`);
    }
    
    if (filters.priceRange[0] !== 0 || filters.priceRange[1] !== 1000000) {
      summary.push(`${locale === 'en' ? 'Price' : '価格'}: ¥${filters.priceRange[0].toLocaleString()} - ¥${filters.priceRange[1].toLocaleString()}`);
    }
    
    if (filters.yearRange[0] !== 2000 || filters.yearRange[1] !== 2025) {
      summary.push(`${locale === 'en' ? 'Year' : '年式'}: ${filters.yearRange[0]} - ${filters.yearRange[1]}`);
    }
    
    if (filters.fuelTypes.length > 0) {
      summary.push(`${locale === 'en' ? 'Fuel' : '燃料'}: ${filters.fuelTypes.join(', ')}`);
    }
    
    if (filters.transmissionTypes.length > 0) {
      summary.push(`${locale === 'en' ? 'Transmission' : 'トランスミッション'}: ${filters.transmissionTypes.join(', ')}`);
    }
    
    return summary.length > 0 ? summary.join(' • ') : (locale === 'en' ? 'No filters applied' : 'フィルターが適用されていません');
  };

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-bold text-neutral-800 flex items-center space-x-2">
            <Search className="w-5 h-5" />
            <span>{locale === 'en' ? 'Saved Searches' : '保存済み検索'}</span>
          </h2>
          <button
            onClick={onClose}
            className="text-neutral-600 hover:text-neutral-800 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {/* Save Current Search */}
          {currentFilters && (
            <div className="mb-6 p-4 bg-primary-50 rounded-lg border border-primary-200">
              <h3 className="font-semibold text-primary-800 mb-2 flex items-center space-x-2">
                <Save className="w-4 h-4" />
                <span>{locale === 'en' ? 'Save Current Search' : '現在の検索を保存'}</span>
              </h3>
              
              <div className="mb-3">
                <p className="text-sm text-primary-700 mb-2">
                  {locale === 'en' ? 'Current filters:' : '現在のフィルター:'}
                </p>
                <p className="text-xs text-primary-600 bg-white p-2 rounded border">
                  {getFilterSummary(currentFilters)}
                </p>
              </div>

              {!showSaveForm ? (
                <button
                  onClick={() => setShowSaveForm(true)}
                  className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors text-sm"
                >
                  {locale === 'en' ? 'Save This Search' : 'この検索を保存'}
                </button>
              ) : (
                <div className="space-y-3">
                  <input
                    type="text"
                    value={searchName}
                    onChange={(e) => setSearchName(e.target.value)}
                    placeholder={locale === 'en' ? 'Enter search name...' : '検索名を入力...'}
                    className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm"
                    autoFocus
                  />
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={handleSaveCurrentSearch}
                      disabled={!searchName.trim()}
                      className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {locale === 'en' ? 'Save' : '保存'}
                    </button>
                    <button
                      onClick={() => {
                        setShowSaveForm(false);
                        setSearchName('');
                      }}
                      className="text-neutral-600 hover:text-neutral-800 transition-colors text-sm"
                    >
                      {locale === 'en' ? 'Cancel' : 'キャンセル'}
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Saved Searches List */}
          <div>
            <h3 className="font-semibold text-neutral-800 mb-4">
              {locale === 'en' ? 'Your Saved Searches' : '保存済み検索'}
              <span className="ml-2 text-sm font-normal text-neutral-600">
                ({savedSearches.length})
              </span>
            </h3>

            {savedSearches.length === 0 ? (
              <div className="text-center py-8">
                <Search className="w-12 h-12 text-neutral-300 mx-auto mb-4" />
                <p className="text-neutral-600 mb-2">
                  {locale === 'en' ? 'No saved searches yet' : '保存済み検索はまだありません'}
                </p>
                <p className="text-sm text-neutral-500">
                  {locale === 'en' 
                    ? 'Apply some filters and save your search for quick access later'
                    : 'フィルターを適用して検索を保存すると、後で簡単にアクセスできます'
                  }
                </p>
              </div>
            ) : (
              <div className="space-y-3">
                {savedSearches.map((search) => (
                  <div key={search.id} className="border rounded-lg p-4 hover:bg-neutral-50 transition-colors">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h4 className="font-medium text-neutral-800 mb-1">{search.name}</h4>
                        <p className="text-sm text-neutral-600 mb-2">
                          {getFilterSummary(search.filters)}
                        </p>
                        <div className="flex items-center space-x-4 text-xs text-neutral-500">
                          <span className="flex items-center space-x-1">
                            <Clock className="w-3 h-3" />
                            <span>
                              {locale === 'en' ? 'Created' : '作成日'}: {new Date(search.createdAt).toLocaleDateString()}
                            </span>
                          </span>
                          <span>
                            {locale === 'en' ? 'Last used' : '最終使用'}: {new Date(search.lastUsed).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2 ml-4">
                        <button
                          onClick={() => handleApplySearch(search)}
                          className="bg-primary-600 text-white px-3 py-1 rounded text-sm hover:bg-primary-700 transition-colors"
                        >
                          {locale === 'en' ? 'Apply' : '適用'}
                        </button>
                        <button
                          onClick={() => handleDeleteSearch(search.id)}
                          className="text-red-600 hover:text-red-700 transition-colors p-1"
                          title={locale === 'en' ? 'Delete search' : '検索を削除'}
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="border-t p-4 bg-neutral-50">
          <div className="flex items-center justify-between text-sm text-neutral-600">
            <div className="flex items-center space-x-2">
              <Filter className="w-4 h-4" />
              <span>
                {locale === 'en' 
                  ? 'Tip: Save complex searches to find vehicles faster'
                  : 'ヒント: 複雑な検索を保存して車両をより早く見つけましょう'
                }
              </span>
            </div>
            <button
              onClick={onClose}
              className="text-primary-600 hover:text-primary-700 transition-colors font-medium"
            >
              {locale === 'en' ? 'Close' : '閉じる'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
