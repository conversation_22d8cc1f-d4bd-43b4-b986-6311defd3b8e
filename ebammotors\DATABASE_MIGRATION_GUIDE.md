# Database Migration Guide - EBAM Motors Reviews

## 🎯 Why Upgrade to Database?

While the current JSON file storage works great for development and small-scale production, a database offers:

✅ **Better Performance** - Faster queries and indexing  
✅ **Scalability** - Handle thousands of reviews  
✅ **Reliability** - Automatic backups and recovery  
✅ **Concurrent Access** - Multiple users can access simultaneously  
✅ **Advanced Features** - Search, filtering, analytics  
✅ **Serverless Compatibility** - Works with Vercel, Netlify, etc.  

## 🚀 Recommended Database Solutions

### Option 1: Vercel Postgres (Recommended)
**Best for**: Vercel deployments, serverless applications

**Pros**:
- Free tier: 60 hours compute time/month
- Automatic scaling and backups
- Zero configuration with Vercel
- Built-in connection pooling

**Setup**:
1. Go to [Vercel Dashboard](https://vercel.com/dashboard)
2. Create new project or select existing
3. Go to Storage → Create Database → Postgres
4. Copy connection string to environment variables

### Option 2: Supabase (Great Alternative)
**Best for**: Real-time features, authentication integration

**Pros**:
- Free tier: 500MB database, 50MB file storage
- Real-time subscriptions
- Built-in authentication
- REST and GraphQL APIs

**Setup**:
1. Go to [Supabase](https://supabase.com)
2. Create new project
3. Get database URL from Settings → Database
4. Use built-in table editor

### Option 3: PlanetScale (MySQL)
**Best for**: MySQL preference, branching workflows

**Pros**:
- Free tier: 1 database, 1GB storage
- Database branching (like Git)
- Automatic scaling
- No connection limits

## 📋 Migration Steps

### Step 1: Choose Your Database
Pick one of the options above and create your database.

### Step 2: Install Database Dependencies
```bash
# For PostgreSQL (Vercel Postgres, Supabase)
npm install @vercel/postgres
# OR
npm install pg @types/pg

# For MySQL (PlanetScale)
npm install @planetscale/database

# For ORM (optional but recommended)
npm install prisma @prisma/client
```

### Step 3: Create Database Schema

#### Reviews Table (Existing)
```sql
-- PostgreSQL/Supabase Schema for Reviews
CREATE TABLE reviews (
  id VARCHAR(255) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  location VARCHAR(255) NOT NULL,
  email VARCHAR(255) NOT NULL,
  rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
  title VARCHAR(500),
  review TEXT NOT NULL,
  vehicle_purchased VARCHAR(255),
  purchase_date VARCHAR(50),
  locale VARCHAR(10) NOT NULL DEFAULT 'en',
  submitted_at TIMESTAMP WITH TIME ZONE NOT NULL,
  status VARCHAR(20) NOT NULL DEFAULT 'pending',
  images TEXT[], -- Array of image URLs
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_reviews_status ON reviews(status);
CREATE INDEX idx_reviews_submitted_at ON reviews(submitted_at DESC);
CREATE INDEX idx_reviews_rating ON reviews(rating);
```

#### Car Inventory Table (New - For Bulk CSV Import System)
```sql
-- PostgreSQL/Supabase Schema for Car Inventory
CREATE TABLE cars (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  car_id VARCHAR(255) UNIQUE NOT NULL, -- Unique identifier (e.g., toyota_voxy_2012_TVXYA)

  -- Basic Information
  make VARCHAR(100) NOT NULL, -- Toyota, Honda, etc.
  model VARCHAR(100) NOT NULL, -- Voxy, Noah, Sienta, etc.
  year INTEGER NOT NULL CHECK (year >= 1990 AND year <= 2030),
  title VARCHAR(255) NOT NULL, -- Display title (e.g., "Toyota Voxy 2012")

  -- Pricing
  price INTEGER NOT NULL CHECK (price > 0), -- Price in Yen
  original_price INTEGER, -- For price reduction tracking
  currency VARCHAR(3) DEFAULT 'JPY',

  -- Technical Specifications
  mileage INTEGER CHECK (mileage >= 0), -- In kilometers
  fuel_type VARCHAR(50), -- Gasoline, Hybrid, Electric, Diesel
  transmission VARCHAR(50), -- Automatic, Manual, CVT
  engine_size VARCHAR(50), -- 1.8L, 2.0L, etc.
  drive_type VARCHAR(50), -- FWD, AWD, RWD
  seats INTEGER CHECK (seats > 0 AND seats <= 50), -- Number of seats
  doors INTEGER CHECK (doors > 0 AND doors <= 10), -- Number of doors
  body_type VARCHAR(50), -- Sedan, SUV, Hatchback, Van, etc.

  -- Condition & Features
  body_condition VARCHAR(50) DEFAULT 'Good', -- Excellent, Good, Fair, Needs Work
  interior_condition VARCHAR(50) DEFAULT 'Good',
  exterior_color VARCHAR(50),
  interior_color VARCHAR(50),

  -- Images & Media
  main_image VARCHAR(500), -- Primary display image
  images TEXT[], -- Array of all image URLs
  image_folder VARCHAR(255), -- Folder path for images

  -- Specifications Array (for flexible specs)
  specs TEXT[], -- Array of specification strings
  features TEXT[], -- Array of feature strings

  -- Status & Availability
  status VARCHAR(50) DEFAULT 'Available', -- Available, Reserved, Sold, Pending
  stock_quantity INTEGER DEFAULT 1 CHECK (stock_quantity >= 0),
  location VARCHAR(100) DEFAULT 'Japan',

  -- SEO & Display
  slug VARCHAR(255) UNIQUE, -- URL-friendly identifier
  description TEXT,
  meta_title VARCHAR(255),
  meta_description TEXT,

  -- Tracking & Analytics
  view_count INTEGER DEFAULT 0,
  popularity_score INTEGER DEFAULT 0,
  is_featured BOOLEAN DEFAULT FALSE,
  is_recently_added BOOLEAN DEFAULT TRUE,
  is_price_reduced BOOLEAN DEFAULT FALSE,

  -- Import & Management
  import_batch_id VARCHAR(255), -- For tracking bulk imports
  import_source VARCHAR(100), -- CSV, API, Manual, etc.
  import_notes TEXT,

  -- Timestamps
  added_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  sold_date TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_cars_make_model ON cars(make, model);
CREATE INDEX idx_cars_year ON cars(year);
CREATE INDEX idx_cars_price ON cars(price);
CREATE INDEX idx_cars_status ON cars(status);
CREATE INDEX idx_cars_added_date ON cars(added_date DESC);
CREATE INDEX idx_cars_mileage ON cars(mileage);
CREATE INDEX idx_cars_fuel_type ON cars(fuel_type);
CREATE INDEX idx_cars_transmission ON cars(transmission);
CREATE INDEX idx_cars_body_condition ON cars(body_condition);
CREATE INDEX idx_cars_is_featured ON cars(is_featured);
CREATE INDEX idx_cars_is_recently_added ON cars(is_recently_added);
CREATE INDEX idx_cars_slug ON cars(slug);
CREATE INDEX idx_cars_car_id ON cars(car_id);

-- Full-text search index for car search functionality
CREATE INDEX idx_cars_search ON cars USING gin(
  to_tsvector('english',
    COALESCE(title, '') || ' ' ||
    COALESCE(make, '') || ' ' ||
    COALESCE(model, '') || ' ' ||
    COALESCE(description, '')
  )
);
```

#### CSV Import Tracking Table
```sql
-- Track bulk CSV import operations
CREATE TABLE csv_imports (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  batch_id VARCHAR(255) UNIQUE NOT NULL,
  filename VARCHAR(255) NOT NULL,
  total_rows INTEGER NOT NULL,
  successful_imports INTEGER DEFAULT 0,
  failed_imports INTEGER DEFAULT 0,
  status VARCHAR(50) DEFAULT 'processing', -- processing, completed, failed
  error_log TEXT[], -- Array of error messages
  import_summary JSONB, -- Detailed import statistics
  imported_by VARCHAR(255), -- Admin user identifier
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_csv_imports_batch_id ON csv_imports(batch_id);
CREATE INDEX idx_csv_imports_status ON csv_imports(status);
CREATE INDEX idx_csv_imports_started_at ON csv_imports(started_at DESC);
```

### Step 4: Update Environment Variables
Add to your `.env.local`:
```bash
# Database Configuration
DATABASE_URL="your-database-connection-string"

# For Vercel Postgres
POSTGRES_URL="postgres://..."
POSTGRES_PRISMA_URL="postgres://..."
POSTGRES_URL_NON_POOLING="postgres://..."

# For Supabase
SUPABASE_URL="https://your-project.supabase.co"
SUPABASE_ANON_KEY="your-anon-key"

# For PlanetScale
DATABASE_HOST="your-host"
DATABASE_USERNAME="your-username"
DATABASE_PASSWORD="your-password"
```

### Step 5: Create Database Utility Functions
Create `src/lib/database.ts`:
```typescript
// Example for Vercel Postgres
import { sql } from '@vercel/postgres';
import { Review } from './reviewStorage';

export async function getAllReviewsFromDB(): Promise<Review[]> {
  const { rows } = await sql`
    SELECT * FROM reviews 
    ORDER BY submitted_at DESC
  `;
  return rows.map(row => ({
    id: row.id,
    name: row.name,
    location: row.location,
    email: row.email,
    rating: row.rating,
    title: row.title,
    review: row.review,
    vehiclePurchased: row.vehicle_purchased,
    purchaseDate: row.purchase_date,
    locale: row.locale,
    submittedAt: row.submitted_at,
    status: row.status,
    images: row.images || []
  }));
}

export async function addReviewToDB(review: Omit<Review, 'id'>): Promise<Review> {
  const id = Date.now().toString() + '-' + Math.random().toString(36).substr(2, 9);
  
  await sql`
    INSERT INTO reviews (
      id, name, location, email, rating, title, review,
      vehicle_purchased, purchase_date, locale, submitted_at, status, images
    ) VALUES (
      ${id}, ${review.name}, ${review.location}, ${review.email},
      ${review.rating}, ${review.title || ''}, ${review.review},
      ${review.vehiclePurchased || ''}, ${review.purchaseDate || ''},
      ${review.locale}, ${review.submittedAt}, ${review.status},
      ${JSON.stringify(review.images || [])}
    )
  `;
  
  return { ...review, id };
}

export async function updateReviewStatusInDB(
  reviewId: string, 
  status: 'approved' | 'rejected'
): Promise<boolean> {
  const result = await sql`
    UPDATE reviews 
    SET status = ${status}, updated_at = NOW()
    WHERE id = ${reviewId}
  `;
  
  return result.rowCount > 0;
}

export async function getApprovedReviewsFromDB(): Promise<Review[]> {
  const { rows } = await sql`
    SELECT * FROM reviews 
    WHERE status = 'approved'
    ORDER BY submitted_at DESC
  `;
  return rows.map(/* same mapping as getAllReviewsFromDB */);
}
```

### Step 6: Update API Routes
Modify `src/app/api/reviews/route.ts`:
```typescript
// Replace file storage imports with database imports
import { 
  getAllReviewsFromDB as getAllReviews,
  addReviewToDB as addReview,
  updateReviewStatusInDB as updateReviewStatus,
  getApprovedReviewsFromDB as getApprovedReviews
} from '@/lib/database';

// The rest of your API code remains the same!
```

### Step 7: Data Migration
Create a migration script to move existing data:
```typescript
// scripts/migrate-to-database.ts
import { getAllReviews } from '../src/lib/reviewStorage';
import { addReviewToDB } from '../src/lib/database';

async function migrateData() {
  console.log('Starting migration...');
  
  const fileReviews = await getAllReviews();
  console.log(`Found ${fileReviews.length} reviews in file storage`);
  
  for (const review of fileReviews) {
    const { id, ...reviewData } = review;
    await addReviewToDB(reviewData);
    console.log(`Migrated review: ${review.name}`);
  }
  
  console.log('Migration completed!');
}

migrateData().catch(console.error);
```

## 🧪 Testing Database Integration

### 1. Test Database Connection
```typescript
// Test script
import { getAllReviewsFromDB } from './src/lib/database';

async function testConnection() {
  try {
    const reviews = await getAllReviewsFromDB();
    console.log(`✅ Database connected! Found ${reviews.length} reviews`);
  } catch (error) {
    console.error('❌ Database connection failed:', error);
  }
}

testConnection();
```

### 2. Test CRUD Operations
1. Submit a new review
2. Check it appears in database
3. Approve/reject via admin panel
4. Verify status updates in database
5. Check public API returns approved reviews

## 🔄 Rollback Plan

If something goes wrong:

1. **Keep file storage code** as backup
2. **Switch environment variable** to disable database
3. **Restore from file backup** if needed

```typescript
// Fallback configuration
const USE_DATABASE = process.env.DATABASE_URL ? true : false;

export const getAllReviews = USE_DATABASE 
  ? getAllReviewsFromDB 
  : getAllReviewsFromFile;
```

## 📊 Performance Optimization

### Database Indexes
```sql
-- Add these indexes for better performance
CREATE INDEX idx_reviews_status_date ON reviews(status, submitted_at DESC);
CREATE INDEX idx_reviews_rating_status ON reviews(rating, status);
```

### Connection Pooling
```typescript
// For high traffic, use connection pooling
import { Pool } from 'pg';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  max: 20, // Maximum connections
  idleTimeoutMillis: 30000,
});
```

## 🚀 Deployment Considerations

### Environment Variables
Set these in your production environment:
- `DATABASE_URL`
- `ADMIN_PASSWORD`
- Any database-specific variables

### Database Migrations
- Use migration tools like Prisma Migrate
- Version control your schema changes
- Test migrations in staging first

### Monitoring
- Set up database monitoring
- Monitor connection usage
- Track query performance
- Set up alerts for errors

## 🎯 Success Checklist

✅ Database created and accessible  
✅ Schema created with proper indexes  
✅ Environment variables configured  
✅ Database utility functions working  
✅ API routes updated and tested  
✅ Data migration completed  
✅ Production deployment successful  
✅ Monitoring and backups configured  

Your review system is now enterprise-ready with database storage!
