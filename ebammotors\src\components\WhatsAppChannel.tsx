'use client';

import Image from 'next/image';
import { useState } from 'react';

interface WhatsAppChannelProps {
  locale: string;
  variant?: 'full' | 'compact' | 'button-only';
  showQR?: boolean;
}

export default function WhatsAppChannel({ 
  locale, 
  variant = 'compact', 
  showQR = false 
}: WhatsAppChannelProps) {
  const [copied, setCopied] = useState(false);

  const channelUrl = 'https://whatsapp.com/channel/0029Vb5sDlFDTkK191T6Ut1G';

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(channelUrl);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy: ', err);
    }
  };

  if (variant === 'button-only') {
    return (
      <a
        href={channelUrl}
        target="_blank"
        rel="noopener noreferrer"
        className="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-200 flex items-center justify-center hover:scale-105 hover:shadow-lg"
      >
        <span className="mr-2">💬</span>
        {locale === 'en' ? 'Join WhatsApp Channel' : 'WhatsAppチャンネルに参加'}
      </a>
    );
  }

  if (variant === 'full') {
    return (
      <section className="py-16 bg-gradient-to-br from-green-50 to-green-100">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-heading font-bold text-neutral-800 mb-4">
              {locale === 'en' ? 'Join Our WhatsApp Channel' : 'WhatsAppチャンネルに参加'}
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              {locale === 'en'
                ? 'Stay updated with our latest stock, special offers, and important announcements.'
                : '最新の在庫、特別オファー、重要なお知らせを受け取ってください。'
              }
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* QR Code - Hidden on mobile phones, visible on tablets and larger */}
            {showQR && (
              <div className="hidden sm:block text-center">
                <div className="bg-white rounded-2xl shadow-lg p-8 inline-block">
                  <Image
                    src="/whatsapp qrcode.jpg"
                    alt="WhatsApp Channel QR Code"
                    width={250}
                    height={250}
                    className="rounded-lg w-auto h-auto"
                  />
                  <p className="text-sm text-neutral-600 mt-4">
                    {locale === 'en' ? 'Scan with your phone camera' : 'スマートフォンのカメラでスキャン'}
                  </p>
                </div>
              </div>
            )}

            {/* Channel Info */}
            <div className={showQR ? 'sm:col-span-1 lg:col-span-1' : 'lg:col-span-2'}>
              <div className="bg-white rounded-xl shadow-lg p-8">
                <h3 className="text-2xl font-heading font-bold text-neutral-800 mb-6">
                  {locale === 'en' ? 'What You\'ll Get:' : '受け取れる内容：'}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
                  <div className="flex items-start">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-3 mt-2 flex-shrink-0"></div>
                    <span className="text-neutral-700">
                      {locale === 'en' ? 'Latest car arrivals' : '最新の車両入荷'}
                    </span>
                  </div>
                  <div className="flex items-start">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-3 mt-2 flex-shrink-0"></div>
                    <span className="text-neutral-700">
                      {locale === 'en' ? 'Special offers' : '特別オファー'}
                    </span>
                  </div>
                  <div className="flex items-start">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-3 mt-2 flex-shrink-0"></div>
                    <span className="text-neutral-700">
                      {locale === 'en' ? 'Important updates' : '重要なアップデート'}
                    </span>
                  </div>
                  <div className="flex items-start">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-3 mt-2 flex-shrink-0"></div>
                    <span className="text-neutral-700">
                      {locale === 'en' ? 'Exclusive deals' : '限定のお得な情報'}
                    </span>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-4">
                  <a
                    href={channelUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex-1 bg-green-500 hover:bg-green-600 text-white px-6 py-4 rounded-lg font-semibold transition-all duration-200 flex items-center justify-center hover:scale-105 hover:shadow-lg"
                  >
                    {locale === 'en' ? 'Join Channel Now' : '今すぐチャンネルに参加'}
                  </a>
                  <button
                    onClick={copyToClipboard}
                    className="flex-1 bg-neutral-100 hover:bg-neutral-200 text-neutral-700 px-6 py-4 rounded-lg font-semibold transition-all duration-200 flex items-center justify-center hover:scale-105"
                  >
                    {copied
                      ? (locale === 'en' ? 'Copied!' : 'コピーしました！')
                      : (locale === 'en' ? 'Copy Link' : 'リンクをコピー')
                    }
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    );
  }

  // Compact variant
  return (
    <div className="bg-gradient-to-r from-green-500 to-green-600 text-white rounded-xl p-6 shadow-lg">
      <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
        <div className="text-center sm:text-left">
          <h3 className="text-lg font-bold mb-2">
            {locale === 'en' ? 'Join Our WhatsApp Channel' : 'WhatsAppチャンネルに参加'}
          </h3>
          <p className="text-green-100 text-sm">
            {locale === 'en'
              ? 'Get updates on latest stock and special offers'
              : '最新の在庫と特別オファーの更新を受け取る'
            }
          </p>
        </div>
        <div className="flex flex-col sm:flex-row gap-2">
          <a
            href={channelUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="bg-white text-green-600 hover:bg-green-50 px-4 py-2 rounded-lg font-semibold transition-all duration-200 flex items-center justify-center hover:scale-105 text-sm"
          >
            {locale === 'en' ? 'Join Now' : '参加する'}
          </a>
          {showQR && (
            <button
              onClick={copyToClipboard}
              className="hidden sm:flex bg-green-400 hover:bg-green-300 text-green-800 px-4 py-2 rounded-lg font-semibold transition-all duration-200 items-center justify-center hover:scale-105 text-sm"
            >

              {copied
                ? (locale === 'en' ? 'Copied!' : 'コピー済み')
                : (locale === 'en' ? 'Copy' : 'コピー')
              }
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
