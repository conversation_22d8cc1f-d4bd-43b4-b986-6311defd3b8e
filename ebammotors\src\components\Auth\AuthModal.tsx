'use client';

import { useState } from 'react';
import { X, Eye, EyeOff, Mail, Lock, User, Phone, MapPin, Loader } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialMode?: 'login' | 'register';
  locale: string;
}

export default function AuthModal({ isOpen, onClose, initialMode = 'login', locale }: AuthModalProps) {
  const [mode, setMode] = useState<'login' | 'register'>(initialMode);
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  
  const { login, register } = useAuth();
  
  const [loginData, setLoginData] = useState({
    email: '',
    password: '',
  });
  
  const [registerData, setRegisterData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    phone: '',
    location: '',
  });

  if (!isOpen) return null;

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    
    try {
      const success = await login(loginData.email, loginData.password);
      if (success) {
        onClose();
        setLoginData({ email: '', password: '' });
      } else {
        setError(locale === 'en' ? 'Invalid email or password' : 'メールアドレスまたはパスワードが無効です');
      }
    } catch (err) {
      setError(locale === 'en' ? 'Login failed. Please try again.' : 'ログインに失敗しました。もう一度お試しください。');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    
    if (registerData.password !== registerData.confirmPassword) {
      setError(locale === 'en' ? 'Passwords do not match' : 'パスワードが一致しません');
      setIsLoading(false);
      return;
    }
    
    try {
      const success = await register({
        name: registerData.name,
        email: registerData.email,
        password: registerData.password,
        phone: registerData.phone,
        location: registerData.location,
      });
      
      if (success) {
        onClose();
        setRegisterData({
          name: '',
          email: '',
          password: '',
          confirmPassword: '',
          phone: '',
          location: '',
        });
      } else {
        setError(locale === 'en' ? 'Registration failed. Please try again.' : '登録に失敗しました。もう一度お試しください。');
      }
    } catch (err) {
      setError(locale === 'en' ? 'Registration failed. Please try again.' : '登録に失敗しました。もう一度お試しください。');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-bold text-neutral-800">
            {mode === 'login' 
              ? (locale === 'en' ? 'Sign In' : 'サインイン')
              : (locale === 'en' ? 'Create Account' : 'アカウント作成')
            }
          </h2>
          <button
            onClick={onClose}
            className="text-neutral-600 hover:text-neutral-800 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm">
              {error}
            </div>
          )}

          {mode === 'login' ? (
            <form onSubmit={handleLogin} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-neutral-700 mb-2">
                  {locale === 'en' ? 'Email Address' : 'メールアドレス'}
                </label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-5 h-5" />
                  <input
                    type="email"
                    value={loginData.email}
                    onChange={(e) => setLoginData(prev => ({ ...prev, email: e.target.value }))}
                    className="w-full pl-10 pr-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    placeholder={locale === 'en' ? 'Enter your email' : 'メールアドレスを入力'}
                    required
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-neutral-700 mb-2">
                  {locale === 'en' ? 'Password' : 'パスワード'}
                </label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-5 h-5" />
                  <input
                    type={showPassword ? 'text' : 'password'}
                    value={loginData.password}
                    onChange={(e) => setLoginData(prev => ({ ...prev, password: e.target.value }))}
                    className="w-full pl-10 pr-12 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    placeholder={locale === 'en' ? 'Enter your password' : 'パスワードを入力'}
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-400 hover:text-neutral-600"
                  >
                    {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                  </button>
                </div>
              </div>

              <button
                type="submit"
                disabled={isLoading}
                className="w-full bg-primary-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
              >
                {isLoading && <Loader className="w-4 h-4 animate-spin" />}
                <span>{locale === 'en' ? 'Sign In' : 'サインイン'}</span>
              </button>
            </form>
          ) : (
            <form onSubmit={handleRegister} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-neutral-700 mb-2">
                  {locale === 'en' ? 'Full Name' : '氏名'}
                </label>
                <div className="relative">
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-5 h-5" />
                  <input
                    type="text"
                    value={registerData.name}
                    onChange={(e) => setRegisterData(prev => ({ ...prev, name: e.target.value }))}
                    className="w-full pl-10 pr-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    placeholder={locale === 'en' ? 'Enter your full name' : '氏名を入力'}
                    required
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-neutral-700 mb-2">
                  {locale === 'en' ? 'Email Address' : 'メールアドレス'}
                </label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-5 h-5" />
                  <input
                    type="email"
                    value={registerData.email}
                    onChange={(e) => setRegisterData(prev => ({ ...prev, email: e.target.value }))}
                    className="w-full pl-10 pr-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    placeholder={locale === 'en' ? 'Enter your email' : 'メールアドレスを入力'}
                    required
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-neutral-700 mb-2">
                  {locale === 'en' ? 'Phone Number' : '電話番号'} ({locale === 'en' ? 'Optional' : '任意'})
                </label>
                <div className="relative">
                  <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-5 h-5" />
                  <input
                    type="tel"
                    value={registerData.phone}
                    onChange={(e) => setRegisterData(prev => ({ ...prev, phone: e.target.value }))}
                    className="w-full pl-10 pr-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    placeholder={locale === 'en' ? 'Enter your phone number' : '電話番号を入力'}
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-neutral-700 mb-2">
                  {locale === 'en' ? 'Location' : '所在地'} ({locale === 'en' ? 'Optional' : '任意'})
                </label>
                <div className="relative">
                  <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-5 h-5" />
                  <input
                    type="text"
                    value={registerData.location}
                    onChange={(e) => setRegisterData(prev => ({ ...prev, location: e.target.value }))}
                    className="w-full pl-10 pr-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    placeholder={locale === 'en' ? 'Enter your location' : '所在地を入力'}
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-neutral-700 mb-2">
                  {locale === 'en' ? 'Password' : 'パスワード'}
                </label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-5 h-5" />
                  <input
                    type={showPassword ? 'text' : 'password'}
                    value={registerData.password}
                    onChange={(e) => setRegisterData(prev => ({ ...prev, password: e.target.value }))}
                    className="w-full pl-10 pr-12 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    placeholder={locale === 'en' ? 'Create a password' : 'パスワードを作成'}
                    required
                    minLength={6}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-400 hover:text-neutral-600"
                  >
                    {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                  </button>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-neutral-700 mb-2">
                  {locale === 'en' ? 'Confirm Password' : 'パスワード確認'}
                </label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-5 h-5" />
                  <input
                    type={showPassword ? 'text' : 'password'}
                    value={registerData.confirmPassword}
                    onChange={(e) => setRegisterData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                    className="w-full pl-10 pr-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    placeholder={locale === 'en' ? 'Confirm your password' : 'パスワードを確認'}
                    required
                  />
                </div>
              </div>

              <button
                type="submit"
                disabled={isLoading}
                className="w-full bg-primary-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
              >
                {isLoading && <Loader className="w-4 h-4 animate-spin" />}
                <span>{locale === 'en' ? 'Create Account' : 'アカウント作成'}</span>
              </button>
            </form>
          )}

          {/* Switch Mode */}
          <div className="mt-6 text-center">
            <p className="text-neutral-600">
              {mode === 'login' 
                ? (locale === 'en' ? "Don't have an account?" : 'アカウントをお持ちでない方は')
                : (locale === 'en' ? 'Already have an account?' : '既にアカウントをお持ちの方は')
              }
            </p>
            <button
              onClick={() => {
                setMode(mode === 'login' ? 'register' : 'login');
                setError('');
              }}
              className="text-primary-600 hover:text-primary-700 font-semibold transition-colors"
            >
              {mode === 'login' 
                ? (locale === 'en' ? 'Create Account' : 'アカウント作成')
                : (locale === 'en' ? 'Sign In' : 'サインイン')
              }
            </button>
          </div>

          {/* Benefits */}
          <div className="mt-6 p-4 bg-primary-50 rounded-lg">
            <h3 className="font-semibold text-primary-800 mb-2">
              {locale === 'en' ? 'Member Benefits' : '会員特典'}
            </h3>
            <ul className="text-sm text-primary-700 space-y-1">
              <li>• {locale === 'en' ? 'Save favorite vehicles' : 'お気に入り車両の保存'}</li>
              <li>• {locale === 'en' ? 'Track purchase history' : '購入履歴の追跡'}</li>
              <li>• {locale === 'en' ? 'Earn loyalty points' : 'ロイヤルティポイントの獲得'}</li>
              <li>• {locale === 'en' ? 'Save custom searches' : 'カスタム検索の保存'}</li>
              <li>• {locale === 'en' ? 'Get exclusive offers' : '限定オファーの取得'}</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
