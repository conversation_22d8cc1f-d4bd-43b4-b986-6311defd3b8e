'use client';

import { useState, useEffect } from 'react';
import { CheckCircle, XCircle, AlertCircle, RefreshCw } from 'lucide-react';

interface AIStatus {
  status: string;
  aiAvailable: boolean;
  provider?: {
    provider: string;
    model: string;
  };
  error?: string;
}

export default function AIStatusPanel() {
  const [aiStatus, setAiStatus] = useState<AIStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [testing, setTesting] = useState(false);
  const [testResult, setTestResult] = useState<string>('');

  const checkAIStatus = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/chatbot');
      const data = await response.json();
      setAiStatus(data);
    } catch (error) {
      setAiStatus({
        status: 'error',
        aiAvailable: false,
        error: 'Failed to check AI status'
      });
    } finally {
      setLoading(false);
    }
  };

  const testAI = async () => {
    setTesting(true);
    setTestResult('');
    try {
      const response = await fetch('/api/chatbot', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: 'Hello, can you tell me about EBAM Motors?',
          context: 'Testing AI functionality',
          locale: 'en'
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        setTestResult(`✅ AI Response: ${data.response.substring(0, 200)}${data.response.length > 200 ? '...' : ''}`);
      } else {
        setTestResult(`❌ AI Test Failed: ${data.error}`);
      }
    } catch (error) {
      setTestResult(`❌ Test Error: ${error}`);
    } finally {
      setTesting(false);
    }
  };

  useEffect(() => {
    checkAIStatus();
  }, []);

  const getStatusIcon = () => {
    if (loading) return <RefreshCw className="w-5 h-5 animate-spin text-blue-500" />;
    if (!aiStatus) return <XCircle className="w-5 h-5 text-red-500" />;
    if (aiStatus.aiAvailable) return <CheckCircle className="w-5 h-5 text-green-500" />;
    return <XCircle className="w-5 h-5 text-red-500" />;
  };

  const getStatusText = () => {
    if (loading) return 'Checking...';
    if (!aiStatus) return 'Unknown';
    if (aiStatus.aiAvailable) return 'AI Active';
    return 'AI Unavailable';
  };

  const getStatusColor = () => {
    if (loading) return 'text-blue-600';
    if (!aiStatus || !aiStatus.aiAvailable) return 'text-red-600';
    return 'text-green-600';
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">AI Chatbot Status</h3>
        <button
          onClick={checkAIStatus}
          disabled={loading}
          className="flex items-center space-x-2 px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
        >
          <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
          <span>Refresh</span>
        </button>
      </div>

      {/* Status Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
          {getStatusIcon()}
          <div>
            <p className="text-sm text-gray-600">Status</p>
            <p className={`font-medium ${getStatusColor()}`}>{getStatusText()}</p>
          </div>
        </div>

        {aiStatus?.provider && (
          <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
            <AlertCircle className="w-5 h-5 text-blue-500" />
            <div>
              <p className="text-sm text-gray-600">Provider</p>
              <p className="font-medium text-gray-900">{aiStatus.provider.provider}</p>
              <p className="text-xs text-gray-500">{aiStatus.provider.model}</p>
            </div>
          </div>
        )}
      </div>

      {/* Error Message */}
      {aiStatus?.error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-sm text-red-600">
            <strong>Error:</strong> {aiStatus.error}
          </p>
        </div>
      )}

      {/* Configuration Info */}
      <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
        <p className="text-sm text-blue-800 mb-2">
          <strong>Configuration:</strong>
        </p>
        <ul className="text-xs text-blue-700 space-y-1">
          <li>• Check your .env.local file for AI_PROVIDER setting</li>
          <li>• Ensure API keys are configured correctly</li>
          <li>• See AI_CHATBOT_SETUP.md for detailed instructions</li>
        </ul>
      </div>

      {/* Test AI */}
      <div className="border-t pt-4">
        <div className="flex items-center justify-between mb-3">
          <h4 className="font-medium text-gray-900">Test AI Response</h4>
          <button
            onClick={testAI}
            disabled={testing || !aiStatus?.aiAvailable}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {testing ? 'Testing...' : 'Test AI'}
          </button>
        </div>

        {testResult && (
          <div className="p-3 bg-gray-50 rounded-lg">
            <p className="text-sm text-gray-700 whitespace-pre-wrap">{testResult}</p>
          </div>
        )}
      </div>

      {/* Quick Setup Links */}
      <div className="border-t pt-4 mt-4">
        <h4 className="font-medium text-gray-900 mb-2">Quick Setup</h4>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
          <a
            href="https://huggingface.co/settings/tokens"
            target="_blank"
            rel="noopener noreferrer"
            className="text-xs px-3 py-2 bg-yellow-100 text-yellow-800 rounded hover:bg-yellow-200 text-center"
          >
            Get Hugging Face Key
          </a>
          <a
            href="https://console.groq.com/"
            target="_blank"
            rel="noopener noreferrer"
            className="text-xs px-3 py-2 bg-purple-100 text-purple-800 rounded hover:bg-purple-200 text-center"
          >
            Get Groq Key
          </a>
          <a
            href="https://ollama.ai/"
            target="_blank"
            rel="noopener noreferrer"
            className="text-xs px-3 py-2 bg-green-100 text-green-800 rounded hover:bg-green-200 text-center"
          >
            Install Ollama
          </a>
        </div>
      </div>
    </div>
  );
}