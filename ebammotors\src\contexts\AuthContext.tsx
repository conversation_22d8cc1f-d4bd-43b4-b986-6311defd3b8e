'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  phone?: string;
  location?: string;
  joinDate: string;
  loyaltyPoints: number;
  membershipTier: 'Bronze' | 'Silver' | 'Gold' | 'Platinum';
  preferences: {
    notifications: {
      email: boolean;
      sms: boolean;
      push: boolean;
      marketing: boolean;
    };
    language: 'en' | 'ja';
    currency: 'JPY' | 'USD';
  };
}

export interface SavedSearch {
  id: string;
  name: string;
  filters: any;
  createdAt: string;
  lastUsed: string;
}

export interface FavoriteItem {
  id: string;
  vehicleId: string;
  addedAt: string;
  notes?: string;
}

export interface PurchaseHistoryItem {
  id: string;
  vehicleId: string;
  vehicleTitle: string;
  price: string;
  status: 'pending' | 'confirmed' | 'shipped' | 'delivered' | 'cancelled';
  orderDate: string;
  estimatedDelivery?: string;
  trackingNumber?: string;
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<boolean>;
  register: (userData: RegisterData) => Promise<boolean>;
  logout: () => void;
  updateProfile: (updates: Partial<User>) => Promise<boolean>;
  
  // Favorites
  favorites: FavoriteItem[];
  addToFavorites: (vehicleId: string, notes?: string) => void;
  removeFromFavorites: (vehicleId: string) => void;
  isFavorite: (vehicleId: string) => boolean;
  
  // Saved Searches
  savedSearches: SavedSearch[];
  saveSearch: (name: string, filters: any) => void;
  removeSavedSearch: (searchId: string) => void;
  
  // Purchase History
  purchaseHistory: PurchaseHistoryItem[];
  addPurchase: (purchase: Omit<PurchaseHistoryItem, 'id' | 'orderDate'>) => void;
  
  // Loyalty Points
  addLoyaltyPoints: (points: number, reason: string) => void;
  redeemPoints: (points: number, reason: string) => boolean;
}

interface RegisterData {
  name: string;
  email: string;
  password: string;
  phone?: string;
  location?: string;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [favorites, setFavorites] = useState<FavoriteItem[]>([]);
  const [savedSearches, setSavedSearches] = useState<SavedSearch[]>([]);
  const [purchaseHistory, setPurchaseHistory] = useState<PurchaseHistoryItem[]>([]);

  // Initialize from localStorage
  useEffect(() => {
    const initializeAuth = () => {
      try {
        // Ensure we're on the client side before accessing localStorage
        if (typeof window === 'undefined') {
          setIsLoading(false);
          return;
        }

        const storedUser = localStorage.getItem('ebam_user');
        const storedFavorites = localStorage.getItem('ebam_favorites');
        const storedSearches = localStorage.getItem('ebam_saved_searches');
        const storedPurchases = localStorage.getItem('ebam_purchase_history');

        if (storedUser) {
          setUser(JSON.parse(storedUser));
        }
        if (storedFavorites) {
          setFavorites(JSON.parse(storedFavorites));
        }
        if (storedSearches) {
          setSavedSearches(JSON.parse(storedSearches));
        }
        if (storedPurchases) {
          setPurchaseHistory(JSON.parse(storedPurchases));
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  // Save to localStorage whenever data changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      if (user) {
        localStorage.setItem('ebam_user', JSON.stringify(user));
      } else {
        localStorage.removeItem('ebam_user');
      }
    }
  }, [user]);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('ebam_favorites', JSON.stringify(favorites));
    }
  }, [favorites]);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('ebam_saved_searches', JSON.stringify(savedSearches));
    }
  }, [savedSearches]);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('ebam_purchase_history', JSON.stringify(purchaseHistory));
    }
  }, [purchaseHistory]);

  const login = async (email: string, password: string): Promise<boolean> => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock user data - in real app, this would come from API
      const mockUser: User = {
        id: '1',
        email,
        name: email.split('@')[0],
        joinDate: new Date().toISOString(),
        loyaltyPoints: 1250,
        membershipTier: 'Silver',
        preferences: {
          notifications: {
            email: true,
            sms: false,
            push: true,
            marketing: false,
          },
          language: 'en',
          currency: 'JPY',
        },
      };
      
      setUser(mockUser);
      return true;
    } catch (error) {
      console.error('Login error:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (userData: RegisterData): Promise<boolean> => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newUser: User = {
        id: Date.now().toString(),
        email: userData.email,
        name: userData.name,
        phone: userData.phone,
        location: userData.location,
        joinDate: new Date().toISOString(),
        loyaltyPoints: 100, // Welcome bonus
        membershipTier: 'Bronze',
        preferences: {
          notifications: {
            email: true,
            sms: false,
            push: true,
            marketing: false,
          },
          language: 'en',
          currency: 'JPY',
        },
      };
      
      setUser(newUser);
      return true;
    } catch (error) {
      console.error('Registration error:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    setUser(null);
    setFavorites([]);
    setSavedSearches([]);
    setPurchaseHistory([]);

    if (typeof window !== 'undefined') {
      localStorage.removeItem('ebam_user');
      localStorage.removeItem('ebam_favorites');
      localStorage.removeItem('ebam_saved_searches');
      localStorage.removeItem('ebam_purchase_history');
    }
  };

  const updateProfile = async (updates: Partial<User>): Promise<boolean> => {
    if (!user) return false;
    
    try {
      const updatedUser = { ...user, ...updates };
      setUser(updatedUser);
      return true;
    } catch (error) {
      console.error('Profile update error:', error);
      return false;
    }
  };

  // Favorites functions
  const addToFavorites = (vehicleId: string, notes?: string) => {
    if (!user) return;
    
    const newFavorite: FavoriteItem = {
      id: Date.now().toString(),
      vehicleId,
      addedAt: new Date().toISOString(),
      notes,
    };
    
    setFavorites(prev => [...prev, newFavorite]);
    addLoyaltyPoints(5, 'Added to favorites');
  };

  const removeFromFavorites = (vehicleId: string) => {
    setFavorites(prev => prev.filter(fav => fav.vehicleId !== vehicleId));
  };

  const isFavorite = (vehicleId: string): boolean => {
    return favorites.some(fav => fav.vehicleId === vehicleId);
  };

  // Saved searches functions
  const saveSearch = (name: string, filters: any) => {
    if (!user) return;
    
    const newSearch: SavedSearch = {
      id: Date.now().toString(),
      name,
      filters,
      createdAt: new Date().toISOString(),
      lastUsed: new Date().toISOString(),
    };
    
    setSavedSearches(prev => [...prev, newSearch]);
  };

  const removeSavedSearch = (searchId: string) => {
    setSavedSearches(prev => prev.filter(search => search.id !== searchId));
  };

  // Purchase history functions
  const addPurchase = (purchase: Omit<PurchaseHistoryItem, 'id' | 'orderDate'>) => {
    if (!user) return;
    
    const newPurchase: PurchaseHistoryItem = {
      ...purchase,
      id: Date.now().toString(),
      orderDate: new Date().toISOString(),
    };
    
    setPurchaseHistory(prev => [...prev, newPurchase]);
    
    // Add loyalty points based on purchase
    const price = parseInt(purchase.price.replace(/[¥,]/g, '')) || 0;
    const points = Math.floor(price / 1000); // 1 point per ¥1000
    addLoyaltyPoints(points, `Purchase: ${purchase.vehicleTitle}`);
  };

  // Loyalty points functions
  const addLoyaltyPoints = (points: number, reason: string) => {
    if (!user) return;
    
    const newPoints = user.loyaltyPoints + points;
    let newTier = user.membershipTier;
    
    // Update tier based on points
    if (newPoints >= 10000) newTier = 'Platinum';
    else if (newPoints >= 5000) newTier = 'Gold';
    else if (newPoints >= 2000) newTier = 'Silver';
    else newTier = 'Bronze';
    
    setUser(prev => prev ? {
      ...prev,
      loyaltyPoints: newPoints,
      membershipTier: newTier,
    } : null);
  };

  const redeemPoints = (points: number, reason: string): boolean => {
    if (!user || user.loyaltyPoints < points) return false;
    
    setUser(prev => prev ? {
      ...prev,
      loyaltyPoints: prev.loyaltyPoints - points,
    } : null);
    
    return true;
  };

  const value: AuthContextType = {
    user,
    isLoading,
    login,
    register,
    logout,
    updateProfile,
    favorites,
    addToFavorites,
    removeFromFavorites,
    isFavorite,
    savedSearches,
    saveSearch,
    removeSavedSearch,
    purchaseHistory,
    addPurchase,
    addLoyaltyPoints,
    redeemPoints,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
