'use client';

import { useState } from 'react';
import CategoryFilter from './CategoryFilter';
import InventoryGrid from './InventoryGrid';

interface InventoryItem {
  id: number;
  category: string;
  title: string;
  price: string;
  location: string;
  status: string;
  image: string;
  images: string[];
  specs: string[];
  carId: string;
}

interface InventoryWrapperProps {
  items: InventoryItem[];
  locale: string;
}

export default function InventoryWrapper({ items, locale }: InventoryWrapperProps) {
  const [filteredCategory, setFilteredCategory] = useState('all');

  const handleCategoryChange = (category: string) => {
    setFilteredCategory(category);
  };

  return (
    <>
      <CategoryFilter locale={locale} onCategoryChange={handleCategoryChange} />
      <InventoryGrid items={items} locale={locale} filteredCategory={filteredCategory} />
    </>
  );
}
