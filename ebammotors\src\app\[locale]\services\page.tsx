import Link from 'next/link';
import Image from 'next/image';
import { getMessages } from '@/lib/messages';
import Navigation from '@/components/Navigation';

export default async function ServicesPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;
  const messages = await getMessages(locale);

  const services = [
    {
      title: messages.services.usedCars,
      description: messages.services.usedCarsDesc,
      image: "/used cars/voxy.jpeg"
    },
    {
      title: messages.services.carParts,
      description: messages.services.carPartsDesc,
      image: "/car parts and tires/car parts.jpeg"
    },
    {
      title: messages.services.furniture,
      description: messages.services.furnitureDesc,
      image: "/furniture/furniture.png"
    },
    {
      title: messages.services.electronics,
      description: messages.services.electronicsDesc,
      image: "/electronics/phones and laptops.jpg"
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Navigation */}
      <Navigation />

      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 text-white py-20 overflow-hidden">
        <div className="absolute inset-0">
          <Image
            src="/move-out services/move out1.jpg"
            alt="Services background"
            fill
            className="object-cover opacity-20"
            priority
          />
        </div>
        <div className="absolute inset-0 bg-black/40"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center z-10">
          <h1 className="text-4xl lg:text-5xl font-heading font-bold mb-6 animate-fadeInUp">
            {messages.services.title}
          </h1>
          <p className="text-xl lg:text-2xl text-primary-100 max-w-4xl mx-auto animate-fadeInUp animate-delay-200">
            {messages.services.subtitle}
          </p>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-20 bg-neutral-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-heading font-bold text-neutral-800 mb-6 animate-fadeInUp">
              {locale === 'en' ? 'Our Services' : '私たちのサービス'}
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto animate-fadeInUp animate-delay-200">
              {locale === 'en'
                ? 'From vehicles to household items, we handle a wide range of quality used goods for export to Africa.'
                : '車両から家庭用品まで、アフリカ輸出用の幅広い中古品を取り扱っています。'
              }
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <div
                key={index}
                className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2 group animate-fadeInUp"
                style={{
                  animationDelay: `${index * 100}ms`
                }}
              >
                <div className="relative h-48 overflow-hidden">
                  <Image
                    src={service.image}
                    alt={service.title}
                    fill
                    className="object-cover transition-transform duration-500 group-hover:scale-110"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  />
                  <div className="absolute inset-0 bg-black/20 group-hover:bg-black/30 transition-colors duration-300"></div>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-heading font-bold text-neutral-800 mb-3 group-hover:text-primary-600 transition-colors duration-300">
                    {service.title}
                  </h3>
                  <p className="text-neutral-600 leading-relaxed">
                    {service.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Partner Services Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-heading font-bold text-neutral-800 mb-6">
              {messages.services.partners}
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              {messages.services.moveOutDesc}
            </p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="bg-primary-50 rounded-xl p-8 relative overflow-hidden">
                <div className="absolute top-0 right-0 w-32 h-32 opacity-10">
                  <Image
                    src="/move-out services/move out.png"
                    alt="Move out services"
                    fill
                    className="object-cover"
                  />
                </div>
                <h3 className="text-2xl font-heading font-bold text-primary-800 mb-4 relative z-10">
                  {messages.services.moveOut}
                </h3>
                <p className="text-neutral-700 leading-relaxed relative z-10">
                  {messages.services.moveOutDesc}
                </p>
              </div>

              <div className="bg-secondary-50 rounded-xl p-8 relative overflow-hidden">
                <div className="absolute top-0 right-0 w-32 h-32 opacity-10">
                  <Image
                    src="/move-out services/junk services.png"
                    alt="Recycling services"
                    fill
                    className="object-cover"
                  />
                </div>
                <h3 className="text-2xl font-heading font-bold text-secondary-800 mb-4 relative z-10">
                  {messages.services.recycling}
                </h3>
                <p className="text-neutral-700 leading-relaxed relative z-10">
                  We promote sustainable practices by giving quality used goods a second life in markets where they are most needed.
                </p>
              </div>
            </div>
            
            <div className="relative">
              <div className="bg-gradient-to-br from-primary-600 to-primary-800 rounded-2xl p-8 text-white">
                <h3 className="text-2xl font-heading font-bold mb-6">
                  Ready to Partner With Us?
                </h3>
                <p className="text-primary-100 mb-8 leading-relaxed">
                  Whether you&apos;re a moving company, junk removal service, or individual looking to dispose of quality goods, we offer competitive pricing and reliable collection services.
                </p>
                <div className="space-y-4">
                  <Link
                    href={`/${locale}/suppliers`}
                    className="block bg-secondary-500 hover:bg-secondary-600 text-neutral-900 px-6 py-3 rounded-lg font-semibold text-center transition-colors duration-200"
                  >
                    {messages.navigation.suppliers}
                  </Link>
                  <Link
                    href={`/${locale}/contact`}
                    className="block border-2 border-white text-white hover:bg-white hover:text-primary-800 px-6 py-3 rounded-lg font-semibold text-center transition-colors duration-200"
                  >
                    {messages.navigation.contact}
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-neutral-800 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-xl">EM</span>
                </div>
                <span className="font-heading font-bold text-xl">EBAM MOTORS</span>
              </div>
              <p className="text-neutral-300">
                {messages.about?.mission || 'Promote sustainable trade by giving used goods a second life where they are needed most.'}
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-lg mb-4">Contact Info</h3>
              <div className="space-y-2 text-neutral-300">
                <div>
                  <p>📞 Japan: {messages.about?.phone || '080-6985-2864'}</p>
                  <p>📞 Ghana: {messages.about?.ghanaPhone || '+233245375692'}</p>
                </div>
                <p>✉️ {messages.about?.email || '<EMAIL>'}</p>
                <div>
                  <p>📍 Japan: {messages.about?.location || 'Japan, Saitama, Hanno City, Nagata'}</p>
                  <p>📍 Ghana: {messages.about?.ghanaLocation || 'Kumasi, Ghana'}</p>
                </div>
              </div>
            </div>
            <div>
              <h3 className="font-semibold text-lg mb-4">{messages.contact?.quickLinks || 'Quick Links'}</h3>
              <div className="space-y-2">
                <Link href={`/${locale}/about`} className="block text-neutral-300 hover:text-white transition-colors">
                  {messages.navigation.about}
                </Link>
                <Link href={`/${locale}/inventory`} className="block text-neutral-300 hover:text-white transition-colors">
                  {messages.navigation.inventory}
                </Link>
                <Link href={`/${locale}/contact`} className="block text-neutral-300 hover:text-white transition-colors">
                  {messages.navigation.contact}
                </Link>
              </div>
            </div>
          </div>
          <div className="border-t border-neutral-700 mt-8 pt-8 text-center text-neutral-400">
            <p>&copy; 2025 EBAM MOTORS. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
