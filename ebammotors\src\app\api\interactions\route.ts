import { NextRequest, NextResponse } from 'next/server';
import { 
  createInteraction, 
  getAllInteractions, 
  getInteractionsByCustomerId,
  getInteractionsByLeadId 
} from '@/lib/crmStorage';
import { Interaction } from '@/types/payment';

interface ActivityDetails {
  page?: string;
  productId?: string;
  searchTerm?: string;
  downloadType?: string;
  [key: string]: unknown;
}

// GET - Fetch interactions
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const adminKey = searchParams.get('adminKey');
    const customerId = searchParams.get('customerId');
    const leadId = searchParams.get('leadId');

    // Verify admin authentication
    const validAdminKey = process.env.ADMIN_PASSWORD || 'admin123';
    if (adminKey !== validAdminKey) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    let interactions: Interaction[] = [];

    // Get interactions by customer ID
    if (customerId) {
      interactions = await getInteractionsByCustomerId(customerId);
    }
    // Get interactions by lead ID
    else if (leadId) {
      interactions = await getInteractionsByLeadId(leadId);
    }
    // Get all interactions
    else {
      interactions = await getAllInteractions();
    }

    // Sort by most recent first
    interactions.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    return NextResponse.json({ success: true, interactions });

  } catch (error) {
    console.error('Error fetching interactions:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch interactions' },
      { status: 500 }
    );
  }
}

// POST - Create new interaction
export async function POST(request: NextRequest) {
  try {
    const interactionData = await request.json();

    // Validate required fields
    if (!interactionData.type || !interactionData.content) {
      return NextResponse.json(
        { success: false, message: 'Type and content are required' },
        { status: 400 }
      );
    }

    // Set defaults
    const newInteractionData = {
      direction: 'inbound',
      channel: 'website',
      tags: [],
      createdBy: 'system',
      ...interactionData,
    } as Omit<Interaction, 'id' | 'createdAt'>;

    const interaction = await createInteraction(newInteractionData);

    return NextResponse.json({ 
      success: true, 
      message: 'Interaction logged successfully',
      interaction 
    });

  } catch (error) {
    console.error('Error creating interaction:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to log interaction' },
      { status: 500 }
    );
  }
}

// POST endpoint for logging chatbot interactions
export async function logChatbotInteraction(
  customerId: string | undefined,
  leadId: string | undefined,
  userMessage: string,
  botResponse: string,
  productInterest?: string
) {
  try {
    // Log user message
    await createInteraction({
      customerId,
      leadId,
      type: 'chat',
      direction: 'inbound',
      channel: 'website',
      content: userMessage,
      subject: 'Chatbot Conversation',
      tags: productInterest ? ['chatbot', 'product_interest', productInterest] : ['chatbot'],
      createdBy: customerId || 'anonymous',
    });

    // Log bot response
    await createInteraction({
      customerId,
      leadId,
      type: 'chat',
      direction: 'outbound',
      channel: 'website',
      content: botResponse,
      subject: 'Chatbot Response',
      tags: ['chatbot', 'automated_response'],
      createdBy: 'system',
    });

    return true;
  } catch (error) {
    console.error('Error logging chatbot interaction:', error);
    return false;
  }
}

// POST endpoint for logging order interactions
export async function logOrderInteraction(
  customerId: string,
  orderId: string,
  type: 'order' | 'payment' | 'shipping',
  content: string,
  status?: string
) {
  try {
    await createInteraction({
      customerId,
      type,
      direction: 'outbound',
      channel: 'website',
      content,
      subject: `Order ${type.charAt(0).toUpperCase() + type.slice(1)} Update`,
      tags: ['order', type, status || 'update'].filter(Boolean),
      relatedOrderId: orderId,
      createdBy: 'system',
    });

    return true;
  } catch (error) {
    console.error('Error logging order interaction:', error);
    return false;
  }
}

// POST endpoint for logging website activity
export async function logWebsiteActivity(
  customerId: string,
  activityType: 'page_view' | 'product_view' | 'search' | 'download',
  details: ActivityDetails
) {
  try {
    const { createCustomerActivity } = await import('@/lib/crmStorage');
    
    await createCustomerActivity({
      customerId,
      type: activityType,
      details,
    });

    // Also create an interaction for significant activities
    if (activityType === 'product_view' || activityType === 'search') {
      await createInteraction({
        customerId,
        type: 'website_visit',
        direction: 'inbound',
        channel: 'website',
        content: activityType === 'product_view' 
          ? `Viewed product: ${details.productId}` 
          : `Searched for: ${details.searchQuery}`,
        subject: 'Website Activity',
        tags: ['website_activity', activityType],
        createdBy: customerId,
        metadata: details,
      });
    }

    return true;
  } catch (error) {
    console.error('Error logging website activity:', error);
    return false;
  }
}
