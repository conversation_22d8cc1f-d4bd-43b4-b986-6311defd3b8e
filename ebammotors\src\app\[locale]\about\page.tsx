import Link from 'next/link';
import { <PERSON>R<PERSON>, Globe, Shield, Truck, Users, Heart, Award, Leaf, Handshake } from 'lucide-react';
import { getMessages } from '@/lib/messages';
import Navigation from '@/components/Navigation';

export default async function AboutPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;
  const messages = await getMessages(locale);

  return (
    <div className="min-h-screen">
      {/* Navigation */}
      <Navigation />

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary-700 via-primary-800 to-primary-900 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-8">
            <h1 className="text-4xl lg:text-6xl font-heading font-bold leading-tight">
              {messages.about.heroTitle}
            </h1>
            <p className="text-xl lg:text-2xl text-primary-100 leading-relaxed max-w-4xl mx-auto">
              {messages.about.heroSubtitle}
            </p>
          </div>
        </div>
      </section>

      {/* Our Story Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div className="space-y-8">
              <h2 className="text-3xl lg:text-4xl font-heading font-bold text-neutral-800">
                {messages.about.ourStory.title}
              </h2>
              <p className="text-lg text-neutral-600 leading-relaxed">
                {messages.about.ourStory.content}
              </p>
            </div>
            <div className="relative">
              <div className="bg-gradient-to-br from-primary-100 to-secondary-100 rounded-2xl p-8 h-96 flex items-center justify-center">
                <Globe className="w-32 h-32 text-primary-600" />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* What We Do Section */}
      <section className="py-20 bg-neutral-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-4 mb-16">
            <h2 className="text-3xl lg:text-4xl font-heading font-bold text-neutral-800">
              {messages.about.whatWeDo.title}
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              {messages.about.whatWeDo.subtitle}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="bg-white rounded-lg p-8 text-center shadow-sm hover:shadow-md transition-shadow duration-200">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Globe className="w-8 h-8 text-primary-600" />
              </div>
              <h3 className="font-semibold text-neutral-800 mb-4">{messages.about.whatWeDo.sourcing.title}</h3>
              <p className="text-neutral-600 text-sm leading-relaxed">{messages.about.whatWeDo.sourcing.description}</p>
            </div>

            <div className="bg-white rounded-lg p-8 text-center shadow-sm hover:shadow-md transition-shadow duration-200">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Shield className="w-8 h-8 text-primary-600" />
              </div>
              <h3 className="font-semibold text-neutral-800 mb-4">{messages.about.whatWeDo.quality.title}</h3>
              <p className="text-neutral-600 text-sm leading-relaxed">{messages.about.whatWeDo.quality.description}</p>
            </div>

            <div className="bg-white rounded-lg p-8 text-center shadow-sm hover:shadow-md transition-shadow duration-200">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Truck className="w-8 h-8 text-primary-600" />
              </div>
              <h3 className="font-semibold text-neutral-800 mb-4">{messages.about.whatWeDo.logistics.title}</h3>
              <p className="text-neutral-600 text-sm leading-relaxed">{messages.about.whatWeDo.logistics.description}</p>
            </div>

            <div className="bg-white rounded-lg p-8 text-center shadow-sm hover:shadow-md transition-shadow duration-200">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Users className="w-8 h-8 text-primary-600" />
              </div>
              <h3 className="font-semibold text-neutral-800 mb-4">{messages.about.whatWeDo.support.title}</h3>
              <p className="text-neutral-600 text-sm leading-relaxed">{messages.about.whatWeDo.support.description}</p>
            </div>
          </div>
        </div>
      </section>

      {/* Why Japan Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-4 mb-16">
            <h2 className="text-3xl lg:text-4xl font-heading font-bold text-neutral-800">
              {messages.about.whyJapan.title}
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              {messages.about.whyJapan.subtitle}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="flex items-start space-x-6">
              <div className="w-12 h-12 bg-secondary-100 rounded-lg flex items-center justify-center flex-shrink-0">
                <Award className="w-6 h-6 text-secondary-600" />
              </div>
              <div>
                <h3 className="font-semibold text-neutral-800 mb-2">{messages.about.whyJapan.quality.title}</h3>
                <p className="text-neutral-600">{messages.about.whyJapan.quality.description}</p>
              </div>
            </div>

            <div className="flex items-start space-x-6">
              <div className="w-12 h-12 bg-secondary-100 rounded-lg flex items-center justify-center flex-shrink-0">
                <Globe className="w-6 h-6 text-secondary-600" />
              </div>
              <div>
                <h3 className="font-semibold text-neutral-800 mb-2">{messages.about.whyJapan.variety.title}</h3>
                <p className="text-neutral-600">{messages.about.whyJapan.variety.description}</p>
              </div>
            </div>

            <div className="flex items-start space-x-6">
              <div className="w-12 h-12 bg-secondary-100 rounded-lg flex items-center justify-center flex-shrink-0">
                <Heart className="w-6 h-6 text-secondary-600" />
              </div>
              <div>
                <h3 className="font-semibold text-neutral-800 mb-2">{messages.about.whyJapan.value.title}</h3>
                <p className="text-neutral-600">{messages.about.whyJapan.value.description}</p>
              </div>
            </div>

            <div className="flex items-start space-x-6">
              <div className="w-12 h-12 bg-secondary-100 rounded-lg flex items-center justify-center flex-shrink-0">
                <Leaf className="w-6 h-6 text-secondary-600" />
              </div>
              <div>
                <h3 className="font-semibold text-neutral-800 mb-2">{messages.about.whyJapan.sustainability.title}</h3>
                <p className="text-neutral-600">{messages.about.whyJapan.sustainability.description}</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Values Section */}
      <section className="py-20 bg-neutral-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-4 mb-16">
            <h2 className="text-3xl lg:text-4xl font-heading font-bold text-neutral-800">
              {messages.about.values.title}
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="bg-white rounded-lg p-8 text-center shadow-sm">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Shield className="w-8 h-8 text-primary-600" />
              </div>
              <h3 className="font-semibold text-neutral-800 mb-4">{messages.about.values.integrity.title}</h3>
              <p className="text-neutral-600 text-sm">{messages.about.values.integrity.description}</p>
            </div>

            <div className="bg-white rounded-lg p-8 text-center shadow-sm">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Award className="w-8 h-8 text-primary-600" />
              </div>
              <h3 className="font-semibold text-neutral-800 mb-4">{messages.about.values.quality.title}</h3>
              <p className="text-neutral-600 text-sm">{messages.about.values.quality.description}</p>
            </div>

            <div className="bg-white rounded-lg p-8 text-center shadow-sm">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Leaf className="w-8 h-8 text-primary-600" />
              </div>
              <h3 className="font-semibold text-neutral-800 mb-4">{messages.about.values.sustainability.title}</h3>
              <p className="text-neutral-600 text-sm">{messages.about.values.sustainability.description}</p>
            </div>

            <div className="bg-white rounded-lg p-8 text-center shadow-sm">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Handshake className="w-8 h-8 text-primary-600" />
              </div>
              <h3 className="font-semibold text-neutral-800 mb-4">{messages.about.values.partnership.title}</h3>
              <p className="text-neutral-600 text-sm">{messages.about.values.partnership.description}</p>
            </div>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-8">
            <h2 className="text-3xl lg:text-4xl font-heading font-bold text-neutral-800">
              {messages.about.team.title}
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              {messages.about.team.subtitle}
            </p>
            <p className="text-lg text-neutral-600 max-w-4xl mx-auto leading-relaxed">
              {messages.about.team.description}
            </p>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-to-r from-primary-700 to-primary-800 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-8">
            <h2 className="text-3xl lg:text-4xl font-heading font-bold">
              {messages.mission.readyTitle}
            </h2>
            <p className="text-xl text-primary-100 max-w-4xl mx-auto mb-8">
              {messages.mission.readySubtitle}
            </p>



            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href={`/${locale}/contact`}
                className="bg-secondary-500 hover:bg-secondary-600 text-neutral-900 px-8 py-3 rounded-lg font-semibold transition-colors duration-200 flex items-center justify-center space-x-2"
              >
                <span>{messages.homepage.contactUs}</span>
                <ArrowRight className="w-5 h-5" />
              </Link>
              <Link
                href={`/${locale}/services`}
                className="border-2 border-white text-white hover:bg-white hover:text-primary-700 px-8 py-3 rounded-lg font-semibold transition-colors duration-200"
              >
                {messages.navigation.services}
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
