'use client';

import { useState, useEffect, useCallback } from 'react';
import { ChevronDown, ChevronUp, Filter, X, Search, Bookmark } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import SavedSearchModal from '@/components/SavedSearches/SavedSearchModal';

export interface FilterOptions {
  category: string;
  searchTerm: string;
  priceRange: [number, number];
  yearRange: [number, number];
  mileageRange: [number, number];
  fuelTypes: string[];
  transmissionTypes: string[];
  bodyConditions: string[];
}

interface FilterAndSearchProps {
  locale: string;
  onCategoryChange: (category: string) => void;
  onSearchChange: (searchTerm: string) => void;
  onFiltersChange: (filters: FilterOptions) => void;
  categories: Array<{ id: string; name: string; count: number }>;
  compact?: boolean;
}

export default function FilterAndSearch({
  locale,
  onCategoryChange,
  onSearchChange,
  onFiltersChange,
  categories,
  compact = false
}: FilterAndSearchProps) {
  const [activeCategory, setActiveCategory] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [showSavedSearches, setShowSavedSearches] = useState(false);
  const { user } = useAuth();

  // Advanced filter states
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 1000000]);
  const [yearRange, setYearRange] = useState<[number, number]>([2000, 2025]);
  const [mileageRange, setMileageRange] = useState<[number, number]>([0, 200000]);
  const [selectedFuelTypes, setSelectedFuelTypes] = useState<string[]>([]);
  const [selectedTransmissionTypes, setSelectedTransmissionTypes] = useState<string[]>([]);
  const [selectedBodyConditions, setSelectedBodyConditions] = useState<string[]>([]);

  // Filter options data
  const fuelTypeOptions = ['Gasoline', 'Diesel', 'Hybrid', 'Electric', 'LPG'];
  const transmissionOptions = ['Manual', 'Automatic', 'CVT', 'Semi-Automatic'];
  const bodyConditionOptions = ['Excellent', 'Very Good', 'Good', 'Fair', 'Needs Work'];

  // Handle URL hash navigation for direct access to filter/search
  useEffect(() => {
    const hash = window.location.hash;
    if (hash === '#filter') {
      document.getElementById('filter-section')?.scrollIntoView({ behavior: 'smooth' });
    } else if (hash === '#search') {
      document.getElementById('search-section')?.scrollIntoView({ behavior: 'smooth' });
      document.getElementById('search-input')?.focus();
    }
  }, []);

  // Update filters whenever any filter state changes
  const handleFiltersChange = useCallback(() => {
    const filters: FilterOptions = {
      category: activeCategory,
      searchTerm,
      priceRange,
      yearRange,
      mileageRange,
      fuelTypes: selectedFuelTypes,
      transmissionTypes: selectedTransmissionTypes,
      bodyConditions: selectedBodyConditions,
    };
    onFiltersChange(filters);
  }, [activeCategory, searchTerm, priceRange, yearRange, mileageRange, selectedFuelTypes, selectedTransmissionTypes, selectedBodyConditions, onFiltersChange]);

  useEffect(() => {
    handleFiltersChange();
  }, [handleFiltersChange]);

  const handleCategoryClick = (categoryId: string) => {
    setActiveCategory(categoryId);
    onCategoryChange(categoryId);
  };

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearchChange(searchTerm);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    onSearchChange(value);
  };

  const handleCheckboxChange = (
    value: string,
    selectedArray: string[],
    setSelectedArray: React.Dispatch<React.SetStateAction<string[]>>
  ) => {
    if (selectedArray.includes(value)) {
      setSelectedArray(selectedArray.filter(item => item !== value));
    } else {
      setSelectedArray([...selectedArray, value]);
    }
  };

  const clearAllFilters = () => {
    setActiveCategory('all');
    setSearchTerm('');
    setPriceRange([0, 1000000]);
    setYearRange([2000, 2025]);
    setMileageRange([0, 200000]);
    setSelectedFuelTypes([]);
    setSelectedTransmissionTypes([]);
    setSelectedBodyConditions([]);
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (activeCategory !== 'all') count++;
    if (searchTerm) count++;
    if (priceRange[0] !== 0 || priceRange[1] !== 1000000) count++;
    if (yearRange[0] !== 2000 || yearRange[1] !== 2025) count++;
    if (mileageRange[0] !== 0 || mileageRange[1] !== 200000) count++;
    if (selectedFuelTypes.length > 0) count++;
    if (selectedTransmissionTypes.length > 0) count++;
    if (selectedBodyConditions.length > 0) count++;
    return count;
  };

  const getCurrentFilters = (): FilterOptions => ({
    category: activeCategory,
    searchTerm,
    priceRange,
    yearRange,
    mileageRange,
    fuelTypes: selectedFuelTypes,
    transmissionTypes: selectedTransmissionTypes,
    bodyConditions: selectedBodyConditions,
  });

  const applyFilters = (filters: FilterOptions) => {
    setActiveCategory(filters.category);
    setSearchTerm(filters.searchTerm);
    setPriceRange(filters.priceRange);
    setYearRange(filters.yearRange);
    setMileageRange(filters.mileageRange);
    setSelectedFuelTypes(filters.fuelTypes);
    setSelectedTransmissionTypes(filters.transmissionTypes);
    setSelectedBodyConditions(filters.bodyConditions);

    // Trigger the callbacks
    onCategoryChange(filters.category);
    onSearchChange(filters.searchTerm);
  };

  // Compact mode for sidebar
  if (compact) {
    return (
      <div className="space-y-4">
        {/* Price Range */}
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            {locale === 'en' ? 'Price Range (¥)' : '価格帯 (¥)'}
          </label>
          <div className="space-y-2">
            <input
              type="range"
              min="0"
              max="1000000"
              step="10000"
              value={priceRange[1]}
              onChange={(e) => setPriceRange([0, parseInt(e.target.value)])}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-neutral-600">
              <span>¥0</span>
              <span>¥{priceRange[1].toLocaleString()}</span>
            </div>
          </div>
        </div>

        {/* Year Range */}
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            {locale === 'en' ? 'Year Range' : '年式'}
          </label>
          <div className="space-y-2">
            <input
              type="range"
              min="2000"
              max="2025"
              value={yearRange[0]}
              onChange={(e) => setYearRange([parseInt(e.target.value), yearRange[1]])}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-neutral-600">
              <span>{yearRange[0]}</span>
              <span>2025</span>
            </div>
          </div>
        </div>

        {/* Clear Filters */}
        <button
          onClick={clearAllFilters}
          className="w-full text-sm text-primary-600 hover:text-primary-700 py-2 border border-primary-200 rounded-lg hover:bg-primary-50 transition-colors"
        >
          {locale === 'en' ? 'Clear Filters' : 'フィルターをクリア'}
        </button>
      </div>
    );
  }

  return (
    <section className="py-8 bg-neutral-50 border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col gap-6">
          {/* Header with Filter Toggle */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <h2 className="text-xl font-bold text-neutral-800">
              {locale === 'en' ? 'Search & Filter Stock' : '在庫検索・絞り込み'}
            </h2>
            <div className="flex items-center gap-3">
              {getActiveFiltersCount() > 0 && (
                <button
                  onClick={clearAllFilters}
                  className="flex items-center gap-2 px-3 py-2 text-sm text-neutral-600 hover:text-neutral-800 border border-neutral-300 rounded-lg hover:bg-white transition-colors"
                >
                  <X className="w-4 h-4" />
                  {locale === 'en' ? `Clear Filters (${getActiveFiltersCount()})` : `フィルタークリア (${getActiveFiltersCount()})`}
                </button>
              )}
              {user && (
                <button
                  onClick={() => setShowSavedSearches(true)}
                  className="flex items-center gap-2 px-3 py-2 text-sm text-neutral-600 hover:text-neutral-800 border border-neutral-300 rounded-lg hover:bg-white transition-colors"
                >
                  <Bookmark className="w-4 h-4" />
                  {locale === 'en' ? 'Saved Searches' : '保存済み検索'}
                </button>
              )}
              <button
                onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                className="flex items-center gap-2 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
              >
                <Filter className="w-4 h-4" />
                {locale === 'en' ? 'Advanced Filters' : '詳細フィルター'}
                {showAdvancedFilters ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
              </button>
            </div>
          </div>

          {/* Search Bar */}
          <div id="search-section" className="w-full">
            <form onSubmit={handleSearchSubmit} className="flex flex-col sm:flex-row gap-3 w-full">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-5 h-5" />
                <input
                  id="search-input"
                  type="text"
                  value={searchTerm}
                  onChange={handleSearchChange}
                  placeholder={locale === 'en' ? 'Search by make, model, year...' : 'メーカー、モデル、年式で検索...'}
                  className="w-full pl-10 pr-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-base"
                />
              </div>
              <button
                type="submit"
                className="px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-200 font-medium text-base whitespace-nowrap"
              >
                {locale === 'en' ? 'Search' : '検索'}
              </button>
            </form>
          </div>

          {/* Categories */}
          <div id="filter-section" className="w-full">
            <h3 className="text-sm font-medium text-neutral-700 mb-3">
              {locale === 'en' ? 'Categories' : 'カテゴリー'}
            </h3>
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => handleCategoryClick(category.id)}
                  className={`px-4 py-2 rounded-full border transition-colors duration-200 text-sm font-medium whitespace-nowrap ${
                    activeCategory === category.id
                      ? 'bg-primary-600 text-white border-primary-600'
                      : 'bg-white border-neutral-300 hover:border-primary-500 hover:text-primary-600'
                  }`}
                >
                  {category.name} ({category.count})
                </button>
              ))}
            </div>
          </div>

          {/* Advanced Filters */}
          {showAdvancedFilters && (
            <div className="bg-white rounded-lg border border-neutral-200 p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">

                {/* Price Range */}
                <div className="space-y-3">
                  <label className="block text-sm font-medium text-neutral-700">
                    {locale === 'en' ? 'Price Range (¥)' : '価格帯 (¥)'}
                  </label>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <input
                        type="number"
                        value={priceRange[0]}
                        onChange={(e) => setPriceRange([parseInt(e.target.value) || 0, priceRange[1]])}
                        placeholder="Min"
                        className="w-full px-3 py-2 border border-neutral-300 rounded-md text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      />
                      <span className="text-neutral-500">-</span>
                      <input
                        type="number"
                        value={priceRange[1]}
                        onChange={(e) => setPriceRange([priceRange[0], parseInt(e.target.value) || 1000000])}
                        placeholder="Max"
                        className="w-full px-3 py-2 border border-neutral-300 rounded-md text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      />
                    </div>
                    <input
                      type="range"
                      min="0"
                      max="1000000"
                      step="10000"
                      value={priceRange[0]}
                      onChange={(e) => setPriceRange([parseInt(e.target.value), priceRange[1]])}
                      className="w-full h-2 bg-neutral-200 rounded-lg appearance-none cursor-pointer slider"
                    />
                    <input
                      type="range"
                      min="0"
                      max="1000000"
                      step="10000"
                      value={priceRange[1]}
                      onChange={(e) => setPriceRange([priceRange[0], parseInt(e.target.value)])}
                      className="w-full h-2 bg-neutral-200 rounded-lg appearance-none cursor-pointer slider"
                    />
                  </div>
                </div>

                {/* Year Range */}
                <div className="space-y-3">
                  <label className="block text-sm font-medium text-neutral-700">
                    {locale === 'en' ? 'Year Range' : '年式'}
                  </label>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <input
                        type="number"
                        value={yearRange[0]}
                        onChange={(e) => setYearRange([parseInt(e.target.value) || 2000, yearRange[1]])}
                        placeholder="From"
                        min="1990"
                        max="2025"
                        className="w-full px-3 py-2 border border-neutral-300 rounded-md text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      />
                      <span className="text-neutral-500">-</span>
                      <input
                        type="number"
                        value={yearRange[1]}
                        onChange={(e) => setYearRange([yearRange[0], parseInt(e.target.value) || 2025])}
                        placeholder="To"
                        min="1990"
                        max="2025"
                        className="w-full px-3 py-2 border border-neutral-300 rounded-md text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      />
                    </div>
                    <input
                      type="range"
                      min="1990"
                      max="2025"
                      value={yearRange[0]}
                      onChange={(e) => setYearRange([parseInt(e.target.value), yearRange[1]])}
                      className="w-full h-2 bg-neutral-200 rounded-lg appearance-none cursor-pointer slider"
                    />
                    <input
                      type="range"
                      min="1990"
                      max="2025"
                      value={yearRange[1]}
                      onChange={(e) => setYearRange([yearRange[0], parseInt(e.target.value)])}
                      className="w-full h-2 bg-neutral-200 rounded-lg appearance-none cursor-pointer slider"
                    />
                  </div>
                </div>

                {/* Mileage Range */}
                <div className="space-y-3">
                  <label className="block text-sm font-medium text-neutral-700">
                    {locale === 'en' ? 'Mileage Range (km)' : '走行距離 (km)'}
                  </label>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <input
                        type="number"
                        value={mileageRange[0]}
                        onChange={(e) => setMileageRange([parseInt(e.target.value) || 0, mileageRange[1]])}
                        placeholder="Min"
                        className="w-full px-3 py-2 border border-neutral-300 rounded-md text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      />
                      <span className="text-neutral-500">-</span>
                      <input
                        type="number"
                        value={mileageRange[1]}
                        onChange={(e) => setMileageRange([mileageRange[0], parseInt(e.target.value) || 200000])}
                        placeholder="Max"
                        className="w-full px-3 py-2 border border-neutral-300 rounded-md text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      />
                    </div>
                    <input
                      type="range"
                      min="0"
                      max="200000"
                      step="5000"
                      value={mileageRange[0]}
                      onChange={(e) => setMileageRange([parseInt(e.target.value), mileageRange[1]])}
                      className="w-full h-2 bg-neutral-200 rounded-lg appearance-none cursor-pointer slider"
                    />
                    <input
                      type="range"
                      min="0"
                      max="200000"
                      step="5000"
                      value={mileageRange[1]}
                      onChange={(e) => setMileageRange([mileageRange[0], parseInt(e.target.value)])}
                      className="w-full h-2 bg-neutral-200 rounded-lg appearance-none cursor-pointer slider"
                    />
                  </div>
                </div>

                {/* Fuel Type */}
                <div className="space-y-3">
                  <label className="block text-sm font-medium text-neutral-700">
                    {locale === 'en' ? 'Fuel Type' : '燃料タイプ'}
                  </label>
                  <div className="space-y-2">
                    {fuelTypeOptions.map((fuel) => (
                      <label key={fuel} className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={selectedFuelTypes.includes(fuel)}
                          onChange={() => handleCheckboxChange(fuel, selectedFuelTypes, setSelectedFuelTypes)}
                          className="w-4 h-4 text-primary-600 border-neutral-300 rounded focus:ring-primary-500"
                        />
                        <span className="text-sm text-neutral-700">{fuel}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Transmission Type */}
                <div className="space-y-3">
                  <label className="block text-sm font-medium text-neutral-700">
                    {locale === 'en' ? 'Transmission' : 'トランスミッション'}
                  </label>
                  <div className="space-y-2">
                    {transmissionOptions.map((transmission) => (
                      <label key={transmission} className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={selectedTransmissionTypes.includes(transmission)}
                          onChange={() => handleCheckboxChange(transmission, selectedTransmissionTypes, setSelectedTransmissionTypes)}
                          className="w-4 h-4 text-primary-600 border-neutral-300 rounded focus:ring-primary-500"
                        />
                        <span className="text-sm text-neutral-700">{transmission}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Body Condition */}
                <div className="space-y-3">
                  <label className="block text-sm font-medium text-neutral-700">
                    {locale === 'en' ? 'Body Condition' : 'ボディコンディション'}
                  </label>
                  <div className="space-y-2">
                    {bodyConditionOptions.map((condition) => (
                      <label key={condition} className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={selectedBodyConditions.includes(condition)}
                          onChange={() => handleCheckboxChange(condition, selectedBodyConditions, setSelectedBodyConditions)}
                          className="w-4 h-4 text-primary-600 border-neutral-300 rounded focus:ring-primary-500"
                        />
                        <span className="text-sm text-neutral-700">{condition}</span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Saved Searches Modal */}
      <SavedSearchModal
        isOpen={showSavedSearches}
        onClose={() => setShowSavedSearches(false)}
        locale={locale}
        currentFilters={getCurrentFilters()}
        onApplySearch={applyFilters}
      />
    </section>
  );
}
