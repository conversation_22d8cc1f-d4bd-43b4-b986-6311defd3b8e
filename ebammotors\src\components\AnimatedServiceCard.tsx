'use client';

import { useState, useEffect } from 'react';
import { Car, Smartphone, Sofa, Truck, Recycle, Wrench, Bike, Home, Cog } from 'lucide-react';

interface AnimatedServiceCardProps {
  images: string[];
  iconName: string;
  title: string;
  description: string;
  interval?: number;
}

export default function AnimatedServiceCard({
  images,
  iconName,
  title,
  description,
  interval = 3500
}: AnimatedServiceCardProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isHovered, setIsHovered] = useState(false);

  useEffect(() => {
    if (images.length <= 1 || isHovered) return;

    const timer = setInterval(() => {
      setCurrentImageIndex(prev => (prev + 1) % images.length);
    }, interval);

    return () => clearInterval(timer);
  }, [images.length, interval, isHovered]);

  const currentImage = images.length > 0 ? images[currentImageIndex] : '';

  // Get the appropriate icon component
  const getIcon = () => {
    switch (iconName) {
      case 'Car': return Car;
      case 'Wrench': return Wrench;
      case 'Cog': return Cog;
      case 'Smartphone': return Smartphone;
      case 'Sofa': return Sofa;
      case 'Bike': return Bike;
      case 'Truck': return Truck;
      case 'Home': return Home;
      case 'Recycle': return Recycle;
      default: return Car;
    }
  };

  const Icon = getIcon();

  return (
    <div
      className="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 group hover:scale-105 hover:-translate-y-2 border border-neutral-100"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="relative h-48 overflow-hidden">
        {/* Background Image */}
        <div
          className="absolute inset-0 bg-cover bg-center transition-all duration-700 group-hover:scale-110"
          style={{
            backgroundImage: `url('${currentImage}')`
          }}
        />

        {/* Enhanced Overlay with gradient */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-black/10 to-transparent group-hover:from-black/60 group-hover:via-black/20 transition-all duration-500" />

        {/* Image Counter with better styling */}
        {images.length > 1 && (
          <div className="absolute top-3 right-3 bg-black/80 backdrop-blur-sm text-white px-3 py-1.5 rounded-full text-xs font-medium opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
            {currentImageIndex + 1}/{images.length}
          </div>
        )}

        {/* Enhanced Image Indicators */}
        {images.length > 1 && (
          <div className="absolute bottom-3 left-1/2 transform -translate-x-1/2 flex space-x-2 opacity-0 group-hover:opacity-100 transition-all duration-300">
            {images.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentImageIndex(index)}
                className={`w-2 h-2 rounded-full transition-all duration-300 ${
                  index === currentImageIndex
                    ? 'bg-white scale-125 shadow-lg'
                    : 'bg-white/60 hover:bg-white/90 hover:scale-110'
                }`}
                aria-label={`Go to image ${index + 1}`}
              />
            ))}
          </div>
        )}

        {/* Floating action hint */}
        <div className="absolute top-3 left-3 opacity-0 group-hover:opacity-100 transition-all duration-300 transform -translate-y-2 group-hover:translate-y-0">
          <div className="bg-white/90 backdrop-blur-sm text-neutral-700 px-2 py-1 rounded-lg text-xs font-medium">
            View Details
          </div>
        </div>
      </div>

      <div className="p-6 text-center relative">
        {/* Background decoration */}
        <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-primary-500 to-secondary-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500"></div>

        {iconName && (
          <div className="w-14 h-14 bg-gradient-to-br from-primary-100 to-primary-200 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:from-primary-200 group-hover:to-primary-300 transition-all duration-300 group-hover:scale-110 group-hover:rotate-3">
            <Icon className="w-7 h-7 text-primary-600 group-hover:scale-110 transition-transform duration-300" />
          </div>
        )}

        <h3 className="font-bold text-neutral-800 mb-3 text-lg group-hover:text-primary-600 transition-colors duration-300">
          {title}
        </h3>

        <p className="text-neutral-600 leading-relaxed group-hover:text-neutral-700 transition-colors duration-300">
          {description}
        </p>

        {/* Hover CTA */}
        <div className="mt-4 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
          <div className="inline-flex items-center space-x-2 text-primary-600 font-semibold text-sm">
            <span>Learn More</span>
            <svg className="w-4 h-4 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </div>
        </div>
      </div>
    </div>
  );
}
