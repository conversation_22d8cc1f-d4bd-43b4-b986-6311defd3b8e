'use client';

import { useState } from 'react';
import { FileText, Download, Eye, CheckCircle, Calendar, User, MapPin, Wrench, Shield } from 'lucide-react';

interface StockItem {
  id: number;
  category: string;
  title: string;
  price: string;
  location: string;
  status: string;
  image: string;
  images: string[];
  specs: string[];
  carId: string;
  year?: number;
  mileage?: number;
  fuelType?: string;
  transmission?: string;
  bodyCondition?: string;
}

interface VehicleHistoryProps {
  vehicle: StockItem;
  locale: string;
}

export default function VehicleHistory({ vehicle, locale }: VehicleHistoryProps) {
  const [selectedReport, setSelectedReport] = useState<string | null>(null);

  // Generate vehicle history data
  const generateHistory = () => {
    const currentYear = new Date().getFullYear();
    const vehicleYear = vehicle.year || 2010;
    const yearsOwned = currentYear - vehicleYear;
    
    return {
      previousOwners: Math.min(Math.floor(yearsOwned / 3) + 1, 3),
      serviceRecords: Math.floor(yearsOwned * 1.5) + 2,
      accidentHistory: vehicle.bodyCondition === 'Excellent' ? 'None' : vehicle.bodyCondition === 'Needs Work' ? 'Minor' : 'None',
      lastInspection: '2024-01-15',
      registrationHistory: [
        { date: '2024-01-15', type: 'Inspection', status: 'Passed', location: 'Tokyo' },
        { date: '2023-06-20', type: 'Registration Renewal', status: 'Completed', location: 'Tokyo' },
        { date: '2023-01-10', type: 'Inspection', status: 'Passed', location: 'Tokyo' },
      ]
    };
  };

  const history = generateHistory();

  const reports = [
    {
      id: 'inspection',
      title: locale === 'en' ? 'Vehicle Inspection Report' : '車両検査報告書',
      date: '2024-01-15',
      status: 'passed',
      description: locale === 'en' ? 'Comprehensive 150-point inspection' : '包括的な150項目検査',
      icon: CheckCircle,
    },
    {
      id: 'history',
      title: locale === 'en' ? 'Ownership History' : '所有者履歴',
      date: '2024-01-10',
      status: 'available',
      description: locale === 'en' ? 'Complete ownership and registration history' : '完全な所有者・登録履歴',
      icon: User,
    },
    {
      id: 'maintenance',
      title: locale === 'en' ? 'Maintenance Records' : 'メンテナンス記録',
      date: '2024-01-05',
      status: 'available',
      description: locale === 'en' ? 'Service and maintenance history' : 'サービス・メンテナンス履歴',
      icon: Wrench,
    },
    {
      id: 'export',
      title: locale === 'en' ? 'Export Certification' : '輸出証明書',
      date: '2024-01-20',
      status: 'ready',
      description: locale === 'en' ? 'Export readiness and documentation' : '輸出準備と書類',
      icon: Shield,
    },
  ];

  const downloadReport = (reportId: string) => {
    // Simulate report download
    const reportName = reports.find(r => r.id === reportId)?.title || 'Report';
    alert(`${locale === 'en' ? 'Downloading' : 'ダウンロード中'}: ${reportName}`);
  };

  const viewReport = (reportId: string) => {
    setSelectedReport(reportId);
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-white rounded-lg p-6 border">
        <h2 className="text-2xl font-bold text-neutral-800 mb-2">
          {locale === 'en' ? 'Vehicle History & Reports' : '車両履歴・レポート'}
        </h2>
        <p className="text-neutral-600">
          {locale === 'en' 
            ? 'Complete history, inspection reports, and documentation for this vehicle.'
            : 'この車両の完全な履歴、検査報告書、書類。'
          }
        </p>
      </div>

      {/* Quick Summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg p-4 border text-center">
          <div className="text-2xl font-bold text-primary-600">{history.previousOwners}</div>
          <div className="text-sm text-neutral-600">
            {locale === 'en' ? 'Previous Owners' : '前所有者数'}
          </div>
        </div>
        <div className="bg-white rounded-lg p-4 border text-center">
          <div className="text-2xl font-bold text-green-600">{history.serviceRecords}</div>
          <div className="text-sm text-neutral-600">
            {locale === 'en' ? 'Service Records' : 'サービス記録'}
          </div>
        </div>
        <div className="bg-white rounded-lg p-4 border text-center">
          <div className="text-2xl font-bold text-blue-600">
            {history.accidentHistory === 'None' ? '0' : '1'}
          </div>
          <div className="text-sm text-neutral-600">
            {locale === 'en' ? 'Accidents' : '事故歴'}
          </div>
        </div>
        <div className="bg-white rounded-lg p-4 border text-center">
          <div className="text-2xl font-bold text-purple-600">✓</div>
          <div className="text-sm text-neutral-600">
            {locale === 'en' ? 'Export Ready' : '輸出準備完了'}
          </div>
        </div>
      </div>

      {/* Available Reports */}
      <div className="bg-white rounded-lg border">
        <div className="px-6 py-4 border-b bg-neutral-50">
          <h3 className="text-lg font-semibold text-neutral-800 flex items-center space-x-2">
            <FileText className="w-5 h-5 text-primary-600" />
            <span>{locale === 'en' ? 'Available Reports' : '利用可能なレポート'}</span>
          </h3>
        </div>
        
        <div className="p-6">
          <div className="space-y-4">
            {reports.map((report) => {
              const Icon = report.icon;
              return (
                <div key={report.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-neutral-50 transition-colors">
                  <div className="flex items-center space-x-4">
                    <div className={`p-2 rounded-lg ${
                      report.status === 'passed' ? 'bg-green-100' :
                      report.status === 'ready' ? 'bg-blue-100' : 'bg-neutral-100'
                    }`}>
                      <Icon className={`w-5 h-5 ${
                        report.status === 'passed' ? 'text-green-600' :
                        report.status === 'ready' ? 'text-blue-600' : 'text-neutral-600'
                      }`} />
                    </div>
                    <div>
                      <h4 className="font-semibold text-neutral-800">{report.title}</h4>
                      <p className="text-sm text-neutral-600">{report.description}</p>
                      <p className="text-xs text-neutral-500 mt-1">
                        {locale === 'en' ? 'Updated' : '更新日'}: {report.date}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => viewReport(report.id)}
                      className="p-2 text-neutral-600 hover:text-primary-600 transition-colors"
                      title={locale === 'en' ? 'View Report' : 'レポートを表示'}
                    >
                      <Eye className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => downloadReport(report.id)}
                      className="p-2 text-neutral-600 hover:text-primary-600 transition-colors"
                      title={locale === 'en' ? 'Download Report' : 'レポートをダウンロード'}
                    >
                      <Download className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Registration History Timeline */}
      <div className="bg-white rounded-lg border">
        <div className="px-6 py-4 border-b bg-neutral-50">
          <h3 className="text-lg font-semibold text-neutral-800 flex items-center space-x-2">
            <Calendar className="w-5 h-5 text-primary-600" />
            <span>{locale === 'en' ? 'Registration Timeline' : '登録履歴タイムライン'}</span>
          </h3>
        </div>
        
        <div className="p-6">
          <div className="space-y-6">
            {history.registrationHistory.map((record, index) => (
              <div key={index} className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <div className="w-3 h-3 bg-primary-600 rounded-full mt-2"></div>
                  {index < history.registrationHistory.length - 1 && (
                    <div className="w-px h-12 bg-neutral-200 ml-1 mt-1"></div>
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h4 className="font-semibold text-neutral-800">{record.type}</h4>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      record.status === 'Passed' || record.status === 'Completed'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {record.status}
                    </span>
                  </div>
                  <div className="flex items-center space-x-4 mt-1 text-sm text-neutral-600">
                    <span className="flex items-center space-x-1">
                      <Calendar className="w-3 h-3" />
                      <span>{record.date}</span>
                    </span>
                    <span className="flex items-center space-x-1">
                      <MapPin className="w-3 h-3" />
                      <span>{record.location}</span>
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Report Viewer Modal */}
      {selectedReport && (
        <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-6 border-b">
              <h3 className="text-lg font-semibold text-neutral-800">
                {reports.find(r => r.id === selectedReport)?.title}
              </h3>
              <button
                onClick={() => setSelectedReport(null)}
                className="text-neutral-600 hover:text-neutral-800"
              >
                ×
              </button>
            </div>
            
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
              <div className="text-center py-12">
                <FileText className="w-16 h-16 text-neutral-400 mx-auto mb-4" />
                <h4 className="text-lg font-semibold text-neutral-800 mb-2">
                  {locale === 'en' ? 'Report Preview' : 'レポートプレビュー'}
                </h4>
                <p className="text-neutral-600 mb-6">
                  {locale === 'en' 
                    ? 'This is a preview of the report. The full report contains detailed information about the vehicle.'
                    : 'これはレポートのプレビューです。完全なレポートには車両の詳細情報が含まれています。'
                  }
                </p>
                <button
                  onClick={() => downloadReport(selectedReport)}
                  className="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors"
                >
                  {locale === 'en' ? 'Download Full Report' : '完全なレポートをダウンロード'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
