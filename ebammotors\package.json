{"name": "ebam-motors-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "migrate-cars": "npx tsx scripts/migrate-to-database.ts", "migrate-cars-dry": "npx tsx scripts/migrate-to-database.ts --dry-run", "migrate-cars-verbose": "npx tsx scripts/migrate-to-database.ts --verbose", "create-schema": "node scripts/create-schema.js"}, "dependencies": {"@headlessui/react": "^2.2.4", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/nodemailer": "^6.4.17", "@types/uuid": "^10.0.0", "@vercel/postgres": "^0.10.0", "bcryptjs": "^3.0.2", "dotenv": "^17.0.0", "html2canvas": "^1.4.1", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "lucide-react": "^0.516.0", "next": "15.3.3", "nodemailer": "^7.0.3", "react": "^19.0.0", "react-dom": "^19.0.0", "resend": "^4.6.0", "stripe": "^18.2.1", "tsx": "^4.19.2", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}