import { notFound } from 'next/navigation';

const locales = ['en', 'ja'] as const;
type Locale = typeof locales[number];

// Import messages statically to avoid dynamic import issues with Turbopack
import enMessages from '../../messages/en.json';
import jaMessages from '../../messages/ja.json';

const messages = {
  en: enMessages,
  ja: jaMessages,
} as const;

export async function getMessages(locale: string) {
  if (!locales.includes(locale as Locale)) {
    notFound();
  }

  return messages[locale as Locale];
}

export function getMessagesSync(locale: string) {
  if (!locales.includes(locale as Locale)) {
    return messages.en; // fallback to English
  }

  return messages[locale as Locale];
}
