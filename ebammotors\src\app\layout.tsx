import type { Metadata } from "next";
import { Inter, Poppins } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "@/contexts/AuthContext";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

const poppins = Poppins({
  variable: "--font-poppins",
  subsets: ["latin"],
  weight: ["400", "500", "600", "700", "800"],
});

export const metadata: Metadata = {
  title: "EBAM MOTORS - Quality Used Goods from Japan to Ghana",
  description: "EBAM Motors specializes in sourcing, recycling, and exporting high-demand used goods including automobiles, electronics, furniture from Japan to Ghana and Africa.",
  keywords: "used cars Japan, export Ghana, EBAM Motors, Japanese cars, used electronics, furniture export, trading company, sustainable trade",
  authors: [{ name: "EBAM MOTORS" }],
  creator: "EBAM MOTORS",
  publisher: "EBAM MOTORS",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://ebammotors.com',
    title: 'EBAM MOTORS - Quality Used Goods from Japan to Ghana',
    description: 'EBAM Motors specializes in sourcing, recycling, and exporting high-demand used goods including automobiles, electronics, furniture from Japan to Ghana and Africa.',
    siteName: 'EBAM MOTORS',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'EBAM MOTORS - Quality Used Goods from Japan to Ghana',
    description: 'EBAM Motors specializes in sourcing, recycling, and exporting high-demand used goods including automobiles, electronics, furniture from Japan to Ghana and Africa.',
  },
  verification: {
    google: 'your-google-verification-code',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={`${inter.variable} ${poppins.variable}`}>
      <body
        className="font-inter antialiased"
        suppressHydrationWarning={true}
      >
        <AuthProvider>
          {children}
        </AuthProvider>
      </body>
    </html>
  );
}
