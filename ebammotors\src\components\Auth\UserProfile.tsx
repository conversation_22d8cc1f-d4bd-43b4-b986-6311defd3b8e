'use client';

import { useState } from 'react';
import { User, <PERSON>ting<PERSON>, Heart, Search, ShoppingBag, Star, Bell, LogOut, Edit, Save, X } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

interface UserProfileProps {
  locale: string;
  onClose: () => void;
}

export default function UserProfile({ locale, onClose }: UserProfileProps) {
  const { user, logout, updateProfile, favorites, savedSearches, purchaseHistory } = useAuth();
  const [activeTab, setActiveTab] = useState<'profile' | 'favorites' | 'searches' | 'orders' | 'loyalty' | 'settings'>('profile');
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState({
    name: user?.name || '',
    phone: user?.phone || '',
    location: user?.location || '',
  });

  if (!user) return null;

  const handleSaveProfile = async () => {
    const success = await updateProfile(editData);
    if (success) {
      setIsEditing(false);
    }
  };

  const tabs = [
    { id: 'profile', label: locale === 'en' ? 'Profile' : 'プロフィール', icon: User },
    { id: 'favorites', label: locale === 'en' ? 'Favorites' : 'お気に入り', icon: Heart, count: favorites.length },
    { id: 'searches', label: locale === 'en' ? 'Saved Searches' : '保存済み検索', icon: Search, count: savedSearches.length },
    { id: 'orders', label: locale === 'en' ? 'Orders' : '注文履歴', icon: ShoppingBag, count: purchaseHistory.length },
    { id: 'loyalty', label: locale === 'en' ? 'Loyalty' : 'ロイヤルティ', icon: Star },
    { id: 'settings', label: locale === 'en' ? 'Settings' : '設定', icon: Settings },
  ];

  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'Bronze': return 'text-amber-600 bg-amber-100';
      case 'Silver': return 'text-gray-600 bg-gray-100';
      case 'Gold': return 'text-yellow-600 bg-yellow-100';
      case 'Platinum': return 'text-purple-600 bg-purple-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b bg-gradient-to-r from-primary-600 to-primary-700 text-white">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
              <User className="w-6 h-6" />
            </div>
            <div>
              <h2 className="text-xl font-bold">{user.name}</h2>
              <div className="flex items-center space-x-2">
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTierColor(user.membershipTier)}`}>
                  {user.membershipTier}
                </span>
                <span className="text-primary-100 text-sm">
                  {user.loyaltyPoints} {locale === 'en' ? 'points' : 'ポイント'}
                </span>
              </div>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-white/80 hover:text-white transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <div className="flex">
          {/* Sidebar */}
          <div className="w-64 border-r bg-neutral-50 p-4">
            <nav className="space-y-2">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`w-full flex items-center justify-between p-3 rounded-lg text-left transition-colors ${
                      activeTab === tab.id
                        ? 'bg-primary-100 text-primary-700'
                        : 'text-neutral-600 hover:bg-neutral-100'
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <Icon className="w-5 h-5" />
                      <span className="font-medium">{tab.label}</span>
                    </div>
                    {tab.count !== undefined && (
                      <span className="bg-primary-600 text-white text-xs px-2 py-1 rounded-full">
                        {tab.count}
                      </span>
                    )}
                  </button>
                );
              })}
              
              <button
                onClick={logout}
                className="w-full flex items-center space-x-3 p-3 rounded-lg text-left text-red-600 hover:bg-red-50 transition-colors mt-4"
              >
                <LogOut className="w-5 h-5" />
                <span className="font-medium">{locale === 'en' ? 'Sign Out' : 'サインアウト'}</span>
              </button>
            </nav>
          </div>

          {/* Content */}
          <div className="flex-1 p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
            {activeTab === 'profile' && (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-neutral-800">
                    {locale === 'en' ? 'Profile Information' : 'プロフィール情報'}
                  </h3>
                  {!isEditing ? (
                    <button
                      onClick={() => setIsEditing(true)}
                      className="flex items-center space-x-2 text-primary-600 hover:text-primary-700 transition-colors"
                    >
                      <Edit className="w-4 h-4" />
                      <span>{locale === 'en' ? 'Edit' : '編集'}</span>
                    </button>
                  ) : (
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={handleSaveProfile}
                        className="flex items-center space-x-2 bg-primary-600 text-white px-3 py-1 rounded-lg hover:bg-primary-700 transition-colors"
                      >
                        <Save className="w-4 h-4" />
                        <span>{locale === 'en' ? 'Save' : '保存'}</span>
                      </button>
                      <button
                        onClick={() => {
                          setIsEditing(false);
                          setEditData({
                            name: user.name,
                            phone: user.phone || '',
                            location: user.location || '',
                          });
                        }}
                        className="text-neutral-600 hover:text-neutral-800 transition-colors"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-2">
                      {locale === 'en' ? 'Full Name' : '氏名'}
                    </label>
                    {isEditing ? (
                      <input
                        type="text"
                        value={editData.name}
                        onChange={(e) => setEditData(prev => ({ ...prev, name: e.target.value }))}
                        className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      />
                    ) : (
                      <p className="text-neutral-800">{user.name}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-2">
                      {locale === 'en' ? 'Email Address' : 'メールアドレス'}
                    </label>
                    <p className="text-neutral-800">{user.email}</p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-2">
                      {locale === 'en' ? 'Phone Number' : '電話番号'}
                    </label>
                    {isEditing ? (
                      <input
                        type="tel"
                        value={editData.phone}
                        onChange={(e) => setEditData(prev => ({ ...prev, phone: e.target.value }))}
                        className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                        placeholder={locale === 'en' ? 'Enter phone number' : '電話番号を入力'}
                      />
                    ) : (
                      <p className="text-neutral-800">{user.phone || (locale === 'en' ? 'Not provided' : '未設定')}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-2">
                      {locale === 'en' ? 'Location' : '所在地'}
                    </label>
                    {isEditing ? (
                      <input
                        type="text"
                        value={editData.location}
                        onChange={(e) => setEditData(prev => ({ ...prev, location: e.target.value }))}
                        className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                        placeholder={locale === 'en' ? 'Enter location' : '所在地を入力'}
                      />
                    ) : (
                      <p className="text-neutral-800">{user.location || (locale === 'en' ? 'Not provided' : '未設定')}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-2">
                      {locale === 'en' ? 'Member Since' : '会員登録日'}
                    </label>
                    <p className="text-neutral-800">
                      {new Date(user.joinDate).toLocaleDateString(locale === 'en' ? 'en-US' : 'ja-JP')}
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-2">
                      {locale === 'en' ? 'Membership Tier' : '会員ランク'}
                    </label>
                    <span className={`inline-flex px-3 py-1 rounded-full text-sm font-medium ${getTierColor(user.membershipTier)}`}>
                      {user.membershipTier}
                    </span>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'favorites' && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-neutral-800">
                  {locale === 'en' ? 'Favorite Vehicles' : 'お気に入り車両'}
                </h3>
                {favorites.length === 0 ? (
                  <div className="text-center py-8">
                    <Heart className="w-12 h-12 text-neutral-300 mx-auto mb-4" />
                    <p className="text-neutral-600">
                      {locale === 'en' ? 'No favorites yet' : 'お気に入りはまだありません'}
                    </p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {favorites.map((favorite) => (
                      <div key={favorite.id} className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-neutral-800">Vehicle ID: {favorite.vehicleId}</p>
                            <p className="text-sm text-neutral-600">
                              {locale === 'en' ? 'Added' : '追加日'}: {new Date(favorite.addedAt).toLocaleDateString()}
                            </p>
                            {favorite.notes && (
                              <p className="text-sm text-neutral-600 mt-1">{favorite.notes}</p>
                            )}
                          </div>
                          <button className="text-red-600 hover:text-red-700 transition-colors">
                            <Heart className="w-5 h-5 fill-current" />
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {activeTab === 'searches' && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-neutral-800">
                  {locale === 'en' ? 'Saved Searches' : '保存済み検索'}
                </h3>
                {savedSearches.length === 0 ? (
                  <div className="text-center py-8">
                    <Search className="w-12 h-12 text-neutral-300 mx-auto mb-4" />
                    <p className="text-neutral-600">
                      {locale === 'en' ? 'No saved searches yet' : '保存済み検索はまだありません'}
                    </p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {savedSearches.map((search) => (
                      <div key={search.id} className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-neutral-800">{search.name}</p>
                            <p className="text-sm text-neutral-600">
                              {locale === 'en' ? 'Created' : '作成日'}: {new Date(search.createdAt).toLocaleDateString()}
                            </p>
                          </div>
                          <button className="text-primary-600 hover:text-primary-700 transition-colors">
                            {locale === 'en' ? 'Use Search' : '検索を使用'}
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {activeTab === 'orders' && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-neutral-800">
                  {locale === 'en' ? 'Purchase History' : '購入履歴'}
                </h3>
                {purchaseHistory.length === 0 ? (
                  <div className="text-center py-8">
                    <ShoppingBag className="w-12 h-12 text-neutral-300 mx-auto mb-4" />
                    <p className="text-neutral-600">
                      {locale === 'en' ? 'No orders yet' : '注文履歴はまだありません'}
                    </p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {purchaseHistory.map((order) => (
                      <div key={order.id} className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-neutral-800">{order.vehicleTitle}</p>
                            <p className="text-lg font-bold text-primary-600">{order.price}</p>
                            <p className="text-sm text-neutral-600">
                              {locale === 'en' ? 'Ordered' : '注文日'}: {new Date(order.orderDate).toLocaleDateString()}
                            </p>
                          </div>
                          <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                            order.status === 'delivered' ? 'bg-green-100 text-green-800' :
                            order.status === 'shipped' ? 'bg-blue-100 text-blue-800' :
                            order.status === 'confirmed' ? 'bg-yellow-100 text-yellow-800' :
                            order.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {order.status}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {activeTab === 'loyalty' && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-neutral-800">
                  {locale === 'en' ? 'Loyalty Program' : 'ロイヤルティプログラム'}
                </h3>
                
                <div className="bg-gradient-to-r from-primary-50 to-primary-100 rounded-lg p-6">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-primary-600 mb-2">{user.loyaltyPoints}</div>
                    <div className="text-primary-700 font-medium">
                      {locale === 'en' ? 'Available Points' : '利用可能ポイント'}
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 border rounded-lg">
                    <h4 className="font-semibold text-neutral-800 mb-2">
                      {locale === 'en' ? 'Earn Points' : 'ポイント獲得'}
                    </h4>
                    <ul className="text-sm text-neutral-600 space-y-1">
                      <li>• {locale === 'en' ? '1 point per ¥1,000 spent' : '¥1,000につき1ポイント'}</li>
                      <li>• {locale === 'en' ? '5 points for adding favorites' : 'お気に入り追加で5ポイント'}</li>
                      <li>• {locale === 'en' ? '100 points for referrals' : '紹介で100ポイント'}</li>
                    </ul>
                  </div>

                  <div className="p-4 border rounded-lg">
                    <h4 className="font-semibold text-neutral-800 mb-2">
                      {locale === 'en' ? 'Redeem Points' : 'ポイント交換'}
                    </h4>
                    <ul className="text-sm text-neutral-600 space-y-1">
                      <li>• {locale === 'en' ? '1000 points = ¥1,000 discount' : '1000ポイント = ¥1,000割引'}</li>
                      <li>• {locale === 'en' ? '500 points = Free inspection' : '500ポイント = 無料検査'}</li>
                      <li>• {locale === 'en' ? '2000 points = Priority shipping' : '2000ポイント = 優先配送'}</li>
                    </ul>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'settings' && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-neutral-800">
                  {locale === 'en' ? 'Account Settings' : 'アカウント設定'}
                </h3>
                
                <div className="space-y-4">
                  <div className="p-4 border rounded-lg">
                    <h4 className="font-semibold text-neutral-800 mb-3 flex items-center space-x-2">
                      <Bell className="w-5 h-5" />
                      <span>{locale === 'en' ? 'Notification Preferences' : '通知設定'}</span>
                    </h4>
                    <div className="space-y-3">
                      {Object.entries(user.preferences.notifications).map(([key, value]) => (
                        <label key={key} className="flex items-center justify-between">
                          <span className="text-neutral-700">
                            {key === 'email' ? (locale === 'en' ? 'Email Notifications' : 'メール通知') :
                             key === 'sms' ? (locale === 'en' ? 'SMS Notifications' : 'SMS通知') :
                             key === 'push' ? (locale === 'en' ? 'Push Notifications' : 'プッシュ通知') :
                             key === 'marketing' ? (locale === 'en' ? 'Marketing Emails' : 'マーケティングメール') : key}
                          </span>
                          <input
                            type="checkbox"
                            checked={value}
                            onChange={() => {
                              updateProfile({
                                preferences: {
                                  ...user.preferences,
                                  notifications: {
                                    ...user.preferences.notifications,
                                    [key]: !value,
                                  },
                                },
                              });
                            }}
                            className="w-4 h-4 text-primary-600 border-neutral-300 rounded focus:ring-primary-500"
                          />
                        </label>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
