// Chatbot Configuration for Controlling Hallucination
export const CHATBOT_CONFIG = {
  // Anti-hallucination settings
  antiHallucination: {
    // Model parameters to reduce hallucination
    temperature: 0.3,           // Lower = more focused, less creative (0.0-1.0)
    topP: 0.8,                 // Nucleus sampling - more focused (0.0-1.0)
    maxTokens: 200,            // Limit response length
    frequencyPenalty: 0.5,     // Reduce repetition (0.0-2.0)
    presencePenalty: 0.3,      // Encourage staying on topic (0.0-2.0)
    
    // Stop sequences to prevent rambling
    stopSequences: ["User:", "Human:", "Customer:", "\n\nUser:", "\n\nHuman:"],
    
    // Validation patterns
    suspiciousPatterns: [
      /¥[0-9,]+(?!,000)/g,                                    // Invalid price formats
      /Toyota [A-Z][a-z]+(?!Voxy|Noah|Sienta|Yaris|Vitz)/g, // Unknown Toyota models
      /Honda [A-Z][a-z]+/g,                                   // Specific Honda models
      /Nissan [A-Z][a-z]+/g,                                  // Specific Nissan models
      /\$[0-9,]+/g,                                           // Dollar prices
      /[0-9]+ years? warranty/gi,                             // Warranty claims
      /guaranteed|promise|100%/gi,                            // Absolute guarantees
      /free shipping|no cost/gi,                              // Free shipping claims
      /[0-9]+ days? delivery/gi,                              // Specific delivery times
    ]
  },

  // Exact inventory data - NEVER change these without updating actual inventory
  inventory: {
    cars: [
      { model: 'Toyota Voxy', seats: 8, price: 300000, status: 'available' },
      { model: 'Toyota Noah', seats: 8, price: 350000, status: 'available' },
      { model: 'Toyota Sienta', seats: 7, price: 320000, status: 'available' },
      { model: 'Toyota Yaris', seats: 5, price: 550000, status: 'available' },
      { model: 'Toyota Vitz', seats: 5, price: 325000, status: 'available' },
    ],
    
    // Valid price formats
    validPrices: ['¥300,000', '¥350,000', '¥320,000', '¥550,000', '¥325,000'],
    
    // Valid model names
    validModels: ['Voxy', 'Noah', 'Sienta', 'Yaris', 'Vitz'],
    
    // Cheapest car
    cheapestCar: { model: 'Toyota Voxy', price: '¥300,000' }
  },

  // Company information - EXACT details only
  company: {
    name: 'EBAM Motors',
    location: 'Kumasi, Ghana',
    whatsapp: '+************',
    whatsappChannel: 'https://whatsapp.com/channel/0029Vb5sDlFDTkK191T6Ut1G',
    
    services: [
      'Used Cars (Toyota, Honda, Nissan)',
      'Electronics (appliances, phones, laptops)',
      'Furniture (dining sets, office furniture)',
      'Bicycles (city bikes, mountain bikes)',
      'Heavy Equipment',
      'Household Items'
    ]
  },

  // Response guidelines
  responseGuidelines: {
    // When to admit uncertainty
    uncertaintyPhrases: [
      "I don't have that specific information in our current inventory",
      "Let me connect you with our team for detailed information about that",
      "For the most accurate details, please contact us directly",
      "I'd recommend speaking with our specialists for specific technical details"
    ],
    
    // Safe fallback responses
    fallbackResponses: {
      unknownCar: "I can only provide information about our current inventory: Toyota Voxy, Noah, Sienta, Yaris, and Vitz. For other models, please contact our team.",
      unknownPrice: "For the most current pricing and availability, please contact us via WhatsApp at +************.",
      technicalSpecs: "For detailed technical specifications, I'd recommend speaking directly with our team who can provide comprehensive information.",
      shipping: "Shipping costs and timeframes vary by destination. Please contact us for a personalized quote."
    },
    
    // Maximum response length
    maxResponseLength: 300,
    
    // Required disclaimers for certain topics
    disclaimers: {
      pricing: "*Prices are FOB Japan and subject to change. Contact us for current rates.*",
      shipping: "*Shipping costs and times vary by destination.*",
      availability: "*Availability subject to change. Please confirm before ordering.*"
    }
  },

  // Monitoring and logging
  monitoring: {
    logSuspiciousResponses: true,
    flagPotentialHallucinations: true,
    requireHumanReview: false  // Set to true for extra safety
  }
};

// Helper functions
export const getCheapestCar = () => CHATBOT_CONFIG.inventory.cheapestCar;

export const isValidCarModel = (model: string): boolean => {
  return CHATBOT_CONFIG.inventory.validModels.some(validModel => 
    model.toLowerCase().includes(validModel.toLowerCase())
  );
};

export const isValidPrice = (price: string): boolean => {
  return CHATBOT_CONFIG.inventory.validPrices.includes(price);
};

export const getUncertaintyResponse = (): string => {
  const phrases = CHATBOT_CONFIG.responseGuidelines.uncertaintyPhrases;
  return phrases[Math.floor(Math.random() * phrases.length)];
};

export const validateResponse = (response: string): { isValid: boolean; issues: string[] } => {
  const issues: string[] = [];
  const config = CHATBOT_CONFIG.antiHallucination;
  
  // Check for suspicious patterns
  config.suspiciousPatterns.forEach((pattern, index) => {
    if (pattern.test(response)) {
      issues.push(`Suspicious pattern detected: ${pattern.source}`);
    }
  });
  
  // Check response length
  if (response.length > CHATBOT_CONFIG.responseGuidelines.maxResponseLength) {
    issues.push('Response too long');
  }
  
  return {
    isValid: issues.length === 0,
    issues
  };
};
