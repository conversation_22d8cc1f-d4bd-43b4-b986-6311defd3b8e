'use client';

import { useState, useEffect, useCallback } from 'react';
import Image from 'next/image';
import { Star, Check, X, Calendar, Mail, MapPin } from 'lucide-react';

interface Review {
  id: string;
  name: string;
  location: string;
  email: string;
  rating: number;
  title: string;
  review: string;
  vehiclePurchased: string;
  purchaseDate: string;
  submittedAt: string;
  status: 'pending' | 'approved' | 'rejected';
  images: string[];
}

export default function AdminReviewsPage() {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'pending' | 'approved' | 'rejected'>('pending');
  const [adminKey, setAdminKey] = useState('');
  const [authenticated, setAuthenticated] = useState(false);


  const fetchReviews = useCallback(async () => {
    try {
      // Ensure we're on the client side before accessing localStorage
      if (typeof window === 'undefined') {
        setLoading(false);
        return;
      }

      const token = localStorage.getItem('admin_token');
      const response = await fetch('/api/reviews', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.status === 401) {
        // Token expired or invalid
        setAuthenticated(false);
        localStorage.removeItem('admin_token');
        return;
      }

      const data = await response.json();
      setReviews(data.reviews || []);
    } catch (error) {
      console.error('Error fetching reviews:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    if (authenticated) {
      fetchReviews();
    }
  }, [authenticated, fetchReviews]);

  const handleAuth = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const response = await fetch('/api/admin/auth', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ password: adminKey }),
      });

      const data = await response.json();

      if (data.success) {
        // Store the JWT token for API calls
        if (typeof window !== 'undefined') {
          localStorage.setItem('admin_token', data.token);
        }
        setAuthenticated(true);
      } else {
        if (data.remainingAttempts !== undefined) {
          alert(`Invalid admin password. ${data.remainingAttempts} attempts remaining.`);
        } else if (data.lockoutTime) {
          const minutes = Math.ceil(data.lockoutTime / 60000);
          alert(`Too many failed attempts. Please try again in ${minutes} minutes.`);
        } else {
          alert('Invalid admin password');
        }
      }
    } catch (error) {
      console.error('Authentication error:', error);
      alert('Authentication failed. Please try again.');
    }
  };

  const moderateReview = async (reviewId: string, status: 'approved' | 'rejected') => {
    try {
      const response = await fetch('/api/reviews', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reviewId,
          status,
          adminKey
        }),
      });

      if (response.ok) {
        await fetchReviews(); // Refresh the list
      } else {
        alert('Failed to moderate review');
      }
    } catch (error) {
      console.error('Error moderating review:', error);
      alert('Error moderating review');
    }
  };



  const filteredReviews = reviews.filter(review => {
    if (filter === 'all') return true;
    return review.status === filter;
  });

  if (!authenticated) {
    return (
      <div className="min-h-screen bg-neutral-100 flex items-center justify-center">
        <div className="bg-white p-8 rounded-2xl shadow-lg max-w-md w-full">
          <h1 className="text-2xl font-bold text-neutral-800 mb-6 text-center">
            Admin Access Required
          </h1>
          <form onSubmit={handleAuth} className="space-y-4">
            <div>
              <label htmlFor="adminKey" className="block text-sm font-semibold text-neutral-700 mb-2">
                Admin Key
              </label>
              <input
                type="password"
                id="adminKey"
                value={adminKey}
                onChange={(e) => setAdminKey(e.target.value)}
                className="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="Enter admin key"
                required
              />
            </div>
            <button
              type="submit"
              className="w-full bg-primary-600 hover:bg-primary-700 text-white font-bold py-3 px-4 rounded-lg transition-colors duration-200"
            >
              Access Admin Panel
            </button>
          </form>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-neutral-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <div className="flex justify-between items-center mb-4">
            <h1 className="text-3xl font-bold text-neutral-800">Review Moderation</h1>
          </div>



          {/* Filter Tabs */}
          <div className="flex space-x-4 mb-6">
            {(['all', 'pending', 'approved', 'rejected'] as const).map((status) => (
              <button
                key={status}
                onClick={() => setFilter(status)}
                className={`px-4 py-2 rounded-lg font-semibold transition-colors duration-200 ${
                  filter === status
                    ? 'bg-primary-600 text-white'
                    : 'bg-white text-neutral-600 hover:bg-neutral-100'
                }`}
              >
                {status.charAt(0).toUpperCase() + status.slice(1)}
                <span className="ml-2 text-sm">
                  ({reviews.filter(r => status === 'all' || r.status === status).length})
                </span>
              </button>
            ))}
          </div>
        </div>

        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
            <p className="mt-4 text-neutral-600">Loading reviews...</p>
          </div>
        ) : (
          <div className="space-y-6">
            {filteredReviews.length === 0 ? (
              <div className="text-center py-12 bg-white rounded-2xl">
                <p className="text-neutral-600">No reviews found for the selected filter.</p>
              </div>
            ) : (
              filteredReviews.map((review) => (
                <div key={review.id} className="bg-white rounded-2xl shadow-lg p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <div className="flex items-center space-x-4 mb-2">
                        <h3 className="text-xl font-bold text-neutral-800">{review.name}</h3>
                        <span className={`px-3 py-1 rounded-full text-sm font-semibold ${
                          review.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                          review.status === 'approved' ? 'bg-green-100 text-green-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {review.status}
                        </span>
                      </div>
                      
                      <div className="flex items-center space-x-4 text-sm text-neutral-600 mb-3">
                        <div className="flex items-center space-x-1">
                          <MapPin className="w-4 h-4" />
                          <span>{review.location}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Mail className="w-4 h-4" />
                          <span>{review.email}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Calendar className="w-4 h-4" />
                          <span>{new Date(review.submittedAt).toLocaleDateString()}</span>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2 mb-3">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <Star
                            key={star}
                            className={`w-5 h-5 ${
                              star <= review.rating ? 'text-yellow-400 fill-current' : 'text-neutral-300'
                            }`}
                          />
                        ))}
                        <span className="text-sm text-neutral-600">({review.rating}/5)</span>
                      </div>

                      {review.vehiclePurchased && (
                        <p className="text-sm text-neutral-600 mb-2">
                          <strong>Vehicle:</strong> {review.vehiclePurchased}
                          {review.purchaseDate && ` (${review.purchaseDate})`}
                        </p>
                      )}
                    </div>

                    {review.status === 'pending' && (
                      <div className="flex space-x-2">
                        <button
                          onClick={() => moderateReview(review.id, 'approved')}
                          className="bg-green-600 hover:bg-green-700 text-white p-2 rounded-lg transition-colors duration-200"
                          title="Approve Review"
                        >
                          <Check className="w-5 h-5" />
                        </button>
                        <button
                          onClick={() => moderateReview(review.id, 'rejected')}
                          className="bg-red-600 hover:bg-red-700 text-white p-2 rounded-lg transition-colors duration-200"
                          title="Reject Review"
                        >
                          <X className="w-5 h-5" />
                        </button>
                      </div>
                    )}
                  </div>

                  <div className="mb-4">
                    <h4 className="font-semibold text-neutral-800 mb-2">{review.title}</h4>
                    <p className="text-neutral-700 leading-relaxed">{review.review}</p>
                  </div>

                  {review.images.length > 0 && (
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      {review.images.map((image, index) => (
                        <Image
                          key={index}
                          src={image}
                          alt={`Review image ${index + 1}`}
                          width={96}
                          height={96}
                          className="w-full h-24 object-cover rounded-lg"
                        />
                      ))}
                    </div>
                  )}
                </div>
              ))
            )}
          </div>
        )}
      </div>
    </div>
  );
}
