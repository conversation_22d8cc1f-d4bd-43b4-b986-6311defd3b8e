import { NextRequest, NextResponse } from 'next/server';
import { getAdminAuth } from '@/lib/adminMiddleware';
import { 
  getHealthStatus, 
  getPerformanceStats, 
  getRecentErrors, 
  checkDatabaseHealth,
  getMemoryUsage 
} from '@/lib/monitoring';
import { getSecurityHeaders } from '@/lib/security';

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminAuth = getAdminAuth(request);
    if (!adminAuth.isValid) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const detailed = searchParams.get('detailed') === 'true';
    const timeRange = parseInt(searchParams.get('timeRange') || '3600000'); // Default 1 hour

    // Basic health status
    const healthStatus = getHealthStatus();
    
    if (!detailed) {
      const response = NextResponse.json({
        success: true,
        health: healthStatus
      });

      // Add security headers
      const securityHeaders = getSecurityHeaders();
      Object.entries(securityHeaders).forEach(([key, value]) => {
        response.headers.set(key, value);
      });

      return response;
    }

    // Detailed health information
    const [performanceStats, recentErrors, dbHealth, memoryUsage] = await Promise.all([
      getPerformanceStats(timeRange),
      getRecentErrors(20),
      checkDatabaseHealth(),
      getMemoryUsage()
    ]);

    const detailedHealth = {
      ...healthStatus,
      performance: performanceStats,
      database: dbHealth,
      memory: memoryUsage,
      errors: recentErrors,
      system: {
        nodeVersion: process.version,
        platform: process.platform,
        uptime: process.uptime(),
        pid: process.pid
      }
    };

    const response = NextResponse.json({
      success: true,
      health: detailedHealth
    });

    // Add security headers
    const securityHeaders = getSecurityHeaders();
    Object.entries(securityHeaders).forEach(([key, value]) => {
      response.headers.set(key, value);
    });

    return response;

  } catch (error) {
    console.error('Health check error:', error);
    
    const response = NextResponse.json(
      { 
        success: false, 
        message: 'Health check failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );

    // Add security headers even for errors
    const securityHeaders = getSecurityHeaders();
    Object.entries(securityHeaders).forEach(([key, value]) => {
      response.headers.set(key, value);
    });

    return response;
  }
}
