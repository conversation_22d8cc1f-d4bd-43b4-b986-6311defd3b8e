'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { X, ShoppingCart, CreditCard, Truck, Shield, CheckCircle, Calculator, MapPin, Clock } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { PaymentMethod, ShippingMethod } from '@/types/payment';
import { getEnabledPaymentMethods } from '@/lib/paymentConfig';

interface StockItem {
  id: number;
  category: string;
  title: string;
  price: string;
  location: string;
  status: string;
  image: string;
  images: string[];
  specs: string[];
  carId: string;
  year?: number;
  mileage?: number;
  fuelType?: string;
  transmission?: string;
  bodyCondition?: string;
}

interface PurchaseModalProps {
  isOpen: boolean;
  onClose: () => void;
  vehicle: StockItem;
  locale: string;
}

export default function PurchaseModal({ isOpen, onClose, vehicle, locale }: PurchaseModalProps) {
  const { user, addPurchase } = useAuth();
  const [step, setStep] = useState<'details' | 'shipping' | 'payment' | 'confirmation'>('details');
  const [isProcessing, setIsProcessing] = useState(false);
  const [orderComplete, setOrderComplete] = useState(false);

  // New state for payment and shipping
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod | null>(null);
  const [shippingOptions, setShippingOptions] = useState<any[]>([]);
  const [selectedShipping, setSelectedShipping] = useState<any>(null);
  const [shippingAddress, setShippingAddress] = useState({
    street: '',
    city: '',
    state: '',
    country: 'Ghana',
    postalCode: '',
  });
  const [isCalculatingShipping, setIsCalculatingShipping] = useState(false);
  const [orderId, setOrderId] = useState<string | null>(null);

  // Load payment methods on mount
  useEffect(() => {
    if (isOpen) {
      const methods = getEnabledPaymentMethods();
      setPaymentMethods(methods);
      if (methods.length > 0) {
        setSelectedPaymentMethod(methods[0]);
      }
    }
  }, [isOpen]);

  if (!isOpen) return null;

  const calculateShipping = async () => {
    if (!shippingAddress.city || !shippingAddress.country) return;

    setIsCalculatingShipping(true);
    try {
      const response = await fetch('/api/shipping/calculate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          vehicle: {
            category: vehicle.category || 'sedan',
            year: vehicle.year || 2020,
            price: parseInt(vehicle.price.replace(/[¥,]/g, '')) || 500000,
          },
          destination: {
            country: shippingAddress.country,
            city: shippingAddress.city,
          },
          preference: 'balanced',
        }),
      });

      const data = await response.json();
      if (data.success) {
        setShippingOptions(data.data.options);
        if (data.data.recommended) {
          setSelectedShipping(data.data.recommended);
        }
      }
    } catch (error) {
      console.error('Error calculating shipping:', error);
    } finally {
      setIsCalculatingShipping(false);
    }
  };

  const handleCreateOrder = async () => {
    if (!user || !selectedPaymentMethod || !selectedShipping) return;

    setIsProcessing(true);

    try {
      const vehiclePrice = parseInt(vehicle.price.replace(/[¥,]/g, '')) || 0;
      const totalAmount = vehiclePrice + selectedShipping.costs.total;

      const orderData = {
        customerId: user.id,
        customerInfo: {
          name: user.name,
          email: user.email,
          phone: user.phone || '',
          address: shippingAddress,
        },
        vehicle: {
          id: vehicle.carId,
          title: vehicle.title,
          price: vehiclePrice,
          currency: 'JPY',
          images: vehicle.images,
          specs: vehicle.specs,
        },
        payment: {
          method: selectedPaymentMethod,
          amount: totalAmount,
          currency: 'JPY',
          dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        },
        shipping: {
          method: selectedShipping.method,
          cost: selectedShipping.costs.total,
          address: shippingAddress,
          estimatedDelivery: selectedShipping.estimatedDelivery,
        },
        totalAmount,
        currency: 'JPY',
      };

      const response = await fetch('/api/orders', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(orderData),
      });

      const result = await response.json();
      if (result.success) {
        setOrderId(result.order.id);

        // Add to purchase history
        addPurchase({
          vehicleId: vehicle.carId,
          vehicleTitle: vehicle.title,
          price: vehicle.price,
          status: 'pending',
          trackingNumber: result.order.id,
        });

        setOrderComplete(true);
        setStep('confirmation');
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error('Error creating order:', error);
      alert('Failed to create order. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleClose = () => {
    setStep('details');
    setOrderComplete(false);
    setIsProcessing(false);
    setSelectedPaymentMethod(null);
    setSelectedShipping(null);
    setShippingOptions([]);
    setOrderId(null);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-bold text-neutral-800 flex items-center space-x-2">
            <ShoppingCart className="w-5 h-5" />
            <span>{locale === 'en' ? 'Purchase Vehicle' : '車両購入'}</span>
          </h2>
          <button
            onClick={handleClose}
            className="text-neutral-600 hover:text-neutral-800 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {step === 'details' && (
            <div className="space-y-6">
              {/* Vehicle Summary */}
              <div className="bg-neutral-50 rounded-lg p-4">
                <h3 className="font-semibold text-neutral-800 mb-3">
                  {locale === 'en' ? 'Vehicle Details' : '車両詳細'}
                </h3>
                <div className="flex items-center space-x-4">
                  <Image
                    src={vehicle.image}
                    alt={vehicle.title}
                    width={80}
                    height={80}
                    className="w-20 h-20 object-cover rounded-lg"
                  />
                  <div>
                    <h4 className="font-medium text-neutral-800">{vehicle.title}</h4>
                    <p className="text-2xl font-bold text-primary-600">{vehicle.price}</p>
                    <p className="text-sm text-neutral-600">{vehicle.location}</p>
                  </div>
                </div>
              </div>

              {/* Purchase Options */}
              <div className="space-y-4">
                <h3 className="font-semibold text-neutral-800">
                  {locale === 'en' ? 'Purchase Options' : '購入オプション'}
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="border rounded-lg p-4 text-center">
                    <Shield className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                    <h4 className="font-medium text-neutral-800 mb-1">
                      {locale === 'en' ? 'Inspection' : '検査'}
                    </h4>
                    <p className="text-sm text-neutral-600">
                      {locale === 'en' ? 'Professional inspection included' : 'プロの検査が含まれています'}
                    </p>
                  </div>
                  
                  <div className="border rounded-lg p-4 text-center">
                    <Truck className="w-8 h-8 text-green-600 mx-auto mb-2" />
                    <h4 className="font-medium text-neutral-800 mb-1">
                      {locale === 'en' ? 'Shipping' : '配送'}
                    </h4>
                    <p className="text-sm text-neutral-600">
                      {locale === 'en' ? 'Door-to-door delivery' : 'ドア・ツー・ドア配送'}
                    </p>
                  </div>
                  
                  <div className="border rounded-lg p-4 text-center">
                    <CreditCard className="w-8 h-8 text-purple-600 mx-auto mb-2" />
                    <h4 className="font-medium text-neutral-800 mb-1">
                      {locale === 'en' ? 'Payment' : '支払い'}
                    </h4>
                    <p className="text-sm text-neutral-600">
                      {locale === 'en' ? 'Secure payment processing' : '安全な決済処理'}
                    </p>
                  </div>
                </div>
              </div>

              {/* Terms */}
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <h4 className="font-medium text-yellow-800 mb-2">
                  {locale === 'en' ? 'Important Information' : '重要な情報'}
                </h4>
                <ul className="text-sm text-yellow-700 space-y-1">
                  <li>• {locale === 'en' ? 'Final price includes all fees and taxes' : '最終価格にはすべての手数料と税金が含まれています'}</li>
                  <li>• {locale === 'en' ? 'Delivery time: 4-6 weeks to Ghana' : '配送時間：ガーナまで4-6週間'}</li>
                  <li>• {locale === 'en' ? 'Full refund if vehicle condition differs from description' : '車両の状態が説明と異なる場合は全額返金'}</li>
                </ul>
              </div>

              {/* Action Buttons */}
              <div className="flex items-center justify-between pt-4">
                <button
                  onClick={handleClose}
                  className="px-6 py-2 border border-neutral-300 text-neutral-700 rounded-lg hover:bg-neutral-50 transition-colors"
                >
                  {locale === 'en' ? 'Cancel' : 'キャンセル'}
                </button>
                <button
                  onClick={() => setStep('shipping')}
                  className="px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
                >
                  {locale === 'en' ? 'Configure Shipping' : '配送設定'}
                </button>
              </div>
            </div>
          )}

          {step === 'shipping' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-neutral-800 mb-4 flex items-center space-x-2">
                <Truck className="w-5 h-5" />
                <span>{locale === 'en' ? 'Shipping Configuration' : '配送設定'}</span>
              </h3>

              {/* Shipping Address */}
              <div className="bg-neutral-50 rounded-lg p-4">
                <h4 className="font-medium text-neutral-800 mb-3 flex items-center space-x-2">
                  <MapPin className="w-4 h-4" />
                  <span>{locale === 'en' ? 'Delivery Address' : '配送先住所'}</span>
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-1">
                      {locale === 'en' ? 'Street Address' : '住所'}
                    </label>
                    <input
                      type="text"
                      value={shippingAddress.street}
                      onChange={(e) => setShippingAddress(prev => ({ ...prev, street: e.target.value }))}
                      className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      placeholder={locale === 'en' ? 'Enter street address' : '住所を入力'}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-1">
                      {locale === 'en' ? 'City' : '市区町村'}
                    </label>
                    <input
                      type="text"
                      value={shippingAddress.city}
                      onChange={(e) => setShippingAddress(prev => ({ ...prev, city: e.target.value }))}
                      className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      placeholder={locale === 'en' ? 'Enter city' : '市区町村を入力'}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-1">
                      {locale === 'en' ? 'State/Region' : '州/地域'}
                    </label>
                    <input
                      type="text"
                      value={shippingAddress.state}
                      onChange={(e) => setShippingAddress(prev => ({ ...prev, state: e.target.value }))}
                      className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      placeholder={locale === 'en' ? 'Enter state/region' : '州/地域を入力'}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-1">
                      {locale === 'en' ? 'Country' : '国'}
                    </label>
                    <select
                      value={shippingAddress.country}
                      onChange={(e) => setShippingAddress(prev => ({ ...prev, country: e.target.value }))}
                      className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    >
                      <option value="Ghana">Ghana</option>
                      <option value="Nigeria">Nigeria</option>
                      <option value="Kenya">Kenya</option>
                      <option value="South Africa">South Africa</option>
                      <option value="Ivory Coast">Ivory Coast</option>
                    </select>
                  </div>
                </div>

                <button
                  onClick={calculateShipping}
                  disabled={!shippingAddress.city || !shippingAddress.country || isCalculatingShipping}
                  className="mt-4 flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Calculator className="w-4 h-4" />
                  <span>
                    {isCalculatingShipping
                      ? (locale === 'en' ? 'Calculating...' : '計算中...')
                      : (locale === 'en' ? 'Calculate Shipping' : '配送料計算')
                    }
                  </span>
                </button>
              </div>

              {/* Shipping Options */}
              {shippingOptions.length > 0 && (
                <div className="space-y-3">
                  <h4 className="font-medium text-neutral-800 flex items-center space-x-2">
                    <Clock className="w-4 h-4" />
                    <span>{locale === 'en' ? 'Shipping Options' : '配送オプション'}</span>
                  </h4>
                  {shippingOptions.map((option, index) => (
                    <div
                      key={index}
                      className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                        selectedShipping?.method.id === option.method.id
                          ? 'border-primary-500 bg-primary-50'
                          : 'border-neutral-300 hover:border-neutral-400'
                      }`}
                      onClick={() => setSelectedShipping(option)}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <h5 className="font-medium text-neutral-800">{option.method.name}</h5>
                          <p className="text-sm text-neutral-600">{option.method.description}</p>
                          <p className="text-sm text-neutral-500 mt-1">
                            {locale === 'en' ? 'Estimated delivery:' : '配送予定:'} {option.estimatedDelivery}
                          </p>
                        </div>
                        <div className="text-right">
                          <div className="text-lg font-bold text-primary-600">
                            ¥{option.costs.total.toLocaleString()}
                          </div>
                          <div className="text-sm text-neutral-500">
                            {option.method.estimatedDays} {locale === 'en' ? 'days' : '日'}
                          </div>
                        </div>
                      </div>
                      {option.isRecommended && (
                        <div className="mt-2 inline-block px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                          {locale === 'en' ? 'Recommended' : '推奨'}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex items-center justify-between pt-4">
                <button
                  onClick={() => setStep('details')}
                  className="px-6 py-2 border border-neutral-300 text-neutral-700 rounded-lg hover:bg-neutral-50 transition-colors"
                >
                  {locale === 'en' ? 'Back' : '戻る'}
                </button>
                <button
                  onClick={() => setStep('payment')}
                  disabled={!selectedShipping}
                  className="px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {locale === 'en' ? 'Continue to Payment' : '支払いに進む'}
                </button>
              </div>
            </div>
          )}

          {step === 'payment' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-neutral-800 mb-4 flex items-center space-x-2">
                <CreditCard className="w-5 h-5" />
                <span>{locale === 'en' ? 'Payment Method' : '支払い方法'}</span>
              </h3>

              {/* Order Summary */}
              <div className="bg-neutral-50 rounded-lg p-4">
                <h4 className="font-medium text-neutral-800 mb-3">
                  {locale === 'en' ? 'Order Summary' : '注文概要'}
                </h4>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>{vehicle.title}</span>
                    <span>{vehicle.price}</span>
                  </div>
                  {selectedShipping && (
                    <>
                      <div className="flex justify-between">
                        <span>{locale === 'en' ? 'Shipping' : '配送料'} ({selectedShipping.method.name})</span>
                        <span>¥{selectedShipping.costs.total.toLocaleString()}</span>
                      </div>
                      <div className="border-t pt-2 flex justify-between font-bold">
                        <span>{locale === 'en' ? 'Total' : '合計'}</span>
                        <span>¥{(parseInt(vehicle.price.replace(/[¥,]/g, '')) + selectedShipping.costs.total).toLocaleString()}</span>
                      </div>
                    </>
                  )}
                </div>
              </div>

              {/* Payment Methods */}
              <div className="space-y-3">
                <h4 className="font-medium text-neutral-800">
                  {locale === 'en' ? 'Select Payment Method' : '支払い方法を選択'}
                </h4>
                {paymentMethods.map((method) => (
                  <div
                    key={method.id}
                    className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                      selectedPaymentMethod?.id === method.id
                        ? 'border-primary-500 bg-primary-50'
                        : 'border-neutral-300 hover:border-neutral-400'
                    }`}
                    onClick={() => setSelectedPaymentMethod(method)}
                  >
                    <div className="flex items-center space-x-3">
                      <span className="text-2xl">{method.icon}</span>
                      <div>
                        <h5 className="font-medium text-neutral-800">{method.name}</h5>
                        <p className="text-sm text-neutral-600">{method.description}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Create Order Button */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <p className="text-sm text-blue-700 mb-3">
                  {locale === 'en'
                    ? 'By proceeding, you agree to create an order. Payment instructions will be provided after order creation.'
                    : '続行することで、注文の作成に同意したことになります。注文作成後に支払い指示が提供されます。'
                  }
                </p>
                <button
                  onClick={handleCreateOrder}
                  disabled={isProcessing || !selectedPaymentMethod}
                  className="w-full bg-green-600 text-white py-3 px-6 rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
                >
                  {isProcessing ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      <span>{locale === 'en' ? 'Creating Order...' : '注文作成中...'}</span>
                    </>
                  ) : (
                    <>
                      <ShoppingCart className="w-4 h-4" />
                      <span>{locale === 'en' ? 'Create Order' : '注文作成'}</span>
                    </>
                  )}
                </button>
              </div>

              <div className="flex items-center justify-between pt-4">
                <button
                  onClick={() => setStep('shipping')}
                  disabled={isProcessing}
                  className="px-6 py-2 border border-neutral-300 text-neutral-700 rounded-lg hover:bg-neutral-50 transition-colors disabled:opacity-50"
                >
                  {locale === 'en' ? 'Back' : '戻る'}
                </button>
              </div>
            </div>
          )}

          {step === 'confirmation' && orderComplete && (
            <div className="space-y-6 text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>

              <div>
                <h3 className="text-xl font-bold text-neutral-800 mb-2">
                  {locale === 'en' ? 'Order Created Successfully!' : '注文作成完了！'}
                </h3>
                <p className="text-neutral-600 mb-4">
                  {locale === 'en'
                    ? 'Your order has been created and an invoice has been generated. Please complete payment to proceed.'
                    : '注文が作成され、請求書が生成されました。続行するには支払いを完了してください。'
                  }
                </p>
                {orderId && (
                  <div className="bg-neutral-100 rounded-lg p-3 mb-4">
                    <p className="text-sm text-neutral-600">
                      {locale === 'en' ? 'Order ID:' : '注文ID:'} <span className="font-mono font-medium">{orderId}</span>
                    </p>
                  </div>
                )}
              </div>

              {/* Payment Instructions */}
              {selectedPaymentMethod && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-left">
                  <h4 className="font-medium text-blue-800 mb-2 flex items-center space-x-2">
                    <CreditCard className="w-4 h-4" />
                    <span>{locale === 'en' ? 'Payment Instructions' : '支払い指示'}</span>
                  </h4>
                  <div className="text-sm text-blue-700 whitespace-pre-line">
                    {selectedPaymentMethod && selectedShipping && orderId &&
                      (() => {
                        const totalAmount = parseInt(vehicle.price.replace(/[¥,]/g, '')) + selectedShipping.costs.total;
                        // This would normally come from the payment config
                        return `Payment Method: ${selectedPaymentMethod.name}\nAmount: ¥${totalAmount.toLocaleString()}\nOrder Reference: ${orderId}\n\nPlease contact us for detailed payment instructions.`;
                      })()
                    }
                  </div>
                </div>
              )}

              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <h4 className="font-medium text-green-800 mb-2">
                  {locale === 'en' ? 'What happens next?' : '次に何が起こりますか？'}
                </h4>
                <ul className="text-sm text-green-700 space-y-1 text-left">
                  <li>• {locale === 'en' ? 'Complete payment using the instructions above' : '上記の指示に従って支払いを完了'}</li>
                  <li>• {locale === 'en' ? 'Vehicle inspection and preparation' : '車両検査と準備'}</li>
                  <li>• {locale === 'en' ? 'Export documentation processing' : '輸出書類処理'}</li>
                  <li>• {locale === 'en' ? 'Shipping arrangement' : '配送手配'}</li>
                  <li>• {locale === 'en' ? 'Regular updates on delivery status' : '配送状況の定期更新'}</li>
                </ul>
              </div>

              <div className="space-y-3">
                <button
                  onClick={handleClose}
                  className="w-full bg-primary-600 text-white py-3 px-6 rounded-lg hover:bg-primary-700 transition-colors"
                >
                  {locale === 'en' ? 'Close' : '閉じる'}
                </button>

                {orderId && (
                  <button
                    onClick={() => window.open(`/tracking?orderId=${orderId}`, '_blank')}
                    className="w-full bg-neutral-600 text-white py-2 px-6 rounded-lg hover:bg-neutral-700 transition-colors"
                  >
                    {locale === 'en' ? 'Track Order' : '注文追跡'}
                  </button>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
