import { getMessages } from '@/lib/messages';
import Navigation from '@/components/Navigation';
import ReviewForm from '@/components/ReviewForm';

export default async function LeaveReviewPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;
  const messages = await getMessages(locale);

  return (
    <div className="min-h-screen bg-neutral-50">
      <Navigation />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center space-y-6 mb-12">
          <h1 className="text-4xl lg:text-5xl font-heading font-bold text-neutral-800">
            {messages.reviews.leaveAReviewPage}
          </h1>
          <p className="text-xl text-neutral-600 max-w-2xl mx-auto">
            {messages.reviews.shareYourExperienceDesc}
          </p>
        </div>

        <div className="bg-white rounded-2xl shadow-lg p-8">
          <ReviewForm locale={locale} />
        </div>

        {/* Guidelines */}
        <div className="mt-12 bg-primary-50 rounded-2xl p-8">
          <h3 className="text-xl font-bold text-neutral-800 mb-4">{messages.reviews.reviewGuidelines}</h3>
          <ul className="space-y-2 text-neutral-600">
            <li className="flex items-start space-x-2">
              <span className="text-primary-600 font-bold">•</span>
              <span>{messages.reviews.beHonestAndSpecific}</span>
            </li>
            <li className="flex items-start space-x-2">
              <span className="text-primary-600 font-bold">•</span>
              <span>{messages.reviews.focusOnService}</span>
            </li>
            <li className="flex items-start space-x-2">
              <span className="text-primary-600 font-bold">•</span>
              <span>{messages.reviews.keepRespectful}</span>
            </li>
            <li className="flex items-start space-x-2">
              <span className="text-primary-600 font-bold">•</span>
              <span>{messages.reviews.allReviewsModerated}</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
}
