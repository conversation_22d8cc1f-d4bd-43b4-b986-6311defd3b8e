import { MetadataRoute } from 'next'

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://ebammotors.com'

  const staticRoutes = [
    { path: '', priority: 1.0, changeFrequency: 'daily' as const },
    { path: '/about', priority: 0.8, changeFrequency: 'monthly' as const },
    { path: '/services', priority: 0.9, changeFrequency: 'weekly' as const },
    { path: '/stock', priority: 0.9, changeFrequency: 'daily' as const },
    { path: '/how-it-works', priority: 0.7, changeFrequency: 'monthly' as const },
    { path: '/suppliers', priority: 0.6, changeFrequency: 'monthly' as const },
    { path: '/buyers', priority: 0.6, changeFrequency: 'monthly' as const },
    { path: '/contact', priority: 0.8, changeFrequency: 'monthly' as const },
    { path: '/reviews', priority: 0.7, changeFrequency: 'weekly' as const },
    { path: '/leave-review', priority: 0.5, changeFrequency: 'monthly' as const },
  ]

  const locales = ['en', 'ja']
  const sitemap: MetadataRoute.Sitemap = []

  // Add static routes for each locale
  locales.forEach(locale => {
    staticRoutes.forEach(route => {
      sitemap.push({
        url: `${baseUrl}/${locale}${route.path}`,
        lastModified: new Date(),
        changeFrequency: route.changeFrequency,
        priority: route.priority,
      })
    })
  })

  // Add dynamic car routes (if database is available)
  try {
    const { getAllCars } = await import('@/lib/database');
    const cars = await getAllCars({ limit: 1000 }); // Get up to 1000 cars for sitemap

    cars.forEach(car => {
      locales.forEach(locale => {
        sitemap.push({
          url: `${baseUrl}/${locale}/stock/${car.car_id}`,
          lastModified: new Date(car.updated_at || car.created_at),
          changeFrequency: 'weekly',
          priority: 0.6,
        })
      })
    })
  } catch (error) {
    console.warn('Could not fetch cars for sitemap:', error);
    // Continue without dynamic routes if database is not available
  }

  // Add category pages
  const categories = [
    'toyota', 'honda', 'nissan', 'mazda', 'subaru', 'mitsubishi',
    'sedan', 'suv', 'hatchback', 'wagon', 'coupe', 'van'
  ];

  categories.forEach(category => {
    locales.forEach(locale => {
      sitemap.push({
        url: `${baseUrl}/${locale}/stock/category/${category}`,
        lastModified: new Date(),
        changeFrequency: 'weekly',
        priority: 0.7,
      })
    })
  })

  return sitemap
}
