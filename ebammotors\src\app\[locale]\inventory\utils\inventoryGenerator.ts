import path from 'path';

interface InventoryItem {
  id: number;
  category: string;
  title: string;
  price: string;
  location: string;
  status: string;
  image: string;
  images: string[]; // All images for this car
  specs: string[];
  carId: string; // Unique identifier for each car folder
}

interface CarModelConfig {
  name: string;
  basePrice: number;
  seats: string;
  transmission: string;
  fuelType: string;
}

const carModelConfigs: Record<string, CarModelConfig> = {
  'toyota-voxy': {
    name: 'Toyota Voxy',
    basePrice: 300000,
    seats: '8-seater',
    transmission: 'Automatic',
    fuelType: 'Gasoline'
  },
  'toyota-noah': {
    name: 'Toyota Noah',
    basePrice: 350000,
    seats: '8-seater',
    transmission: 'Automatic',
    fuelType: 'Gasoline'
  },
  'toyota-sienta': {
    name: 'Toyota Sienta',
    basePrice: 320000,
    seats: '7-seater',
    transmission: 'CVT',
    fuelType: 'Gasoline'
  },
  'toyota-vitz': {
    name: 'Toyota Vitz',
    basePrice: 325000,
    seats: '5-seater',
    transmission: 'CVT',
    fuelType: 'Gasoline'
  },
  'toyota-yaris': {
    name: 'Toyota Yaris',
    basePrice: 550000,
    seats: '5-seater',
    transmission: 'CVT',
    fuelType: 'Gasoline'
  }
};

function extractYearFromFolderName(folderName: string): number {
  const yearMatch = folderName.match(/(\d{4})/);
  return yearMatch ? parseInt(yearMatch[1]) : 2010;
}

function generateDeterministicMileage(seed: string): number {
  // Create a simple hash from the seed string for deterministic "randomness"
  let hash = 0;
  for (let i = 0; i < seed.length; i++) {
    const char = seed.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }

  // Use the hash to generate a deterministic value between 30000 and 120000
  const range = 120000 - 30000;
  const normalized = Math.abs(hash) / 2147483647; // Normalize to 0-1
  return Math.floor(normalized * range + 30000);
}

function getImageExtensions(): string[] {
  return ['.jpg', '.jpeg', '.JPG', '.JPEG', '.png', '.PNG'];
}

function isImageFile(filename: string): boolean {
  const ext = path.extname(filename);
  return getImageExtensions().includes(ext);
}

function generateFallbackInventory(): InventoryItem[] {
  // Fallback inventory data for client-side rendering
  return [
    {
      id: 1,
      category: 'cars',
      title: 'Toyota Voxy 2015',
      price: '¥450,000',
      location: 'Japan',
      status: 'Available',
      image: '/car-models/voxy/voxy-2015-001/1.jpg',
      images: ['/car-models/voxy/voxy-2015-001/1.jpg', '/car-models/voxy/voxy-2015-001/2.jpg'],
      specs: ['8 seats', 'CVT', '85k km', 'Gasoline', 'Excellent'],
      carId: 'voxy-2015-001',
      year: 2015,
      mileage: 85000,
      fuelType: 'Gasoline',
      transmission: 'CVT',
      bodyCondition: 'Excellent'
    }
  ];
}

export async function generateInventoryFromFileSystem(): Promise<InventoryItem[]> {
  // Check if we're running on the server side
  if (typeof window !== 'undefined') {
    // We're on the client side, return fallback data
    return generateFallbackInventory();
  }

  const inventoryItems: InventoryItem[] = [];
  const carModelsPath = path.join(process.cwd(), 'public', 'car-models');

  try {
    // Dynamic import of fs module only on server side
    const fs = await import('fs');

    if (!fs.existsSync(carModelsPath)) {
      console.warn('Car models directory not found:', carModelsPath);
      return generateFallbackInventory();
    }

    const modelFolders = fs.readdirSync(carModelsPath);
    let itemId = 1;

    for (const modelFolder of modelFolders) {
      const modelPath = path.join(carModelsPath, modelFolder);

      if (!fs.statSync(modelPath).isDirectory()) continue;

      const config = carModelConfigs[modelFolder];
      if (!config) continue;

      // Get all car folders within this model
      const carFolders = fs.readdirSync(modelPath);

      for (const carFolder of carFolders) {
        const carPath = path.join(modelPath, carFolder);
        
        if (!fs.statSync(carPath).isDirectory()) continue;

        // Get all images in this car folder
        const imageFiles = fs.readdirSync(carPath)
          .filter(file => isImageFile(file))
          .sort(); // Sort to ensure consistent order

        if (imageFiles.length === 0) continue;

        // Extract year and generate car details
        const year = extractYearFromFolderName(carFolder);
        const mileage = generateDeterministicMileage(carFolder);
        
        // Adjust price based on year
        let adjustedPrice = config.basePrice;
        if (year <= 2010) adjustedPrice -= 5000;
        if (year >= 2012) adjustedPrice += 5000;

        // Generate all image paths
        const allImages = imageFiles.map(file => 
          `/car-models/${modelFolder}/${carFolder}/${file}`
        );

        // Create inventory item
        const inventoryItem: InventoryItem = {
          id: itemId++,
          category: 'cars',
          title: `${config.name} ${year}`,
          price: `¥${adjustedPrice.toLocaleString()}`,
          location: 'Japan',
          status: carFolder.length % 10 === 0 ? 'Reserved' : 'Available', // Deterministic status based on folder name
          image: allImages[0], // First image as main image
          images: allImages, // All images for animation and modal
          specs: [
            config.seats,
            config.transmission,
            `${Math.floor(mileage / 1000)}k km`,
            config.fuelType
          ],
          carId: carFolder
        };

        inventoryItems.push(inventoryItem);
      }
    }

    // Add some non-car inventory items
    const otherItems: InventoryItem[] = [
      {
        id: itemId++,
        category: 'electronics',
        title: 'Panasonic Refrigerator',
        price: '¥45,000',
        location: 'Japan',
        status: 'Available',
        image: '/electronics/electronics.png',
        images: ['/electronics/electronics.png', '/electronics/electronics1.JPG'],
        specs: ['400L', 'Energy Efficient', 'Good Condition'],
        carId: 'electronics-001'
      },
      {
        id: itemId++,
        category: 'furniture',
        title: 'Japanese Dining Set',
        price: '¥32,000',
        location: 'Japan',
        status: 'Reserved',
        image: '/furniture/furniture.png',
        images: ['/furniture/furniture.png', '/furniture/furniture1.JPG'],
        specs: ['Table + 4 Chairs', 'Solid Wood', 'Excellent Condition'],
        carId: 'furniture-001'
      },
      {
        id: itemId++,
        category: 'electronics',
        title: 'Sharp Air Conditioner',
        price: '¥28,000',
        location: 'Japan',
        status: 'Available',
        image: '/electronics/electronics.png',
        images: ['/electronics/electronics.png', '/electronics/laptop.JPG'],
        specs: ['2.5HP', 'Inverter', 'Remote Control'],
        carId: 'electronics-002'
      }
    ];

    inventoryItems.push(...otherItems);

    return inventoryItems;
  } catch (error) {
    console.error('Error generating inventory from file system:', error);
    return generateFallbackInventory();
  }
}
