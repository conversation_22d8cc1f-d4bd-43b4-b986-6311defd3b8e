# EBAM Motors - Review Management Guide

## Overview
The review system allows customers to submit reviews that require admin approval before appearing on the website.

## Admin Panel Access
- **URL**: `http://localhost:3000/admin/reviews` (or your domain)
- **Admin Password**: Set in `.env.local` file (see setup instructions below)

## Setup Instructions

### 1. Environment Variables Setup
1. Copy `.env.example` to `.env.local`
2. Change `ADMIN_PASSWORD` to a secure password
3. **NEVER commit `.env.local` to version control**

### 2. Production Deployment
1. Set `ADMIN_PASSWORD` environment variable on your hosting platform
2. Use a strong, unique password (minimum 12 characters)
3. Consider using a password manager to generate secure passwords

### 3. Security Notes
- The `.env.local` file is automatically ignored by Git
- Environment variables are only accessible on the server
- Admin password is never exposed to client-side code

## Review Workflow

### 1. Customer Submits Review
- Customer fills out review form at `/leave-review`
- Review is submitted with status: `pending`
- Admin receives notification in console logs

### 2. Admin Reviews Submission
- Access admin panel with admin key
- View all pending reviews
- Check review content for:
  - Appropriate language
  - Genuine feedback
  - No spam or fake content
  - Relevant to EBAM Motors services

### 3. Admin Actions
- **Approve**: Review appears on website immediately
- **Reject**: Review is hidden and won't appear publicly

## Review Display Locations

### Homepage
- Shows up to 6 reviews (mix of approved + static testimonials)
- Displays in testimonial card format
- Includes trust stats section

### Dedicated Reviews Page
- URL: `/reviews`
- Shows all approved reviews
- Includes call-to-action for new reviews
- Accessible via main navigation

## Review Data Structure
Each review contains:
- Customer name and location
- Star rating (1-5)
- Review title and detailed text
- Optional: Vehicle purchased and purchase date
- Optional: Up to 5 images
- Submission timestamp
- Approval status

## Technical Notes

### Storage
- Currently uses in-memory storage (resets on server restart)
- For production: Replace with database (PostgreSQL, MongoDB, etc.)

### Review Content
- Text-based reviews only (images not supported)
- All reviews require moderation before publication

### API Endpoints
- `POST /api/reviews` - Submit new review
- `GET /api/reviews` - Get approved reviews (public)
- `POST /api/admin/auth` - Admin authentication
- `GET /api/reviews?adminKey=[password]` - Get all reviews (admin)
- `PATCH /api/reviews` - Approve/reject review (admin)

## Security Considerations

### For Production
1. **Change admin key** from `admin123` to secure password
2. **Implement proper authentication** (JWT, sessions, etc.)
3. **Add rate limiting** to prevent spam submissions
4. **Use database** instead of in-memory storage
5. **Add image validation** and virus scanning
6. **Implement HTTPS** for secure data transmission

### Content Moderation
- Review all submissions before approval
- Check for inappropriate content
- Verify authenticity when possible
- Monitor for spam patterns

## Troubleshooting

### Reviews Not Showing
1. Check if reviews are approved in admin panel
2. Verify API endpoints are working
3. Check browser console for JavaScript errors
4. Ensure server is running properly

### Admin Panel Access Issues
1. Verify correct admin key
2. Check browser console for errors
3. Ensure `/admin/reviews` route is accessible

### Form Submission Problems
1. Verify all required fields are filled
2. Check network connectivity
3. Review server logs for errors
4. Ensure form validation is working

## Maintenance

### Regular Tasks
- Review and moderate pending submissions
- Monitor for spam or inappropriate content
- Backup review data regularly
- Update admin credentials periodically

### Performance
- Consider pagination for large numbers of reviews
- Optimize image sizes and formats
- Implement caching for frequently accessed reviews
- Monitor API response times
