'use client';

import { useState, useEffect, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import Image from 'next/image';
import { Package, MapPin, Clock, CheckCircle, Truck, Plane, Ship } from 'lucide-react';

interface TrackingData {
  order: {
    id: string;
    orderNumber: string;
    status: string;
    createdAt: string;
  };
  vehicle: {
    title: string;
    image: string;
  };
  shipping: {
    status: string;
    trackingNumber?: string;
    estimatedDelivery: string;
    method: string;
    address: {
      street: string;
      city: string;
      state: string;
      country: string;
      postalCode: string;
    };
    updates: Array<{
      id: string;
      timestamp: string;
      status: string;
      location: string;
      description: string;
    }>;
  };
  timeline: Array<{
    stage: string;
    description: string;
    estimatedDays: number;
    completed: boolean;
    current: boolean;
  }>;
  progress: {
    percentage: number;
    currentStage: string;
    nextStage: string;
  };
}

function TrackingPageContent({ locale }: { locale: string }) {
  const searchParams = useSearchParams();
  const orderId = searchParams.get('orderId');
  
  const [trackingData, setTrackingData] = useState<TrackingData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (orderId) {
      fetchTrackingData(orderId);
    } else {
      setError('Order ID is required');
      setLoading(false);
    }
  }, [orderId]);

  const fetchTrackingData = async (orderIdParam: string) => {
    try {
      const response = await fetch(`/api/tracking?orderId=${orderIdParam}`);
      const result = await response.json();
      
      if (result.success) {
        setTrackingData(result.data);
      } else {
        setError(result.message || 'Failed to fetch tracking data');
      }
    } catch (error) {
      console.error('Tracking fetch error:', error);
      setError('Failed to fetch tracking data');
    } finally {
      setLoading(false);
    }
  };

  const getShippingIcon = (method: string) => {
    if (method.toLowerCase().includes('air')) return <Plane className="w-6 h-6" />;
    if (method.toLowerCase().includes('sea')) return <Ship className="w-6 h-6" />;
    return <Truck className="w-6 h-6" />;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(locale === 'ja' ? 'ja-JP' : 'en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-neutral-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-primary-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-neutral-600">
            {locale === 'en' ? 'Loading tracking information...' : '追跡情報を読み込み中...'}
          </p>
        </div>
      </div>
    );
  }

  if (error || !trackingData) {
    return (
      <div className="min-h-screen bg-neutral-50 flex items-center justify-center">
        <div className="text-center">
          <Package className="w-16 h-16 text-neutral-400 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-neutral-800 mb-2">
            {locale === 'en' ? 'Tracking Not Found' : '追跡情報が見つかりません'}
          </h1>
          <p className="text-neutral-600 mb-4">
            {error || (locale === 'en' ? 'Unable to find tracking information for this order.' : 'この注文の追跡情報が見つかりません。')}
          </p>
          <button
            onClick={() => window.history.back()}
            className="px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
          >
            {locale === 'en' ? 'Go Back' : '戻る'}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-neutral-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-2xl font-bold text-neutral-800 flex items-center space-x-2">
              <Package className="w-6 h-6" />
              <span>{locale === 'en' ? 'Order Tracking' : '注文追跡'}</span>
            </h1>
            <div className="text-right">
              <p className="text-sm text-neutral-600">
                {locale === 'en' ? 'Order Number' : '注文番号'}
              </p>
              <p className="font-mono font-medium">{trackingData.order.orderNumber}</p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center space-x-3">
              <Image
                src={trackingData.vehicle.image}
                alt={trackingData.vehicle.title}
                width={64}
                height={64}
                className="w-16 h-16 object-cover rounded-lg"
              />
              <div>
                <h3 className="font-medium text-neutral-800">{trackingData.vehicle.title}</h3>
                <p className="text-sm text-neutral-600">
                  {locale === 'en' ? 'Ordered on' : '注文日'}: {formatDate(trackingData.order.createdAt)}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              {getShippingIcon(trackingData.shipping.method)}
              <div>
                <h4 className="font-medium text-neutral-800">{trackingData.shipping.method}</h4>
                <p className="text-sm text-neutral-600">
                  {locale === 'en' ? 'Estimated delivery' : '配送予定'}: {trackingData.shipping.estimatedDelivery}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <MapPin className="w-6 h-6 text-neutral-600" />
              <div>
                <h4 className="font-medium text-neutral-800">
                  {trackingData.shipping.address.city}, {trackingData.shipping.address.country}
                </h4>
                <p className="text-sm text-neutral-600">
                  {locale === 'en' ? 'Delivery address' : '配送先'}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-neutral-800">
              {locale === 'en' ? 'Shipping Progress' : '配送進捗'}
            </h2>
            <span className="text-sm text-neutral-600">
              {trackingData.progress.percentage}% {locale === 'en' ? 'Complete' : '完了'}
            </span>
          </div>

          <div className="w-full bg-neutral-200 rounded-full h-2 mb-4">
            <div
              className="bg-primary-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${trackingData.progress.percentage}%` }}
            ></div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {trackingData.timeline.map((stage) => (
              <div
                key={stage.status}
                className={`text-center p-3 rounded-lg ${
                  stage.completed
                    ? 'bg-green-50 border border-green-200'
                    : stage.current
                    ? 'bg-blue-50 border border-blue-200'
                    : 'bg-neutral-50 border border-neutral-200'
                }`}
              >
                <div className="flex justify-center mb-2">
                  {stage.completed ? (
                    <CheckCircle className="w-6 h-6 text-green-600" />
                  ) : stage.current ? (
                    <Clock className="w-6 h-6 text-blue-600" />
                  ) : (
                    <div className="w-6 h-6 border-2 border-neutral-300 rounded-full"></div>
                  )}
                </div>
                <h4 className="font-medium text-sm text-neutral-800 mb-1">{stage.stage}</h4>
                <p className="text-xs text-neutral-600">{stage.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Shipping Updates */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-semibold text-neutral-800 mb-4">
            {locale === 'en' ? 'Shipping Updates' : '配送更新'}
          </h2>

          {trackingData.shipping.updates.length > 0 ? (
            <div className="space-y-4">
              {trackingData.shipping.updates.map((update) => (
                <div key={update.id} className="flex items-start space-x-4 pb-4 border-b border-neutral-200 last:border-b-0">
                  <div className="w-2 h-2 bg-primary-600 rounded-full mt-2 flex-shrink-0"></div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <h4 className="font-medium text-neutral-800">{update.status}</h4>
                      <span className="text-sm text-neutral-500">{formatDate(update.timestamp)}</span>
                    </div>
                    <p className="text-sm text-neutral-600 mb-1">{update.description}</p>
                    <p className="text-xs text-neutral-500 flex items-center space-x-1">
                      <MapPin className="w-3 h-3" />
                      <span>{update.location}</span>
                    </p>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-neutral-600 text-center py-8">
              {locale === 'en' ? 'No shipping updates available yet.' : 'まだ配送更新はありません。'}
            </p>
          )}
        </div>
      </div>
    </div>
  );
}

export default function TrackingPage({ params }: { params: { locale: string } }) {
  const { locale } = params;

  return (
    <Suspense fallback={
      <div className="min-h-screen bg-neutral-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-primary-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-neutral-600">
            {locale === 'en' ? 'Loading tracking information...' : '追跡情報を読み込み中...'}
          </p>
        </div>
      </div>
    }>
      <TrackingPageContent locale={locale} />
    </Suspense>
  );
}
