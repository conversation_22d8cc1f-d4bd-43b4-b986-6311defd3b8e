'use client';

import { useState } from 'react';
import { Send, CheckCircle, AlertCircle, Loader2 } from 'lucide-react';

interface ContactFormProps {
  locale: string;
}

interface FormData {
  name: string;
  email: string;
  phone: string;
  company: string;
  subject: string;
  message: string;
}

export default function ContactForm({ locale }: ContactFormProps) {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    phone: '',
    company: '',
    subject: '',
    message: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (error) setError(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    try {
      // Create FormData object
      const submitData = new FormData();
      submitData.append('name', formData.name);
      submitData.append('email', formData.email);
      submitData.append('phone', formData.phone);
      submitData.append('company', formData.company);
      submitData.append('subject', formData.subject);
      submitData.append('message', formData.message);

      const response = await fetch('/api/contact', {
        method: 'POST',
        body: submitData
      });

      const result = await response.json();

      if (result.success) {
        setIsSubmitted(true);
        // Reset form
        setFormData({
          name: '',
          email: '',
          phone: '',
          company: '',
          subject: '',
          message: ''
        });
      } else {
        setError(result.message || 'Failed to send message. Please try again.');
      }
    } catch (err) {
      console.error('Contact form error:', err);
      setError('Failed to send message. Please check your connection and try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const isJapanese = locale === 'ja';

  const texts = {
    title: isJapanese ? 'お問い合わせフォーム' : 'Contact Form',
    subtitle: isJapanese 
      ? '詳細なメッセージをお送りください。24時間以内にご返信いたします' 
      : 'Send us a detailed message and we\'ll get back to you within 24 hours',
    name: isJapanese ? 'お名前' : 'Full Name',
    namePlaceholder: isJapanese ? 'お名前をご入力ください' : 'Your full name',
    email: isJapanese ? 'メールアドレス' : 'Email Address',
    emailPlaceholder: isJapanese ? 'メールアドレスをご入力ください' : '<EMAIL>',
    phone: isJapanese ? '電話番号（任意）' : 'Phone Number (Optional)',
    phonePlaceholder: isJapanese ? '電話番号をご入力ください' : 'Your phone number',
    company: isJapanese ? '会社名（任意）' : 'Company (Optional)',
    companyPlaceholder: isJapanese ? '会社名をご入力ください' : 'Your company name',
    subject: isJapanese ? '件名' : 'Subject',
    subjectPlaceholder: isJapanese ? '件名をご入力ください' : 'What is this regarding?',
    message: isJapanese ? 'メッセージ' : 'Message',
    messagePlaceholder: isJapanese 
      ? 'お問い合わせ内容を詳しくご記入ください...' 
      : 'Please provide details about your inquiry...',
    submit: isJapanese ? 'メッセージを送信' : 'Send Message',
    submitting: isJapanese ? '送信中...' : 'Sending...',
    successTitle: isJapanese ? 'メッセージを送信しました！' : 'Message Sent Successfully!',
    successMessage: isJapanese 
      ? 'お問い合わせありがとうございます。24時間以内にご返信いたします。' 
      : 'Thank you for your message! We will respond within 24 hours.',
    required: isJapanese ? '必須' : 'Required'
  };

  if (isSubmitted) {
    return (
      <div className="bg-neutral-50 rounded-2xl p-8 text-center">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <CheckCircle className="w-8 h-8 text-green-600" />
        </div>
        <h3 className="text-2xl font-bold text-neutral-800 mb-2">
          {texts.successTitle}
        </h3>
        <p className="text-neutral-600 mb-6">
          {texts.successMessage}
        </p>
        <button
          onClick={() => setIsSubmitted(false)}
          className="text-primary-600 hover:text-primary-700 font-semibold"
        >
          {isJapanese ? '新しいメッセージを送信' : 'Send Another Message'}
        </button>
      </div>
    );
  }

  return (
    <div className="bg-neutral-50 rounded-2xl p-8">
      <div className="text-center mb-8">
        <h2 className="text-3xl lg:text-4xl font-heading font-bold text-neutral-800 mb-4">
          {texts.title}
        </h2>
        <p className="text-xl text-neutral-600">
          {texts.subtitle}
        </p>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center">
          <AlertCircle className="w-5 h-5 text-red-600 mr-3 flex-shrink-0" />
          <p className="text-red-700">{error}</p>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-neutral-700 font-semibold mb-2">
              {texts.name} <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              required
              className="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors"
              placeholder={texts.namePlaceholder}
            />
          </div>
          <div>
            <label className="block text-neutral-700 font-semibold mb-2">
              {texts.email} <span className="text-red-500">*</span>
            </label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              required
              className="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors"
              placeholder={texts.emailPlaceholder}
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-neutral-700 font-semibold mb-2">
              {texts.phone}
            </label>
            <input
              type="tel"
              name="phone"
              value={formData.phone}
              onChange={handleInputChange}
              className="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors"
              placeholder={texts.phonePlaceholder}
            />
          </div>
          <div>
            <label className="block text-neutral-700 font-semibold mb-2">
              {texts.company}
            </label>
            <input
              type="text"
              name="company"
              value={formData.company}
              onChange={handleInputChange}
              className="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors"
              placeholder={texts.companyPlaceholder}
            />
          </div>
        </div>

        <div>
          <label className="block text-neutral-700 font-semibold mb-2">
            {texts.subject} <span className="text-red-500">*</span>
          </label>
          <select
            name="subject"
            value={formData.subject}
            onChange={handleInputChange}
            required
            className="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors"
          >
            <option value="">{texts.subjectPlaceholder}</option>
            <option value="Vehicle Inquiry">{isJapanese ? '車両のお問い合わせ' : 'Vehicle Inquiry'}</option>
            <option value="Shipping Information">{isJapanese ? '配送について' : 'Shipping Information'}</option>
            <option value="Pricing Question">{isJapanese ? '価格について' : 'Pricing Question'}</option>
            <option value="Partnership Inquiry">{isJapanese ? 'パートナーシップについて' : 'Partnership Inquiry'}</option>
            <option value="Technical Support">{isJapanese ? '技術サポート' : 'Technical Support'}</option>
            <option value="Other">{isJapanese ? 'その他' : 'Other'}</option>
          </select>
        </div>

        <div>
          <label className="block text-neutral-700 font-semibold mb-2">
            {texts.message} <span className="text-red-500">*</span>
          </label>
          <textarea
            name="message"
            value={formData.message}
            onChange={handleInputChange}
            required
            rows={6}
            className="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors resize-vertical"
            placeholder={texts.messagePlaceholder}
          />
        </div>

        <button
          type="submit"
          disabled={isSubmitting}
          className="w-full bg-primary-600 hover:bg-primary-700 disabled:bg-primary-400 text-white font-semibold py-4 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center"
        >
          {isSubmitting ? (
            <>
              <Loader2 className="w-5 h-5 mr-2 animate-spin" />
              {texts.submitting}
            </>
          ) : (
            <>
              <Send className="w-5 h-5 mr-2" />
              {texts.submit}
            </>
          )}
        </button>
      </form>
    </div>
  );
}
