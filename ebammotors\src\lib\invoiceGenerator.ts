import jsPDF from 'jspdf';
import { Order, Invoice, InvoiceItem } from '@/types/payment';
import { createInvoice, updateInvoice, getInvoiceByOrderId } from './orderStorage';

/**
 * Generate invoice items from order
 */
function generateInvoiceItems(order: Order): InvoiceItem[] {
  const items: InvoiceItem[] = [
    {
      id: '1',
      description: order.vehicle.title,
      quantity: 1,
      unitPrice: order.vehicle.price,
      total: order.vehicle.price,
    },
  ];

  if (order.shipping.cost > 0) {
    items.push({
      id: '2',
      description: `Shipping (${order.shipping.method.name})`,
      quantity: 1,
      unitPrice: order.shipping.cost,
      total: order.shipping.cost,
    });
  }

  return items;
}

/**
 * Calculate invoice totals
 */
function calculateInvoiceTotals(items: InvoiceItem[]): {
  subtotal: number;
  tax: number;
  total: number;
} {
  const subtotal = items.reduce((sum, item) => sum + item.total, 0);
  const tax = 0; // No tax for international sales
  const total = subtotal + tax;

  return { subtotal, tax, total };
}

/**
 * Generate invoice data from order
 */
export async function generateInvoiceFromOrder(order: Order): Promise<Invoice> {
  const items = generateInvoiceItems(order);
  const { subtotal, tax, total } = calculateInvoiceTotals(items);

  const invoiceData = {
    orderId: order.id,
    issuedDate: new Date().toISOString().split('T')[0],
    dueDate: order.payment.dueDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days from now
    status: 'sent' as const,
    items,
    subtotal,
    tax,
    shipping: order.shipping.cost,
    total,
    currency: order.currency,
    paymentTerms: 'Payment due within 30 days of invoice date',
    notes: `Order: ${order.orderNumber}\nVehicle: ${order.vehicle.title}\nShipping to: ${order.shipping.address.city}, ${order.shipping.address.country}`,
  };

  return await createInvoice(invoiceData);
}

/**
 * Generate PDF invoice
 */
export function generateInvoicePDF(invoice: Invoice, order: Order): jsPDF {
  const doc = new jsPDF();
  const pageWidth = doc.internal.pageSize.width;
  const margin = 20;

  // Company header
  doc.setFontSize(24);
  doc.setFont('helvetica', 'bold');
  doc.text('EBAM MOTORS', margin, 30);
  
  doc.setFontSize(12);
  doc.setFont('helvetica', 'normal');
  doc.text('Premium Japanese Vehicles Export', margin, 40);
  doc.text('Japan Office: Tokyo, Japan', margin, 50);
  doc.text('Ghana Office: Kumasi, Ghana', margin, 60);
  doc.text('Phone: +233245375692', margin, 70);
  doc.text('Email: <EMAIL>', margin, 80);

  // Invoice title and number
  doc.setFontSize(20);
  doc.setFont('helvetica', 'bold');
  doc.text('INVOICE', pageWidth - margin - 40, 30);
  
  doc.setFontSize(12);
  doc.setFont('helvetica', 'normal');
  doc.text(`Invoice #: ${invoice.invoiceNumber}`, pageWidth - margin - 60, 45);
  doc.text(`Date: ${invoice.issuedDate}`, pageWidth - margin - 60, 55);
  doc.text(`Due Date: ${invoice.dueDate}`, pageWidth - margin - 60, 65);
  doc.text(`Order #: ${order.orderNumber}`, pageWidth - margin - 60, 75);

  // Customer information
  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  doc.text('Bill To:', margin, 110);
  
  doc.setFontSize(12);
  doc.setFont('helvetica', 'normal');
  doc.text(order.customerInfo.name, margin, 125);
  doc.text(order.customerInfo.email, margin, 135);
  doc.text(order.customerInfo.phone, margin, 145);
  doc.text(order.customerInfo.address.street, margin, 155);
  doc.text(`${order.customerInfo.address.city}, ${order.customerInfo.address.state}`, margin, 165);
  doc.text(`${order.customerInfo.address.country} ${order.customerInfo.address.postalCode}`, margin, 175);

  // Shipping information
  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  doc.text('Ship To:', pageWidth - margin - 80, 110);
  
  doc.setFontSize(12);
  doc.setFont('helvetica', 'normal');
  doc.text(order.shipping.address.street, pageWidth - margin - 80, 125);
  doc.text(`${order.shipping.address.city}, ${order.shipping.address.state}`, pageWidth - margin - 80, 135);
  doc.text(`${order.shipping.address.country} ${order.shipping.address.postalCode}`, pageWidth - margin - 80, 145);

  // Items table
  const tableTop = 200;
  const itemHeight = 20;
  
  // Table headers
  doc.setFontSize(12);
  doc.setFont('helvetica', 'bold');
  doc.text('Description', margin, tableTop);
  doc.text('Qty', pageWidth - 120, tableTop);
  doc.text('Unit Price', pageWidth - 80, tableTop);
  doc.text('Total', pageWidth - 40, tableTop);
  
  // Draw header line
  doc.line(margin, tableTop + 5, pageWidth - margin, tableTop + 5);

  // Table items
  doc.setFont('helvetica', 'normal');
  let currentY = tableTop + 15;
  
  invoice.items.forEach((item, index) => {
    doc.text(item.description, margin, currentY);
    doc.text(item.quantity.toString(), pageWidth - 120, currentY);
    doc.text(`¥${item.unitPrice.toLocaleString()}`, pageWidth - 80, currentY);
    doc.text(`¥${item.total.toLocaleString()}`, pageWidth - 40, currentY);
    currentY += itemHeight;
  });

  // Draw line before totals
  doc.line(margin, currentY, pageWidth - margin, currentY);
  currentY += 10;

  // Totals
  doc.setFont('helvetica', 'normal');
  doc.text('Subtotal:', pageWidth - 80, currentY);
  doc.text(`¥${invoice.subtotal.toLocaleString()}`, pageWidth - 40, currentY);
  currentY += 15;

  if (invoice.tax > 0) {
    doc.text('Tax:', pageWidth - 80, currentY);
    doc.text(`¥${invoice.tax.toLocaleString()}`, pageWidth - 40, currentY);
    currentY += 15;
  }

  doc.setFont('helvetica', 'bold');
  doc.text('Total:', pageWidth - 80, currentY);
  doc.text(`¥${invoice.total.toLocaleString()}`, pageWidth - 40, currentY);

  // Payment terms
  currentY += 30;
  doc.setFontSize(10);
  doc.setFont('helvetica', 'normal');
  doc.text('Payment Terms:', margin, currentY);
  currentY += 10;
  doc.text(invoice.paymentTerms, margin, currentY);

  // Notes
  if (invoice.notes) {
    currentY += 20;
    doc.text('Notes:', margin, currentY);
    currentY += 10;
    const splitNotes = doc.splitTextToSize(invoice.notes, pageWidth - 2 * margin);
    doc.text(splitNotes, margin, currentY);
  }

  // Footer
  const footerY = doc.internal.pageSize.height - 30;
  doc.setFontSize(8);
  doc.text('Thank you for your business!', margin, footerY);
  doc.text('For questions about this invoice, please contact <NAME_EMAIL>', margin, footerY + 10);

  return doc;
}

/**
 * Save invoice PDF to file system (for development/local storage)
 */
export async function saveInvoicePDF(invoice: Invoice, order: Order): Promise<string> {
  const doc = generateInvoicePDF(invoice, order);
  const pdfBlob = doc.output('blob');
  
  // In a real application, you would upload this to cloud storage
  // For now, we'll return a mock URL
  const mockUrl = `/invoices/${invoice.invoiceNumber}.pdf`;
  
  // Update invoice with PDF URL
  await updateInvoice(invoice.id, { pdfUrl: mockUrl });
  
  return mockUrl;
}

/**
 * Generate and email invoice
 */
export async function generateAndEmailInvoice(order: Order): Promise<Invoice> {
  // Generate invoice
  const invoice = await generateInvoiceFromOrder(order);
  
  // Generate PDF
  const pdfUrl = await saveInvoicePDF(invoice, order);
  
  // In a real application, you would send email here
  console.log(`Invoice ${invoice.invoiceNumber} generated for order ${order.orderNumber}`);
  console.log(`PDF URL: ${pdfUrl}`);
  
  return invoice;
}

/**
 * Mark invoice as paid
 */
export async function markInvoiceAsPaid(invoiceId: string): Promise<boolean> {
  return await updateInvoice(invoiceId, { 
    status: 'paid',
  });
}

/**
 * Get invoice by order ID (re-export from orderStorage)
 */
export { getInvoiceByOrderId };

/**
 * Get invoice summary for display
 */
export function getInvoiceSummary(invoice: Invoice): {
  totalItems: number;
  totalAmount: string;
  daysUntilDue: number;
  isOverdue: boolean;
} {
  const totalItems = invoice.items.length;
  const totalAmount = `¥${invoice.total.toLocaleString()}`;

  const dueDate = new Date(invoice.dueDate);
  const today = new Date();
  const daysUntilDue = Math.ceil((dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
  const isOverdue = daysUntilDue < 0;

  return {
    totalItems,
    totalAmount,
    daysUntilDue,
    isOverdue,
  };
}
