'use client';

import React, { useState, useEffect } from 'react';
import { 
  Upload, 
  Download, 
  Plus, 
  Search, 
  Filter, 
  Edit, 
  Trash2, 
  Eye,
  FileText,
  AlertCircle,
  CheckCircle,
  Clock,
  Car,
  Database
} from 'lucide-react';

interface Car {
  id: string;
  car_id: string;
  make: string;
  model: string;
  year: number;
  title: string;
  price: number;
  status: string;
  mileage?: number;
  fuel_type?: string;
  transmission?: string;
  body_condition?: string;
  location: string;
  is_featured: boolean;
  is_recently_added: boolean;
  stock_quantity: number;
  added_date: string;
  main_image?: string;
}

interface ImportRecord {
  id: string;
  batch_id: string;
  filename: string;
  total_rows: number;
  successful_imports: number;
  failed_imports: number;
  status: 'processing' | 'completed' | 'failed';
  started_at: string;
  completed_at?: string;
  imported_by: string;
}

export default function AdminCarsPage() {
  const [authenticated, setAuthenticated] = useState(false);
  const [adminKey, setAdminKey] = useState('');
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'inventory' | 'import' | 'history'>('inventory');

  // Inventory state
  const [cars, setCars] = useState<Car[]>([]);
  const [totalCars, setTotalCars] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState({
    make: '',
    model: '',
    status: '',
    year_min: '',
    year_max: '',
    price_min: '',
    price_max: '',
  });

  // Import state
  const [csvFile, setCsvFile] = useState<File | null>(null);
  const [importLoading, setImportLoading] = useState(false);
  const [importResult, setImportResult] = useState<any>(null);
  const [importHistory, setImportHistory] = useState<ImportRecord[]>([]);

  // Selected cars for bulk operations
  const [selectedCars, setSelectedCars] = useState<string[]>([]);

  const handleAuth = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await fetch('/api/admin/auth', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ password: adminKey }),
      });

      const data = await response.json();
      if (data.success) {
        setAuthenticated(true);
        await loadCars();
        await loadImportHistory();
      } else {
        alert('Invalid admin password');
      }
    } catch (error) {
      console.error('Authentication error:', error);
      alert('Authentication failed');
    } finally {
      setLoading(false);
    }
  };

  const loadCars = async (page = 1) => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        adminKey,
        page: page.toString(),
        per_page: '20',
        search: searchQuery,
        ...Object.fromEntries(Object.entries(filters).filter(([_, v]) => v !== '')),
      });

      const response = await fetch(`/api/cars?${params}`);
      const data = await response.json();

      if (data.success) {
        setCars(data.cars);
        setTotalCars(data.total_count);
        setCurrentPage(page);
      }
    } catch (error) {
      console.error('Error loading cars:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadImportHistory = async () => {
    try {
      const response = await fetch(`/api/cars/import?adminKey=${encodeURIComponent(adminKey)}`);
      const data = await response.json();

      if (data.success) {
        setImportHistory(data.imports);
      }
    } catch (error) {
      console.error('Error loading import history:', error);
    }
  };

  const handleCSVUpload = async () => {
    if (!csvFile) {
      alert('Please select a CSV file');
      return;
    }

    setImportLoading(true);
    setImportResult(null);

    try {
      const formData = new FormData();
      formData.append('csvFile', csvFile);
      formData.append('adminKey', adminKey);
      formData.append('importedBy', 'admin');

      const response = await fetch('/api/cars/import', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();
      setImportResult(result);

      if (result.success) {
        await loadCars();
        await loadImportHistory();
        setCsvFile(null);
      }
    } catch (error) {
      console.error('Error uploading CSV:', error);
      setImportResult({
        success: false,
        message: 'Failed to upload CSV file',
      });
    } finally {
      setImportLoading(false);
    }
  };

  const downloadTemplate = async (withSamples = false) => {
    try {
      const response = await fetch(
        `/api/cars/template?adminKey=${encodeURIComponent(adminKey)}&with_samples=${withSamples}`
      );

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `car_import_template${withSamples ? '_with_samples' : ''}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error('Error downloading template:', error);
    }
  };

  const handleBulkDelete = async () => {
    if (selectedCars.length === 0) {
      alert('Please select cars to delete');
      return;
    }

    if (!confirm(`Are you sure you want to delete ${selectedCars.length} cars?`)) {
      return;
    }

    try {
      const response = await fetch('/api/cars', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          adminKey,
          ids: selectedCars,
        }),
      });

      const data = await response.json();
      if (data.success) {
        await loadCars();
        setSelectedCars([]);
        alert(`Successfully deleted ${data.deleted_count} cars`);
      } else {
        alert('Failed to delete cars: ' + data.message);
      }
    } catch (error) {
      console.error('Error deleting cars:', error);
      alert('Failed to delete cars');
    }
  };

  const toggleCarSelection = (carId: string) => {
    setSelectedCars(prev => 
      prev.includes(carId) 
        ? prev.filter(id => id !== carId)
        : [...prev, carId]
    );
  };

  const selectAllCars = () => {
    if (selectedCars.length === cars.length) {
      setSelectedCars([]);
    } else {
      setSelectedCars(cars.map(car => car.id));
    }
  };

  useEffect(() => {
    if (authenticated) {
      loadCars();
    }
  }, [searchQuery, filters]);

  if (!authenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6">
          <div className="text-center mb-6">
            <Car className="w-12 h-12 text-primary-600 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-900">Car Inventory Admin</h1>
            <p className="text-gray-600">Enter admin password to continue</p>
          </div>

          <form onSubmit={handleAuth}>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Admin Password
              </label>
              <input
                type="password"
                value={adminKey}
                onChange={(e) => setAdminKey(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              />
            </div>

            <button
              type="submit"
              disabled={loading}
              className="w-full bg-primary-600 text-white py-2 px-4 rounded-md hover:bg-primary-700 disabled:opacity-50"
            >
              {loading ? 'Authenticating...' : 'Access Admin Panel'}
            </button>
          </form>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <Database className="w-8 h-8 text-primary-600" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Car Inventory Management</h1>
                <p className="text-sm text-gray-600">Manage your car inventory and bulk imports</p>
              </div>
            </div>
            <div className="text-sm text-gray-500">
              Total Cars: {totalCars.toLocaleString()}
            </div>
          </div>

          {/* Tabs */}
          <div className="flex space-x-8">
            {[
              { id: 'inventory', label: 'Inventory', icon: Car },
              { id: 'import', label: 'Bulk Import', icon: Upload },
              { id: 'history', label: 'Import History', icon: FileText },
            ].map(({ id, label, icon: Icon }) => (
              <button
                key={id}
                onClick={() => setActiveTab(id as any)}
                className={`flex items-center space-x-2 py-4 px-2 border-b-2 font-medium text-sm ${
                  activeTab === id
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span>{label}</span>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'inventory' && (
          <div className="space-y-6">
            {/* Search and Filters */}
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                <div className="flex-1 max-w-lg">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <input
                      type="text"
                      placeholder="Search cars..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  <button
                    onClick={() => loadCars()}
                    disabled={loading}
                    className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50"
                  >
                    Refresh
                  </button>

                  {selectedCars.length > 0 && (
                    <button
                      onClick={handleBulkDelete}
                      className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 flex items-center space-x-2"
                    >
                      <Trash2 className="w-4 h-4" />
                      <span>Delete Selected ({selectedCars.length})</span>
                    </button>
                  )}
                </div>
              </div>

              {/* Quick Filters */}
              <div className="mt-4 grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
                <select
                  value={filters.make}
                  onChange={(e) => setFilters(prev => ({ ...prev, make: e.target.value }))}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="">All Makes</option>
                  <option value="Toyota">Toyota</option>
                  <option value="Honda">Honda</option>
                  <option value="Nissan">Nissan</option>
                </select>

                <select
                  value={filters.status}
                  onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="">All Status</option>
                  <option value="Available">Available</option>
                  <option value="Reserved">Reserved</option>
                  <option value="Sold">Sold</option>
                </select>

                <input
                  type="number"
                  placeholder="Min Year"
                  value={filters.year_min}
                  onChange={(e) => setFilters(prev => ({ ...prev, year_min: e.target.value }))}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                />

                <input
                  type="number"
                  placeholder="Max Year"
                  value={filters.year_max}
                  onChange={(e) => setFilters(prev => ({ ...prev, year_max: e.target.value }))}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                />

                <input
                  type="number"
                  placeholder="Min Price"
                  value={filters.price_min}
                  onChange={(e) => setFilters(prev => ({ ...prev, price_min: e.target.value }))}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                />

                <input
                  type="number"
                  placeholder="Max Price"
                  value={filters.price_max}
                  onChange={(e) => setFilters(prev => ({ ...prev, price_max: e.target.value }))}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </div>
            </div>

            {/* Cars Table */}
            <div className="bg-white rounded-lg shadow overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-gray-900">
                    Cars ({totalCars.toLocaleString()})
                  </h3>
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={selectedCars.length === cars.length && cars.length > 0}
                      onChange={selectAllCars}
                      className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                    />
                    <span className="text-sm text-gray-600">Select All</span>
                  </div>
                </div>
              </div>

              {loading ? (
                <div className="p-8 text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
                  <p className="mt-2 text-gray-600">Loading cars...</p>
                </div>
              ) : cars.length === 0 ? (
                <div className="p-8 text-center">
                  <Car className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">No cars found</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Select
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Car
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Details
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Price
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {cars.map((car) => (
                        <tr key={car.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <input
                              type="checkbox"
                              checked={selectedCars.includes(car.id)}
                              onChange={() => toggleCarSelection(car.id)}
                              className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                            />
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              {car.main_image ? (
                                <img
                                  src={car.main_image}
                                  alt={car.title}
                                  className="w-12 h-12 rounded-lg object-cover mr-4"
                                />
                              ) : (
                                <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center mr-4">
                                  <Car className="w-6 h-6 text-gray-400" />
                                </div>
                              )}
                              <div>
                                <div className="text-sm font-medium text-gray-900">
                                  {car.title}
                                </div>
                                <div className="text-sm text-gray-500">
                                  ID: {car.car_id}
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <div>
                              <div>{car.year} • {car.mileage ? `${Math.floor(car.mileage / 1000)}k km` : 'N/A'}</div>
                              <div className="text-gray-500">
                                {car.fuel_type} • {car.transmission}
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            ¥{car.price.toLocaleString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              car.status === 'Available' ? 'bg-green-100 text-green-800' :
                              car.status === 'Reserved' ? 'bg-yellow-100 text-yellow-800' :
                              car.status === 'Sold' ? 'bg-red-100 text-red-800' :
                              'bg-gray-100 text-gray-800'
                            }`}>
                              {car.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div className="flex items-center space-x-2">
                              <button className="text-primary-600 hover:text-primary-900">
                                <Eye className="w-4 h-4" />
                              </button>
                              <button className="text-gray-600 hover:text-gray-900">
                                <Edit className="w-4 h-4" />
                              </button>
                              <button className="text-red-600 hover:text-red-900">
                                <Trash2 className="w-4 h-4" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}

              {/* Pagination */}
              {totalCars > 20 && (
                <div className="px-6 py-4 border-t border-gray-200">
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-700">
                      Showing {((currentPage - 1) * 20) + 1} to {Math.min(currentPage * 20, totalCars)} of {totalCars} cars
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => loadCars(currentPage - 1)}
                        disabled={currentPage === 1}
                        className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50"
                      >
                        Previous
                      </button>
                      <span className="px-3 py-1 text-sm">
                        Page {currentPage} of {Math.ceil(totalCars / 20)}
                      </span>
                      <button
                        onClick={() => loadCars(currentPage + 1)}
                        disabled={currentPage >= Math.ceil(totalCars / 20)}
                        className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50"
                      >
                        Next
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'import' && (
          <div className="space-y-6">
            {/* CSV Import Section */}
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">Bulk CSV Import</h3>
                  <p className="text-sm text-gray-600">Upload a CSV file to import multiple cars at once</p>
                </div>
                <div className="flex items-center space-x-4">
                  <button
                    onClick={() => downloadTemplate(false)}
                    className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 flex items-center space-x-2"
                  >
                    <Download className="w-4 h-4" />
                    <span>Download Template</span>
                  </button>
                  <button
                    onClick={() => downloadTemplate(true)}
                    className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 flex items-center space-x-2"
                  >
                    <Download className="w-4 h-4" />
                    <span>Template with Samples</span>
                  </button>
                </div>
              </div>

              {/* File Upload */}
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
                <div className="text-center">
                  <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <div className="mb-4">
                    <label htmlFor="csv-file" className="cursor-pointer">
                      <span className="text-primary-600 hover:text-primary-700 font-medium">
                        Click to upload CSV file
                      </span>
                      <input
                        id="csv-file"
                        type="file"
                        accept=".csv"
                        onChange={(e) => setCsvFile(e.target.files?.[0] || null)}
                        className="hidden"
                      />
                    </label>
                    <p className="text-gray-500">or drag and drop</p>
                  </div>
                  <p className="text-xs text-gray-500">CSV files only, max 10MB</p>
                </div>

                {csvFile && (
                  <div className="mt-4 p-4 bg-gray-50 rounded-md">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <FileText className="w-4 h-4 text-gray-600" />
                        <span className="text-sm font-medium">{csvFile.name}</span>
                        <span className="text-xs text-gray-500">
                          ({(csvFile.size / 1024).toFixed(1)} KB)
                        </span>
                      </div>
                      <button
                        onClick={() => setCsvFile(null)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                )}
              </div>

              {/* Upload Button */}
              <div className="mt-6 flex justify-end">
                <button
                  onClick={handleCSVUpload}
                  disabled={!csvFile || importLoading}
                  className="px-6 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50 flex items-center space-x-2"
                >
                  {importLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      <span>Processing...</span>
                    </>
                  ) : (
                    <>
                      <Upload className="w-4 h-4" />
                      <span>Import Cars</span>
                    </>
                  )}
                </button>
              </div>
            </div>

            {/* Import Result */}
            {importResult && (
              <div className={`rounded-lg p-6 ${
                importResult.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
              }`}>
                <div className="flex items-start space-x-3">
                  {importResult.success ? (
                    <CheckCircle className="w-6 h-6 text-green-600 flex-shrink-0 mt-0.5" />
                  ) : (
                    <AlertCircle className="w-6 h-6 text-red-600 flex-shrink-0 mt-0.5" />
                  )}
                  <div className="flex-1">
                    <h4 className={`font-medium ${
                      importResult.success ? 'text-green-900' : 'text-red-900'
                    }`}>
                      {importResult.message}
                    </h4>

                    {importResult.batch_id && (
                      <div className="mt-2 text-sm text-gray-600">
                        <p>Batch ID: {importResult.batch_id}</p>
                        <p>Total Rows: {importResult.total_rows}</p>
                        <p>Successful: {importResult.successful_imports}</p>
                        <p>Failed: {importResult.failed_imports}</p>
                      </div>
                    )}

                    {importResult.errors && importResult.errors.length > 0 && (
                      <div className="mt-4">
                        <h5 className="font-medium text-red-900 mb-2">Errors:</h5>
                        <div className="max-h-40 overflow-y-auto">
                          {importResult.errors.slice(0, 10).map((error: string, index: number) => (
                            <p key={index} className="text-sm text-red-700 mb-1">
                              • {error}
                            </p>
                          ))}
                          {importResult.errors.length > 10 && (
                            <p className="text-sm text-red-600 italic">
                              ... and {importResult.errors.length - 10} more errors
                            </p>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'history' && (
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">Import History</h3>
                <p className="text-sm text-gray-600">Track all CSV import operations</p>
              </div>

              {importHistory.length === 0 ? (
                <div className="p-8 text-center">
                  <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">No import history found</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          File
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Results
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Date
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Imported By
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {importHistory.map((record) => (
                        <tr key={record.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <FileText className="w-4 h-4 text-gray-400 mr-2" />
                              <div>
                                <div className="text-sm font-medium text-gray-900">
                                  {record.filename}
                                </div>
                                <div className="text-sm text-gray-500">
                                  {record.total_rows} rows
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              record.status === 'completed' ? 'bg-green-100 text-green-800' :
                              record.status === 'failed' ? 'bg-red-100 text-red-800' :
                              'bg-yellow-100 text-yellow-800'
                            }`}>
                              {record.status === 'processing' && <Clock className="w-3 h-3 mr-1" />}
                              {record.status === 'completed' && <CheckCircle className="w-3 h-3 mr-1" />}
                              {record.status === 'failed' && <AlertCircle className="w-3 h-3 mr-1" />}
                              {record.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <div>
                              <div className="text-green-600">✓ {record.successful_imports} success</div>
                              {record.failed_imports > 0 && (
                                <div className="text-red-600">✗ {record.failed_imports} failed</div>
                              )}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {new Date(record.started_at).toLocaleString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {record.imported_by}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
