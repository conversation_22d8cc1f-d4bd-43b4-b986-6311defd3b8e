'use client';

import { useState, useRef, useEffect, useMemo } from 'react';
import { MessageCircle, X, Send } from 'lucide-react';
import ChatbotKnowledge from './ChatbotKnowledge';
import VoiceRecognition from './VoiceRecognition';
import LeadCaptureForm from './LeadCaptureForm';
import { getMessagesSync } from '@/lib/messages';
import { formatDateTime } from '@/lib/dateUtils';

interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
  isTyping?: boolean;
}

interface ChatbotProps {
  locale: string;
  currentPage: string;
}

export default function Chatbot({ locale, currentPage }: ChatbotProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputText, setInputText] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [showLeadForm, setShowLeadForm] = useState(false);
  const [leadProductInterest, setLeadProductInterest] = useState('');
  const [messageCounter, setMessageCounter] = useState(0);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const knowledge = useMemo(() => new ChatbotKnowledge(locale, currentPage), [locale, currentPage]);
  const uiMessages = getMessagesSync(locale);

  // Welcome message when chatbot opens
  useEffect(() => {
    if (isOpen && messages.length === 0) {
      const welcomeMessage = knowledge.getWelcomeMessage();
      setMessages([{
        id: 'welcome-message',
        text: welcomeMessage,
        isUser: false,
        timestamp: new Date()
      }]);
    }
  }, [isOpen, messages.length, knowledge]);

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Focus input when opened
  useEffect(() => {
    if (isOpen) {
      setTimeout(() => inputRef.current?.focus(), 100);
    }
  }, [isOpen]);

  const handleSendMessage = async (text: string) => {
    if (!text.trim()) return;

    const userMessage: Message = {
      id: `user-${messageCounter}`,
      text: text.trim(),
      isUser: true,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setMessageCounter(prev => prev + 1);
    setInputText('');
    setIsTyping(true);

    // Simulate typing delay
    setTimeout(async () => {
      const response = await knowledge.getResponse(text.trim());

      // Check if this should trigger lead capture
      const shouldShowLeadForm = knowledge.shouldTriggerLeadCapture(text.trim());
      if (shouldShowLeadForm) {
        setLeadProductInterest(knowledge.extractProductInterest(text.trim()));
        setShowLeadForm(true);
      }

      // Log chatbot interaction to CRM
      try {
        await fetch('/api/interactions', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            type: 'chat',
            direction: 'inbound',
            channel: 'website',
            content: text.trim(),
            subject: 'Chatbot Conversation',
            tags: ['chatbot', 'user_message'],
            createdBy: 'anonymous',
          }),
        });

        await fetch('/api/interactions', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            type: 'chat',
            direction: 'outbound',
            channel: 'website',
            content: response,
            subject: 'Chatbot Response',
            tags: ['chatbot', 'bot_response'],
            createdBy: 'system',
          }),
        });
      } catch (error) {
        console.error('Failed to log chatbot interaction:', error);
      }

      const botMessage: Message = {
        id: `bot-${messageCounter}`,
        text: response,
        isUser: false,
        timestamp: new Date()
      };

      setMessageCounter(prev => prev + 1);

      setMessages(prev => [...prev, botMessage]);
      setIsTyping(false);

      // Text-to-speech for bot responses (optional)
      if ('speechSynthesis' in window) {
        const utterance = new SpeechSynthesisUtterance(response);
        utterance.lang = locale === 'ja' ? 'ja-JP' : 'en-US';
        utterance.rate = 0.8;
        utterance.volume = 0.7;
        // Uncomment to enable auto-speech
        // speechSynthesis.speak(utterance);
      }
    }, 1000 + Math.random() * 1000); // Random delay for natural feel
  };

  const handleVoiceInput = (transcript: string) => {
    setInputText(transcript);
    handleSendMessage(transcript);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage(inputText);
    }
  };

  const toggleChatbot = () => {
    setIsOpen(!isOpen);
  };

  const clearChat = () => {
    setMessages([]);
    setMessageCounter(0);
    const welcomeMessage = knowledge.getWelcomeMessage();
    setMessages([{
      id: 'welcome-message',
      text: welcomeMessage,
      isUser: false,
      timestamp: new Date()
    }]);
  };

  return (
    <>
      {/* Chatbot Toggle Button - Positioned above WhatsApp button */}
      <div className="fixed bottom-24 right-4 sm:right-6 z-50">
        <button
          onClick={toggleChatbot}
          className={`w-10 h-10 sm:w-14 sm:h-14 rounded-full shadow-lg transition-all duration-300 flex items-center justify-center hover:scale-110 relative ${
            isOpen
              ? 'bg-red-500 hover:bg-red-600 text-white'
              : 'bg-blue-500 hover:bg-blue-600 text-white animate-pulse'
          }`}
          aria-label={locale === 'en' ? 'Toggle Chatbot' : 'チャットボットを開く'}
        >
          {isOpen ? <X size={16} className="sm:w-6 sm:h-6" /> : <MessageCircle size={16} className="sm:w-6 sm:h-6" />}

          {/* Notification Badge */}
          {!isOpen && (
            <div className="absolute -top-1 -right-1 sm:-top-2 sm:-right-2 w-5 h-5 sm:w-6 sm:h-6 bg-red-500 text-white text-xs rounded-full flex items-center justify-center animate-bounce">
              !
            </div>
          )}
        </button>
      </div>

      {/* Chatbot Window */}
      {isOpen && (
        <div className="fixed bottom-16 sm:bottom-42 right-2 sm:right-6 w-[calc(100vw-16px)] sm:w-80 md:w-96 h-[40vh] sm:h-80 max-w-xs sm:max-w-sm bg-white rounded-xl shadow-2xl border border-neutral-200 z-50 flex flex-col animate-slideInUp chatbot-mobile-fix">
          {/* Header */}
          <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-2 sm:p-4 rounded-t-xl flex items-center justify-between">
            <div className="flex items-center space-x-1 sm:space-x-3 flex-1 min-w-0">
              <div className="w-5 h-5 sm:w-8 sm:h-8 bg-white/20 rounded-full flex items-center justify-center flex-shrink-0">
                <MessageCircle size={12} className="sm:w-4 sm:h-4" />
              </div>
              <div className="min-w-0 flex-1">
                <h3 className="font-semibold text-xs sm:text-sm truncate">
                  {locale === 'en' ? 'EBAM Assistant' : 'EBAMアシスタント'}
                </h3>
                <p className="text-xs text-blue-100 truncate hidden sm:block">
                  {locale === 'en' ? 'Online now' : 'オンライン中'}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-1 flex-shrink-0">
              <button
                onClick={clearChat}
                className="text-white/80 hover:text-white text-xs px-1 py-1 rounded transition-colors hidden sm:block"
                title={locale === 'en' ? 'Clear chat' : 'チャットをクリア'}
              >
                {locale === 'en' ? 'Clear' : 'クリア'}
              </button>
              <button
                onClick={toggleChatbot}
                className="text-white/80 hover:text-white p-1 rounded transition-colors flex items-center justify-center"
                title={locale === 'en' ? 'Close chat' : 'チャットを閉じる'}
              >
                <X size={14} className="sm:w-5 sm:h-5" />
              </button>
            </div>
          </div>

          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-2 sm:p-3 md:p-4 space-y-1 sm:space-y-3">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.isUser ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-[85%] p-2 sm:p-3 rounded-lg text-xs sm:text-sm ${
                    message.isUser
                      ? 'bg-blue-500 text-white rounded-br-none'
                      : 'bg-neutral-100 text-neutral-800 rounded-bl-none'
                  }`}
                >
                  <p className="whitespace-pre-wrap">{message.text}</p>
                  <p className={`text-xs mt-1 ${
                    message.isUser ? 'text-blue-100' : 'text-neutral-500'
                  }`}>
                    {formatDateTime(message.timestamp).split(' ')[1]}
                  </p>
                </div>
              </div>
            ))}

            {/* Typing Indicator */}
            {isTyping && (
              <div className="flex justify-start">
                <div className="bg-neutral-100 text-neutral-800 p-3 rounded-lg rounded-bl-none">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-neutral-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-neutral-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-neutral-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Quick Action Buttons */}
          {messages.length <= 1 && (
            <div className="px-2 sm:px-3 md:px-4 pb-1 sm:pb-2">
              <div className="flex flex-wrap gap-1">
                <button
                  onClick={() => handleSendMessage(locale === 'en' ? 'Show me Toyota cars' : 'トヨタ車を見せて')}
                  className="px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs hover:bg-blue-200 transition-colors"
                >
                  {locale === 'en' ? 'Cars' : '車'}
                </button>
                <button
                  onClick={() => handleSendMessage(locale === 'en' ? 'What are your prices?' : '価格はいくらですか？')}
                  className="px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs hover:bg-green-200 transition-colors"
                >
                  {locale === 'en' ? 'Price' : '価格'}
                </button>
                <button
                  onClick={() => handleSendMessage(locale === 'en' ? 'How does shipping work?' : '配送はどうなりますか？')}
                  className="px-2 py-1 bg-purple-100 text-purple-700 rounded-full text-xs hover:bg-purple-200 transition-colors"
                >
                  {locale === 'en' ? 'Ship' : '配送'}
                </button>
                <button
                  onClick={() => handleSendMessage(locale === 'en' ? 'Contact information' : '連絡先')}
                  className="px-2 py-1 bg-orange-100 text-orange-700 rounded-full text-xs hover:bg-orange-200 transition-colors"
                >
                  {locale === 'en' ? 'Contact' : '連絡先'}
                </button>
              </div>
            </div>
          )}

          {/* Input */}
          <div className="p-2 sm:p-3 md:p-4 border-t border-neutral-200 bg-white rounded-b-xl">
            <div className="flex items-center space-x-1">
              <div className="flex-1 relative min-w-0">
                <input
                  ref={inputRef}
                  type="text"
                  value={inputText}
                  onChange={(e) => setInputText(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder={uiMessages.chatbot?.typeMessage || (locale === 'en' ? 'Type message...' : 'メッセージ...')}
                  className="w-full px-2 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-xs sm:text-sm"
                />
              </div>

              {/* Voice Input */}
              <div className="flex-shrink-0">
                <VoiceRecognition
                  locale={locale}
                  isListening={isListening}
                  setIsListening={setIsListening}
                  onTranscript={handleVoiceInput}
                />
              </div>

              {/* Send Button */}
              <button
                onClick={() => handleSendMessage(inputText)}
                disabled={!inputText.trim()}
                className="bg-blue-500 hover:bg-blue-600 disabled:bg-neutral-300 text-white p-2 rounded-lg transition-colors duration-200 flex-shrink-0"
              >
                <Send size={14} className="sm:w-4 sm:h-4" />
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Lead Capture Form */}
      <LeadCaptureForm
        locale={locale}
        isOpen={showLeadForm}
        onClose={() => setShowLeadForm(false)}
        productInterest={leadProductInterest}
      />
    </>
  );
}
