'use client';

import { useState, useCallback } from 'react';
import Link from 'next/link';
import { Menu, X, Car, Package, Search, Filter } from 'lucide-react';
import FilterAndSearch, { FilterOptions } from './FilterAndSearch';
import StockGrid from './StockGrid';
import EnhancedStockDisplay from './EnhancedStockDisplay';
import ListView from './ListView';

interface StockItem {
  id: number;
  category: string;
  title: string;
  price: string;
  location: string;
  status: string;
  image: string;
  images: string[];
  specs: string[];
  carId: string;
  year?: number;
  mileage?: number;
  fuelType?: string;
  transmission?: string;
  bodyCondition?: string;
  addedDate?: string;
  originalPrice?: string;
  stockQuantity?: number;
  popularity?: number;
}

interface StockWrapperProps {
  items: StockItem[];
  locale: string;
}

export default function StockWrapper({ items, locale }: StockWrapperProps) {
  const [filteredCategory, setFilteredCategory] = useState('cars'); // Default to cars
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedItem, setSelectedItem] = useState<StockItem | null>(null);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [filters, setFilters] = useState<FilterOptions>({
    category: 'cars', // Default to cars
    searchTerm: '',
    priceRange: [0, 1000000],
    yearRange: [2000, 2025],
    mileageRange: [0, 200000],
    fuelTypes: [],
    transmissionTypes: [],
    bodyConditions: [],
  });

  const handleCategoryChange = useCallback((category: string) => {
    setFilteredCategory(category);
  }, []);

  const handleSearchChange = useCallback((search: string) => {
    setSearchTerm(search);
  }, []);

  const handleFiltersChange = useCallback((newFilters: FilterOptions) => {
    setFilters(newFilters);
  }, []);

  // Helper function to extract numeric value from price string
  const extractPrice = (priceStr: string): number => {
    const numericStr = priceStr.replace(/[¥,]/g, '');
    return parseInt(numericStr) || 0;
  };

  // Helper function to extract year from title
  const extractYear = (title: string): number => {
    const yearMatch = title.match(/(\d{4})/);
    return yearMatch ? parseInt(yearMatch[1]) : 2000;
  };

  // Helper function to extract mileage from specs
  const extractMileage = (specs: string[]): number => {
    const mileageSpec = specs.find(spec => spec.includes('k km'));
    if (mileageSpec) {
      const mileageMatch = mileageSpec.match(/(\d+)k km/);
      return mileageMatch ? parseInt(mileageMatch[1]) * 1000 : 0;
    }
    return 0;
  };

  // Helper function to get fuel type from specs
  const getFuelType = (specs: string[]): string => {
    const fuelTypes = ['Gasoline', 'Diesel', 'Hybrid', 'Electric', 'LPG'];
    return specs.find(spec => fuelTypes.includes(spec)) || 'Gasoline';
  };

  // Helper function to get transmission type from specs
  const getTransmissionType = (specs: string[]): string => {
    const transmissionTypes = ['Manual', 'Automatic', 'CVT', 'Semi-Automatic'];
    return specs.find(spec => transmissionTypes.includes(spec)) || 'Automatic';
  };

  // Apply advanced filters to items
  const filteredItems = items.filter(item => {
    // Category filter
    if (filters.category !== 'all' && item.category !== filters.category) {
      return false;
    }

    // Search term filter
    if (filters.searchTerm && !item.title.toLowerCase().includes(filters.searchTerm.toLowerCase())) {
      return false;
    }

    // Only apply advanced filters to cars
    if (item.category === 'cars') {
      // Price filter
      const itemPrice = extractPrice(item.price);
      if (itemPrice < filters.priceRange[0] || itemPrice > filters.priceRange[1]) {
        return false;
      }

      // Year filter
      const itemYear = extractYear(item.title);
      if (itemYear < filters.yearRange[0] || itemYear > filters.yearRange[1]) {
        return false;
      }

      // Mileage filter
      const itemMileage = extractMileage(item.specs);
      if (itemMileage < filters.mileageRange[0] || itemMileage > filters.mileageRange[1]) {
        return false;
      }

      // Fuel type filter
      if (filters.fuelTypes.length > 0) {
        const itemFuelType = getFuelType(item.specs);
        if (!filters.fuelTypes.includes(itemFuelType)) {
          return false;
        }
      }

      // Transmission filter
      if (filters.transmissionTypes.length > 0) {
        const itemTransmission = getTransmissionType(item.specs);
        if (!filters.transmissionTypes.includes(itemTransmission)) {
          return false;
        }
      }

      // Body condition filter (for now, we'll use a random assignment based on item ID)
      if (filters.bodyConditions.length > 0) {
        const conditions = ['Excellent', 'Very Good', 'Good', 'Fair', 'Needs Work'];
        const itemCondition = conditions[item.id % conditions.length];
        if (!filters.bodyConditions.includes(itemCondition)) {
          return false;
        }
      }
    }

    return true;
  });

  // Generate categories with counts based on filtered items
  const categories = [
    { id: 'all', name: locale === 'en' ? 'All Items' : '全商品', count: filteredItems.length },
    { id: 'cars', name: locale === 'en' ? 'Cars' : '車', count: filteredItems.filter(item => item.category === 'cars').length }
  ];

  const handleItemClick = (item: StockItem) => {
    setSelectedItem(item);
  };

  return (
    <div className="flex min-h-screen">
      {/* Mobile Menu Button */}
      <button
        onClick={() => setSidebarOpen(!sidebarOpen)}
        className="lg:hidden fixed top-24 left-4 z-50 bg-white shadow-lg rounded-lg p-3 border"
      >
        {sidebarOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
      </button>

      {/* Sidebar */}
      <div className={`
        fixed lg:static inset-y-0 left-0 z-40 w-80 bg-white border-r border-neutral-200 transform transition-transform duration-300 ease-in-out
        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
      `}>
        <div className="h-full overflow-y-auto pt-20 lg:pt-6">
          {/* Sidebar Header */}
          <div className="px-6 pb-4 border-b border-neutral-200">
            <h2 className="text-xl font-bold text-neutral-800">
              {locale === 'en' ? 'Browse Stock' : '在庫を見る'}
            </h2>
            <p className="text-sm text-neutral-600 mt-1">
              {locale === 'en' ? 'Select a category to view items' : 'カテゴリを選択してアイテムを表示'}
            </p>
          </div>

          {/* Category Menu */}
          <div className="p-6">
            <h3 className="text-sm font-semibold text-neutral-700 uppercase tracking-wide mb-4">
              {locale === 'en' ? 'Categories' : 'カテゴリ'}
            </h3>
            <nav className="space-y-2">
              {[
                { id: 'cars', name: locale === 'en' ? 'Cars' : '車', icon: Car, count: items.filter(item => item.category === 'cars').length },
                { id: 'all', name: locale === 'en' ? 'All Items' : '全商品', icon: Package, count: items.length }
              ].map((category) => {
                const IconComponent = category.icon;
                const isActive = filteredCategory === category.id;
                return (
                  <button
                    key={category.id}
                    onClick={() => {
                      setFilteredCategory(category.id);
                      setFilters(prev => ({ ...prev, category: category.id }));
                      setSidebarOpen(false); // Close sidebar on mobile after selection
                    }}
                    className={`
                      w-full flex items-center justify-between px-4 py-3 rounded-lg text-left transition-all duration-200
                      ${isActive
                        ? 'bg-primary-100 text-primary-700 border border-primary-200'
                        : 'text-neutral-700 hover:bg-neutral-50 hover:text-neutral-900'
                      }
                    `}
                  >
                    <div className="flex items-center space-x-3">
                      <IconComponent className={`w-5 h-5 ${isActive ? 'text-primary-600' : 'text-neutral-500'}`} />
                      <span className="font-medium">{category.name}</span>
                    </div>
                    <span className={`
                      text-xs px-2 py-1 rounded-full
                      ${isActive ? 'bg-primary-200 text-primary-700' : 'bg-neutral-200 text-neutral-600'}
                    `}>
                      {category.count}
                    </span>
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Quick Search */}
          <div className="px-6 pb-6">
            <h3 className="text-sm font-semibold text-neutral-700 uppercase tracking-wide mb-4">
              {locale === 'en' ? 'Quick Search' : 'クイック検索'}
            </h3>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-4 h-4" />
              <input
                type="text"
                placeholder={locale === 'en' ? 'Search items...' : 'アイテムを検索...'}
                value={searchTerm}
                onChange={(e) => {
                  setSearchTerm(e.target.value);
                  setFilters(prev => ({ ...prev, searchTerm: e.target.value }));
                }}
                className="w-full pl-10 pr-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* Advanced Filters for Cars */}
          {filteredCategory === 'cars' && (
            <div className="px-6 pb-6 border-t border-neutral-200 pt-6">
              <h3 className="text-sm font-semibold text-neutral-700 uppercase tracking-wide mb-4 flex items-center">
                <Filter className="w-4 h-4 mr-2" />
                {locale === 'en' ? 'Car Filters' : '車のフィルター'}
              </h3>
              <FilterAndSearch
                locale={locale}
                onCategoryChange={handleCategoryChange}
                onSearchChange={handleSearchChange}
                onFiltersChange={handleFiltersChange}
                categories={categories}
                compact={true}
              />
            </div>
          )}
        </div>
      </div>

      {/* Overlay for mobile */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-30 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Main Content Area */}
      <div className="flex-1 lg:ml-0 flex">
        {/* Left: Main Stock Display */}
        <div className="flex-1 p-6 pt-20 lg:pt-6">
          {/* Header */}
          <div className="mb-6">
            <h1 className="text-2xl lg:text-3xl font-bold text-neutral-800 mb-2">
              {categories.find(cat => cat.id === filteredCategory)?.name || (locale === 'en' ? 'Stock' : '在庫')}
            </h1>
            <p className="text-neutral-600">
              {locale === 'en'
                ? `Showing ${filteredItems.length} items`
                : `${filteredItems.length}件のアイテムを表示中`
              }
            </p>
          </div>

          {/* Stock Display */}
          <EnhancedStockDisplay items={filteredItems} locale={locale} hideSpecialSections={true}>
            {({ displayItems, viewMode }) => (
              viewMode === 'grid' ? (
                <StockGrid
                  items={displayItems}
                  locale={locale}
                  filteredCategory={filteredCategory}
                  searchTerm={searchTerm}
                />
              ) : (
                <ListView
                  items={displayItems}
                  locale={locale}
                  onItemClick={handleItemClick}
                />
              )
            )}
          </EnhancedStockDisplay>
        </div>

        {/* Right: Special Sections */}
        <div className="hidden xl:block w-80 p-6 pt-20 lg:pt-6 border-l border-neutral-200 bg-neutral-50">
          <div className="sticky top-6 space-y-6">
            <h2 className="text-lg font-bold text-neutral-800 mb-4">
              {locale === 'en' ? 'Featured' : '注目'}
            </h2>

            {/* Recently Added Section */}
            {(() => {
              const recentlyAdded = filteredItems.filter(item => {
                const sevenDaysAgo = new Date();
                sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
                return new Date(item.addedDate || 0) > sevenDaysAgo;
              });

              return recentlyAdded.length > 0 && (
                <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                  <div className="flex items-center space-x-2 mb-3">
                    <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                    <h3 className="text-sm font-semibold text-blue-800">
                      {locale === 'en' ? 'Recently Added' : '最近追加'}
                    </h3>
                    <span className="bg-blue-600 text-white text-xs px-2 py-1 rounded-full">
                      {recentlyAdded.length}
                    </span>
                  </div>
                  <p className="text-blue-700 text-xs mb-3">
                    {locale === 'en' ? 'New in the last 7 days' : '過去7日間の新着'}
                  </p>
                  <div className="space-y-2">
                    {recentlyAdded.slice(0, 3).map(item => (
                      <div key={item.id} className="bg-white rounded-lg p-3 border">
                        <div className="font-medium text-sm text-neutral-800 truncate">{item.title}</div>
                        <div className="text-primary-600 font-bold text-sm">{item.price}</div>
                        <div className="text-xs text-neutral-600">
                          {new Date(item.addedDate || '').toLocaleDateString()}
                        </div>
                      </div>
                    ))}
                  </div>
                  {recentlyAdded.length > 3 && (
                    <button className="text-blue-600 text-xs mt-2 hover:text-blue-700">
                      {locale === 'en' ? `View all ${recentlyAdded.length}` : `全${recentlyAdded.length}件を見る`}
                    </button>
                  )}
                </div>
              );
            })()}

            {/* Price Reduced Section */}
            {(() => {
              const priceReduced = filteredItems.filter(item => item.originalPrice);

              return priceReduced.length > 0 && (
                <div className="bg-red-50 rounded-lg p-4 border border-red-200">
                  <div className="flex items-center space-x-2 mb-3">
                    <div className="w-2 h-2 bg-red-600 rounded-full"></div>
                    <h3 className="text-sm font-semibold text-red-800">
                      {locale === 'en' ? 'Price Reduced' : '価格下げ'}
                    </h3>
                    <span className="bg-red-600 text-white text-xs px-2 py-1 rounded-full">
                      {priceReduced.length}
                    </span>
                  </div>
                  <p className="text-red-700 text-xs mb-3">
                    {locale === 'en' ? 'Great deals available' : 'お得な価格'}
                  </p>
                  <div className="space-y-2">
                    {priceReduced.slice(0, 3).map(item => (
                      <div key={item.id} className="bg-white rounded-lg p-3 border">
                        <div className="font-medium text-sm text-neutral-800 truncate">{item.title}</div>
                        <div className="flex items-center space-x-2">
                          <div className="text-primary-600 font-bold text-sm">{item.price}</div>
                          <div className="text-neutral-500 line-through text-xs">{item.originalPrice}</div>
                        </div>
                        <div className="text-xs text-green-600 font-medium">
                          {locale === 'en' ? 'Price Reduced!' : '価格下げ！'}
                        </div>
                      </div>
                    ))}
                  </div>
                  {priceReduced.length > 3 && (
                    <button className="text-red-600 text-xs mt-2 hover:text-red-700">
                      {locale === 'en' ? `View all ${priceReduced.length}` : `全${priceReduced.length}件を見る`}
                    </button>
                  )}
                </div>
              );
            })()}
          </div>
        </div>
      </div>

      {/* Modal for selected item */}
      {selectedItem && (
        <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-bold text-neutral-800">{selectedItem.title}</h2>
                <button
                  onClick={() => setSelectedItem(null)}
                  className="text-neutral-600 hover:text-neutral-800 transition-colors"
                >
                  ×
                </button>
              </div>
              <div className="text-center py-8">
                <p className="text-neutral-600">
                  {locale === 'en' ? 'Quick view modal - implement detailed view here' : 'クイックビューモーダル - 詳細ビューをここに実装'}
                </p>
                <Link
                  href={`/${locale}/stock/${selectedItem.carId}`}
                  className="inline-block mt-4 bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors"
                >
                  {locale === 'en' ? 'View Full Details' : '詳細を見る'}
                </Link>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
