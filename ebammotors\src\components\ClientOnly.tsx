'use client';

import { useEffect, useState } from 'react';

interface ClientOnlyProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

/**
 * ClientOnly component to prevent hydration mismatches
 * Only renders children on the client side after hydration
 */
export default function ClientOnly({ children, fallback = null }: ClientOnlyProps) {
  const [hasMounted, setHasMounted] = useState(false);

  useEffect(() => {
    // Double-check we're on the client side
    if (typeof window !== 'undefined') {
      setHasMounted(true);
    }
  }, []);

  // Ensure we're on the client side before rendering
  if (typeof window === 'undefined' || !hasMounted) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}
