#!/bin/bash

echo "🤖 EBAM Motors AI Chatbot Setup"
echo "================================"
echo ""

echo "This script will set up a free AI chatbot using Ollama (completely free, no API keys needed)"
echo ""

echo "Step 1: Checking if Ollama is installed..."
if ! command -v ollama &> /dev/null; then
    echo "❌ Ollama not found. Installing Ollama..."
    echo ""
    
    # Install Ollama
    curl -fsSL https://ollama.ai/install.sh | sh
    
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install Ollama. Please install manually:"
        echo "   Visit: https://ollama.ai/"
        exit 1
    fi
    
    echo "✅ Ollama installed successfully!"
else
    echo "✅ Ollama is already installed!"
fi

echo ""

echo "Step 2: Starting Ollama service..."
# Start Ollama in background
ollama serve &
sleep 3

echo "Step 3: Downloading AI model (llama3.2:3b - fast and efficient)..."
echo "This may take a few minutes depending on your internet connection..."
ollama pull llama3.2:3b

if [ $? -ne 0 ]; then
    echo "❌ Failed to download model. Please check your internet connection."
    exit 1
fi

echo "✅ Model downloaded successfully!"
echo ""

echo "Step 4: Testing the AI model..."
echo "Testing..." | ollama run llama3.2:3b "Respond with just 'AI is working!' if you can understand this."

echo ""
echo "✅ Setup complete! Your AI chatbot is now ready."
echo ""
echo "🎉 What's configured:"
echo "  • AI Provider: Ollama (local, completely free)"
echo "  • Model: llama3.2:3b (fast and efficient)"
echo "  • No API keys needed"
echo "  • No usage limits"
echo "  • Private (runs locally)"
echo ""
echo "🚀 Next steps:"
echo "  1. Start your development server: npm run dev"
echo "  2. Test the chatbot on your website"
echo "  3. Check AI status in admin dashboard: /admin/crm"
echo ""
echo "💡 The chatbot will now give natural, intelligent responses!"
echo ""

# Make sure Ollama keeps running
echo "Keeping Ollama service running in background..."
echo "If you need to stop it later, run: pkill ollama"
