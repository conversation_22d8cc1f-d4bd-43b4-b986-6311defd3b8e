'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Menu, X, ChevronDown, User, LogIn } from 'lucide-react';
import { getMessagesSync } from '@/lib/messages';
import { useAuth } from '@/contexts/AuthContext';
import AuthModal from './Auth/AuthModal';
import UserProfile from './Auth/UserProfile';

export default function Navigation() {
  const [isOpen, setIsOpen] = useState(false);
  const [isStockDropdownOpen, setIsStockDropdownOpen] = useState(false);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [showUserProfile, setShowUserProfile] = useState(false);
  const pathname = usePathname();
  const { user } = useAuth();

  // Extract locale from pathname
  const locale = pathname.split('/')[1] || 'en';
  const messages = getMessagesSync(locale);

  const navigationItems = [
    { name: messages.navigation.home, href: `/${locale}` },
    { name: messages.navigation.services, href: `/${locale}/services` },
    { name: messages.navigation.inventory, href: `/${locale}/stock` },
    { name: messages.navigation.about, href: `/${locale}/about` },
    { name: messages.navigation.contact, href: `/${locale}/contact` },
  ];

  return (
    <nav className="bg-white shadow-lg fixed top-0 left-0 right-0 z-50 transition-all duration-300">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-2">
              <div className="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-xl">EM</span>
              </div>
              <span className="font-heading font-bold text-xl text-neutral-800">
                EBAM MOTORS
              </span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {navigationItems.map((item) => {
              // Special handling for Stock/Inventory item with dropdown
              if (item.href === `/${locale}/stock`) {
                return (
                  <div
                    key={item.name}
                    className="relative"
                    onMouseEnter={() => setIsStockDropdownOpen(true)}
                    onMouseLeave={() => setIsStockDropdownOpen(false)}
                  >
                    <Link
                      href={item.href}
                      className="text-neutral-600 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors duration-200 flex items-center gap-1"
                    >
                      {item.name}
                      <ChevronDown className="w-4 h-4" />
                    </Link>

                    {/* Dropdown Menu */}
                    {isStockDropdownOpen && (
                      <div className="absolute top-full left-0 mt-1 w-48 bg-white rounded-lg shadow-lg border border-neutral-200 py-2 z-50">
                        <Link
                          href={`/${locale}/stock#filter`}
                          className="block px-4 py-2 text-sm text-neutral-600 hover:text-primary-600 hover:bg-neutral-50 transition-colors duration-200"
                        >
                          {locale === 'en' ? 'Filter by Category' : 'カテゴリーで絞り込み'}
                        </Link>
                        <Link
                          href={`/${locale}/stock#search`}
                          className="block px-4 py-2 text-sm text-neutral-600 hover:text-primary-600 hover:bg-neutral-50 transition-colors duration-200"
                        >
                          {locale === 'en' ? 'Search Stock' : '在庫検索'}
                        </Link>
                      </div>
                    )}
                  </div>
                );
              }

              // Regular navigation items
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className="text-neutral-600 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors duration-200"
                >
                  {item.name}
                </Link>
              );
            })}
            <Link
              href={locale === 'en' ? '/ja' : '/en'}
              className="text-neutral-600 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors duration-200"
            >
              {locale === 'en' ? messages.navigation.japanese : 'English'}
            </Link>

            {/* User Authentication */}
            {user ? (
              <button
                onClick={() => setShowUserProfile(true)}
                className="flex items-center space-x-2 text-neutral-600 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors duration-200"
              >
                <User className="w-4 h-4" />
                <span className="hidden lg:inline">{user.name}</span>
              </button>
            ) : (
              <button
                onClick={() => setShowAuthModal(true)}
                className="flex items-center space-x-2 bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors duration-200"
              >
                <LogIn className="w-4 h-4" />
                <span>{locale === 'en' ? 'Sign In' : 'サインイン'}</span>
              </button>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden flex items-center">
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="text-neutral-600 hover:text-primary-600 p-2"
            >
              {isOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t">
              {navigationItems.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className="text-neutral-600 hover:text-primary-600 block px-3 py-2 text-base font-medium transition-colors duration-200"
                  onClick={() => setIsOpen(false)}
                >
                  {item.name}
                </Link>
              ))}
              <Link
                href={locale === 'en' ? '/ja' : '/en'}
                className="text-neutral-600 hover:text-primary-600 block px-3 py-2 text-base font-medium transition-colors duration-200"
                onClick={() => setIsOpen(false)}
              >
                {locale === 'en' ? messages.navigation.japanese : 'English'}
              </Link>

              {/* Mobile User Authentication */}
              <div className="border-t pt-3 mt-3">
                {user ? (
                  <button
                    onClick={() => {
                      setShowUserProfile(true);
                      setIsOpen(false);
                    }}
                    className="flex items-center space-x-2 text-neutral-600 hover:text-primary-600 px-3 py-2 text-base font-medium transition-colors duration-200 w-full text-left"
                  >
                    <User className="w-5 h-5" />
                    <span>{user.name}</span>
                  </button>
                ) : (
                  <button
                    onClick={() => {
                      setShowAuthModal(true);
                      setIsOpen(false);
                    }}
                    className="flex items-center space-x-2 bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors duration-200 mx-3 justify-center"
                  >
                    <LogIn className="w-4 h-4" />
                    <span>{locale === 'en' ? 'Sign In' : 'サインイン'}</span>
                  </button>
                )}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Authentication Modal */}
      {showAuthModal && (
        <AuthModal
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
          locale={locale}
        />
      )}

      {/* User Profile Modal */}
      {showUserProfile && user && (
        <UserProfile
          locale={locale}
          onClose={() => setShowUserProfile(false)}
        />
      )}
    </nav>
  );
}
