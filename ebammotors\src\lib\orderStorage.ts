import { promises as fs } from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { Order, Invoice, PaymentStatus, ShippingUpdate } from '@/types/payment';

// Check if we're in a serverless environment
const isServerless = process.env.VERCEL || process.env.NETLIFY || process.env.AWS_LAMBDA_FUNCTION_NAME;

// File paths
const DATA_DIR = path.join(process.cwd(), 'data');
const ORDERS_FILE = path.join(DATA_DIR, 'orders.json');
const INVOICES_FILE = path.join(DATA_DIR, 'invoices.json');

// In-memory storage for serverless environments
let ordersMemoryStore: Order[] = [];
let invoicesMemoryStore: Invoice[] = [];

/**
 * Get storage type for logging/debugging
 */
export function getStorageType(): string {
  return isServerless ? 'memory' : 'file';
}

/**
 * Ensure data directory exists
 */
async function ensureDataDirectory(): Promise<void> {
  if (isServerless) return;
  
  try {
    await fs.access(DATA_DIR);
  } catch {
    await fs.mkdir(DATA_DIR, { recursive: true });
  }
}

/**
 * Load orders from storage
 */
async function loadOrders(): Promise<Order[]> {
  if (isServerless) {
    return ordersMemoryStore;
  }

  try {
    await ensureDataDirectory();
    const data = await fs.readFile(ORDERS_FILE, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    // File doesn't exist or is invalid, return empty array
    return [];
  }
}

/**
 * Save orders to storage
 */
async function saveOrders(orders: Order[]): Promise<void> {
  if (isServerless) {
    ordersMemoryStore = orders;
    return;
  }

  await ensureDataDirectory();
  await fs.writeFile(ORDERS_FILE, JSON.stringify(orders, null, 2));
}

/**
 * Load invoices from storage
 */
async function loadInvoices(): Promise<Invoice[]> {
  if (isServerless) {
    return invoicesMemoryStore;
  }

  try {
    await ensureDataDirectory();
    const data = await fs.readFile(INVOICES_FILE, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    return [];
  }
}

/**
 * Save invoices to storage
 */
async function saveInvoices(invoices: Invoice[]): Promise<void> {
  if (isServerless) {
    invoicesMemoryStore = invoices;
    return;
  }

  await ensureDataDirectory();
  await fs.writeFile(INVOICES_FILE, JSON.stringify(invoices, null, 2));
}

/**
 * Generate order number
 */
function generateOrderNumber(): string {
  const timestamp = Date.now().toString();
  const random = Math.random().toString(36).substr(2, 4).toUpperCase();
  return `EB${timestamp.slice(-6)}${random}`;
}

/**
 * Generate invoice number
 */
function generateInvoiceNumber(): string {
  const timestamp = Date.now().toString();
  const random = Math.random().toString(36).substr(2, 3).toUpperCase();
  return `INV-${timestamp.slice(-6)}-${random}`;
}

/**
 * Create a new order
 */
export async function createOrder(orderData: Omit<Order, 'id' | 'orderNumber' | 'createdAt' | 'updatedAt'>): Promise<Order> {
  const orders = await loadOrders();
  
  const newOrder: Order = {
    ...orderData,
    id: uuidv4(),
    orderNumber: generateOrderNumber(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  orders.push(newOrder);
  await saveOrders(orders);
  
  return newOrder;
}

/**
 * Get all orders
 */
export async function getAllOrders(): Promise<Order[]> {
  return await loadOrders();
}

/**
 * Get order by ID
 */
export async function getOrderById(orderId: string): Promise<Order | null> {
  const orders = await loadOrders();
  return orders.find(order => order.id === orderId) || null;
}

/**
 * Get orders by customer ID
 */
export async function getOrdersByCustomerId(customerId: string): Promise<Order[]> {
  const orders = await loadOrders();
  return orders.filter(order => order.customerId === customerId);
}

/**
 * Update order
 */
export async function updateOrder(orderId: string, updates: Partial<Order>): Promise<boolean> {
  const orders = await loadOrders();
  const orderIndex = orders.findIndex(order => order.id === orderId);
  
  if (orderIndex === -1) {
    return false;
  }

  orders[orderIndex] = {
    ...orders[orderIndex],
    ...updates,
    updatedAt: new Date().toISOString(),
  };

  await saveOrders(orders);
  return true;
}

/**
 * Update order status
 */
export async function updateOrderStatus(orderId: string, status: Order['status']): Promise<boolean> {
  return await updateOrder(orderId, { status });
}

/**
 * Update payment status
 */
export async function updatePaymentStatus(orderId: string, paymentStatus: Partial<Order['payment']>): Promise<boolean> {
  const order = await getOrderById(orderId);
  if (!order) return false;

  const updatedPayment = {
    ...order.payment,
    ...paymentStatus,
  };

  return await updateOrder(orderId, { payment: updatedPayment });
}

/**
 * Add shipping update
 */
export async function addShippingUpdate(orderId: string, update: Omit<ShippingUpdate, 'id'>): Promise<boolean> {
  const order = await getOrderById(orderId);
  if (!order) return false;

  const newUpdate: ShippingUpdate = {
    ...update,
    id: uuidv4(),
  };

  const updatedShipping = {
    ...order.shipping,
    updates: [...order.shipping.updates, newUpdate],
  };

  return await updateOrder(orderId, { shipping: updatedShipping });
}

/**
 * Create invoice
 */
export async function createInvoice(invoiceData: Omit<Invoice, 'id' | 'invoiceNumber'>): Promise<Invoice> {
  const invoices = await loadInvoices();
  
  const newInvoice: Invoice = {
    ...invoiceData,
    id: uuidv4(),
    invoiceNumber: generateInvoiceNumber(),
  };

  invoices.push(newInvoice);
  await saveInvoices(invoices);
  
  return newInvoice;
}

/**
 * Get invoice by order ID
 */
export async function getInvoiceByOrderId(orderId: string): Promise<Invoice | null> {
  const invoices = await loadInvoices();
  return invoices.find(invoice => invoice.orderId === orderId) || null;
}

/**
 * Update invoice
 */
export async function updateInvoice(invoiceId: string, updates: Partial<Invoice>): Promise<boolean> {
  const invoices = await loadInvoices();
  const invoiceIndex = invoices.findIndex(invoice => invoice.id === invoiceId);
  
  if (invoiceIndex === -1) {
    return false;
  }

  invoices[invoiceIndex] = {
    ...invoices[invoiceIndex],
    ...updates,
  };

  await saveInvoices(invoices);
  return true;
}
