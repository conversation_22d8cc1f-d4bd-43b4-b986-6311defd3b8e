'use client';

// FAQ Section with chatbot integration for enhanced user experience
import { HelpCircle } from 'lucide-react';

// FAQ functionality redirects users to chatbot for better assistance

export default function FAQSection() {
  return (
    <section className="py-16 bg-white">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center space-y-6">
          <div className="inline-flex items-center space-x-2 bg-primary-100 text-primary-700 rounded-full px-4 py-2 text-sm font-semibold">
            <HelpCircle className="w-4 h-4" />
            <span>Get Instant Help</span>
          </div>
          <h2 className="text-3xl lg:text-4xl font-heading font-bold text-neutral-800 leading-tight">
            Need Help? Chat With Us!
          </h2>
          <p className="text-lg text-neutral-600 max-w-2xl mx-auto">
            Our intelligent chatbot is available 24/7 to answer all your questions instantly
          </p>
        </div>

        {/* Chatbot CTA */}
        <div className="text-center mt-8">
          <div className="bg-gradient-to-r from-primary-50 to-secondary-50 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-neutral-800 mb-3">
              Use Our Chatbot!
            </h3>
            <p className="text-neutral-600 mb-4 max-w-xl mx-auto">
              Look for the <strong>chat icon in the bottom-right corner</strong> above the WhatsApp button. Available 24/7 in English and Japanese!
            </p>
            <div className="bg-white/80 rounded-xl p-3 border-l-4 border-primary-500">
              <p className="text-neutral-700 text-sm">
                💬 Ask about vehicles, pricing, shipping<br/>
                🌍 English and Japanese support<br/>
                ⚡ Instant responses anytime
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
